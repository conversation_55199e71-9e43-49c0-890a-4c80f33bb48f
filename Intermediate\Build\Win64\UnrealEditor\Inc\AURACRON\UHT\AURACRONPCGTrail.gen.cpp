// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGTrail.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGTrail() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGTrail();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGTrail_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONTrailType();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_ACharacter_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UBoxComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGSettings_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Class AAURACRONPCGTrail Function AddControlPoint *******************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics
{
	struct AURACRONPCGTrail_eventAddControlPoint_Parms
	{
		FVector ControlPoint;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Adicionar um ponto de controle \xc3\xa0 trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Adicionar um ponto de controle \xc3\xa0 trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControlPoint_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ControlPoint;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::NewProp_ControlPoint = { "ControlPoint", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventAddControlPoint_Parms, ControlPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControlPoint_MetaData), NewProp_ControlPoint_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::NewProp_ControlPoint,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "AddControlPoint", Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::AURACRONPCGTrail_eventAddControlPoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::AURACRONPCGTrail_eventAddControlPoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execAddControlPoint)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_ControlPoint);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddControlPoint(Z_Param_Out_ControlPoint);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function AddControlPoint *********************************

// ********** Begin Class AAURACRONPCGTrail Function ApplyTrailEffectsToPlayer *********************
struct Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics
{
	struct AURACRONPCGTrail_eventApplyTrailEffectsToPlayer_Parms
	{
		ACharacter* Player;
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Aplicar efeitos ao jogador baseado no tipo de trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Aplicar efeitos ao jogador baseado no tipo de trilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventApplyTrailEffectsToPlayer_Parms, Player), Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventApplyTrailEffectsToPlayer_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "ApplyTrailEffectsToPlayer", Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::AURACRONPCGTrail_eventApplyTrailEffectsToPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::AURACRONPCGTrail_eventApplyTrailEffectsToPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execApplyTrailEffectsToPlayer)
{
	P_GET_OBJECT(ACharacter,Z_Param_Player);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyTrailEffectsToPlayer(Z_Param_Player,Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function ApplyTrailEffectsToPlayer ***********************

// ********** Begin Class AAURACRONPCGTrail Function ClearControlPoints ****************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_ClearControlPoints_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Limpar todos os pontos de controle\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpar todos os pontos de controle" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_ClearControlPoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "ClearControlPoints", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_ClearControlPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_ClearControlPoints_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_ClearControlPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_ClearControlPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execClearControlPoints)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearControlPoints();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function ClearControlPoints ******************************

// ********** Begin Class AAURACRONPCGTrail Function GenerateTrail *********************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_GenerateTrail_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Gerar a trilha procedural\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar a trilha procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_GenerateTrail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "GenerateTrail", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GenerateTrail_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_GenerateTrail_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_GenerateTrail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_GenerateTrail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execGenerateTrail)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateTrail();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function GenerateTrail ***********************************

// ********** Begin Class AAURACRONPCGTrail Function GetPlayerPositionAlongTrail *******************
struct Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics
{
	struct AURACRONPCGTrail_eventGetPlayerPositionAlongTrail_Parms
	{
		ACharacter* Player;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Obter a posi\xc3\xa7\xc3\xa3o relativa do jogador na trilha (0.0 = in\xc3\xad""cio, 1.0 = fim)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter a posi\xc3\xa7\xc3\xa3o relativa do jogador na trilha (0.0 = in\xc3\xad""cio, 1.0 = fim)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventGetPlayerPositionAlongTrail_Parms, Player), Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventGetPlayerPositionAlongTrail_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "GetPlayerPositionAlongTrail", Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::AURACRONPCGTrail_eventGetPlayerPositionAlongTrail_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::AURACRONPCGTrail_eventGetPlayerPositionAlongTrail_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execGetPlayerPositionAlongTrail)
{
	P_GET_OBJECT(ACharacter,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetPlayerPositionAlongTrail(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function GetPlayerPositionAlongTrail *********************

// ********** Begin Class AAURACRONPCGTrail Function GetTrailType **********************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics
{
	struct AURACRONPCGTrail_eventGetTrailType_Parms
	{
		EAURACRONTrailType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Obter o tipo de trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter o tipo de trilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventGetTrailType_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONTrailType, METADATA_PARAMS(0, nullptr) }; // 321700334
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "GetTrailType", Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::AURACRONPCGTrail_eventGetTrailType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::AURACRONPCGTrail_eventGetTrailType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execGetTrailType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONTrailType*)Z_Param__Result=P_THIS->GetTrailType();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function GetTrailType ************************************

// ********** Begin Class AAURACRONPCGTrail Function HandlePlayerEndOverlap ************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics
{
	struct AURACRONPCGTrail_eventHandlePlayerEndOverlap_Parms
	{
		AActor* OverlappedActor;
		AActor* OtherActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::NewProp_OverlappedActor = { "OverlappedActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventHandlePlayerEndOverlap_Parms, OverlappedActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventHandlePlayerEndOverlap_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::NewProp_OverlappedActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::NewProp_OtherActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "HandlePlayerEndOverlap", Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::AURACRONPCGTrail_eventHandlePlayerEndOverlap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::AURACRONPCGTrail_eventHandlePlayerEndOverlap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execHandlePlayerEndOverlap)
{
	P_GET_OBJECT(AActor,Z_Param_OverlappedActor);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HandlePlayerEndOverlap(Z_Param_OverlappedActor,Z_Param_OtherActor);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function HandlePlayerEndOverlap **************************

// ********** Begin Class AAURACRONPCGTrail Function HandlePlayerOverlap ***************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics
{
	struct AURACRONPCGTrail_eventHandlePlayerOverlap_Parms
	{
		AActor* OverlappedActor;
		AActor* OtherActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Eventos de intera\xc3\xa7\xc3\xa3o com jogadores\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Eventos de intera\xc3\xa7\xc3\xa3o com jogadores" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappedActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OtherActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_OverlappedActor = { "OverlappedActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventHandlePlayerOverlap_Parms, OverlappedActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_OtherActor = { "OtherActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventHandlePlayerOverlap_Parms, OtherActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_OverlappedActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::NewProp_OtherActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "HandlePlayerOverlap", Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::AURACRONPCGTrail_eventHandlePlayerOverlap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::AURACRONPCGTrail_eventHandlePlayerOverlap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execHandlePlayerOverlap)
{
	P_GET_OBJECT(AActor,Z_Param_OverlappedActor);
	P_GET_OBJECT(AActor,Z_Param_OtherActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HandlePlayerOverlap(Z_Param_OverlappedActor,Z_Param_OtherActor);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function HandlePlayerOverlap *****************************

// ********** Begin Class AAURACRONPCGTrail Function IsPlayerInTrail *******************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics
{
	struct AURACRONPCGTrail_eventIsPlayerInTrail_Parms
	{
		ACharacter* Player;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Verificar se um jogador est\xc3\xa1 dentro da trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se um jogador est\xc3\xa1 dentro da trilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventIsPlayerInTrail_Parms, Player), Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGTrail_eventIsPlayerInTrail_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGTrail_eventIsPlayerInTrail_Parms), &Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "IsPlayerInTrail", Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::AURACRONPCGTrail_eventIsPlayerInTrail_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::AURACRONPCGTrail_eventIsPlayerInTrail_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execIsPlayerInTrail)
{
	P_GET_OBJECT(ACharacter,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPlayerInTrail(Z_Param_Player);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function IsPlayerInTrail *********************************

// ********** Begin Class AAURACRONPCGTrail Function SetActivityScale ******************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics
{
	struct AURACRONPCGTrail_eventSetActivityScale_Parms
	{
		float Scale;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir a escala de atividade da trilha (0.0 = preview, 1.0 = totalmente ativo)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir a escala de atividade da trilha (0.0 = preview, 1.0 = totalmente ativo)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventSetActivityScale_Parms, Scale), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::NewProp_Scale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "SetActivityScale", Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::AURACRONPCGTrail_eventSetActivityScale_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::AURACRONPCGTrail_eventSetActivityScale_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execSetActivityScale)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Scale);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetActivityScale(Z_Param_Scale);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function SetActivityScale ********************************

// ********** Begin Class AAURACRONPCGTrail Function SetTrailEndpoints *****************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics
{
	struct AURACRONPCGTrail_eventSetTrailEndpoints_Parms
	{
		FVector StartPoint;
		FVector EndPoint;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir os pontos de in\xc3\xad""cio e fim da trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir os pontos de in\xc3\xad""cio e fim da trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartPoint_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndPoint_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartPoint;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndPoint;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::NewProp_StartPoint = { "StartPoint", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventSetTrailEndpoints_Parms, StartPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartPoint_MetaData), NewProp_StartPoint_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::NewProp_EndPoint = { "EndPoint", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventSetTrailEndpoints_Parms, EndPoint), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndPoint_MetaData), NewProp_EndPoint_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::NewProp_StartPoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::NewProp_EndPoint,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "SetTrailEndpoints", Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::AURACRONPCGTrail_eventSetTrailEndpoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::AURACRONPCGTrail_eventSetTrailEndpoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execSetTrailEndpoints)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartPoint);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndPoint);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTrailEndpoints(Z_Param_Out_StartPoint,Z_Param_Out_EndPoint);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function SetTrailEndpoints *******************************

// ********** Begin Class AAURACRONPCGTrail Function SetTrailType **********************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics
{
	struct AURACRONPCGTrail_eventSetTrailType_Parms
	{
		EAURACRONTrailType NewType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configurar o tipo de trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar o tipo de trilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::NewProp_NewType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::NewProp_NewType = { "NewType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventSetTrailType_Parms, NewType), Z_Construct_UEnum_AURACRON_EAURACRONTrailType, METADATA_PARAMS(0, nullptr) }; // 321700334
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::NewProp_NewType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::NewProp_NewType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "SetTrailType", Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::AURACRONPCGTrail_eventSetTrailType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::AURACRONPCGTrail_eventSetTrailType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execSetTrailType)
{
	P_GET_ENUM(EAURACRONTrailType,Z_Param_NewType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTrailType(EAURACRONTrailType(Z_Param_NewType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function SetTrailType ************************************

// ********** Begin Class AAURACRONPCGTrail Function SetTrailVisibility ****************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics
{
	struct AURACRONPCGTrail_eventSetTrailVisibility_Parms
	{
		bool bVisible;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir a visibilidade da trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir a visibilidade da trilha" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::NewProp_bVisible_SetBit(void* Obj)
{
	((AURACRONPCGTrail_eventSetTrailVisibility_Parms*)Obj)->bVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGTrail_eventSetTrailVisibility_Parms), &Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::NewProp_bVisible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "SetTrailVisibility", Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::AURACRONPCGTrail_eventSetTrailVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::AURACRONPCGTrail_eventSetTrailVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execSetTrailVisibility)
{
	P_GET_UBOOL(Z_Param_bVisible);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTrailVisibility(Z_Param_bVisible);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function SetTrailVisibility ******************************

// ********** Begin Class AAURACRONPCGTrail Function UpdateForMapPhase *****************************
struct Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics
{
	struct AURACRONPCGTrail_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atualizar a trilha com base na fase do mapa\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar a trilha com base na fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGTrail_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 3530596558
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGTrail, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::AURACRONPCGTrail_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::AURACRONPCGTrail_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGTrail::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGTrail Function UpdateForMapPhase *******************************

// ********** Begin Class AAURACRONPCGTrail ********************************************************
void AAURACRONPCGTrail::StaticRegisterNativesAAURACRONPCGTrail()
{
	UClass* Class = AAURACRONPCGTrail::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddControlPoint", &AAURACRONPCGTrail::execAddControlPoint },
		{ "ApplyTrailEffectsToPlayer", &AAURACRONPCGTrail::execApplyTrailEffectsToPlayer },
		{ "ClearControlPoints", &AAURACRONPCGTrail::execClearControlPoints },
		{ "GenerateTrail", &AAURACRONPCGTrail::execGenerateTrail },
		{ "GetPlayerPositionAlongTrail", &AAURACRONPCGTrail::execGetPlayerPositionAlongTrail },
		{ "GetTrailType", &AAURACRONPCGTrail::execGetTrailType },
		{ "HandlePlayerEndOverlap", &AAURACRONPCGTrail::execHandlePlayerEndOverlap },
		{ "HandlePlayerOverlap", &AAURACRONPCGTrail::execHandlePlayerOverlap },
		{ "IsPlayerInTrail", &AAURACRONPCGTrail::execIsPlayerInTrail },
		{ "SetActivityScale", &AAURACRONPCGTrail::execSetActivityScale },
		{ "SetTrailEndpoints", &AAURACRONPCGTrail::execSetTrailEndpoints },
		{ "SetTrailType", &AAURACRONPCGTrail::execSetTrailType },
		{ "SetTrailVisibility", &AAURACRONPCGTrail::execSetTrailVisibility },
		{ "UpdateForMapPhase", &AAURACRONPCGTrail::execUpdateForMapPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGTrail;
UClass* AAURACRONPCGTrail::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGTrail;
	if (!Z_Registration_Info_UClass_AAURACRONPCGTrail.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGTrail"),
			Z_Registration_Info_UClass_AAURACRONPCGTrail.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGTrail,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGTrail.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGTrail_NoRegister()
{
	return AAURACRONPCGTrail::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGTrail_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Ator para gerenciar uma trilha din\xc3\xa2mica espec\xc3\xad""fica no AURACRON\n * Cada tipo de trilha (Prismal Flow, Ethereal Path, Nexus Connection) ter\xc3\xa1 sua pr\xc3\xb3pria inst\xc3\xa2ncia\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGTrail.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator para gerenciar uma trilha din\xc3\xa2mica espec\xc3\xad""fica no AURACRON\nCada tipo de trilha (Prismal Flow, Ethereal Path, Nexus Connection) ter\xc3\xa1 sua pr\xc3\xb3pria inst\xc3\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponent_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente PCG principal para gera\xc3\xa7\xc3\xa3o da trilha\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente PCG principal para gera\xc3\xa7\xc3\xa3o da trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineComponent_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente Spline para definir o caminho da trilha\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente Spline para definir o caminho da trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrailSettings_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es PCG para esta trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es PCG para esta trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowIntensity_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Caracter\xc3\xadsticas espec\xc3\xad""ficas da trilha\n// Prismal Flow\n" },
#endif
		{ "EditCondition", "TrailType == EAURACRONTrailType::PrismalFlow" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Caracter\xc3\xadsticas espec\xc3\xad""ficas da trilha\nPrismal Flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowWidth_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::PrismalFlow" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpeed_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::PrismalFlow" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasFlowObstacles_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::PrismalFlow" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathVisibility_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Ethereal Path\n" },
#endif
		{ "EditCondition", "TrailType == EAURACRONTrailType::EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ethereal Path" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathWidth_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PathFluctuation_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasPathGuides_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionStrength_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Nexus Connection\n" },
#endif
		{ "EditCondition", "TrailType == EAURACRONTrailType::NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nexus Connection" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionWidth_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionPulseRate_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasConnectionNodes_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
		{ "EditCondition", "TrailType == EAURACRONTrailType::NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionVolume_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente de colis\xc3\xa3o para intera\xc3\xa7\xc3\xa3o com jogadores\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de colis\xc3\xa3o para intera\xc3\xa7\xc3\xa3o com jogadores" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrailType_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tipo de trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivityScale_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Escala de atividade atual (0.0 = preview, 1.0 = totalmente ativo)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala de atividade atual (0.0 = preview, 1.0 = totalmente ativo)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeSinceLastUpdate_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tempo decorrido desde a \xc3\xbaltima atualiza\xc3\xa7\xc3\xa3o da trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo decorrido desde a \xc3\xbaltima atualiza\xc3\xa7\xc3\xa3o da trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdateInterval_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Intervalo de atualiza\xc3\xa7\xc3\xa3o da trilha em segundos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de atualiza\xc3\xa7\xc3\xa3o da trilha em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartLocation_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Armazenar os pontos de in\xc3\xad""cio e fim da trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Armazenar os pontos de in\xc3\xad""cio e fim da trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndLocation_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultFlowWidth_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Valores padr\xc3\xa3o para as propriedades da trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valores padr\xc3\xa3o para as propriedades da trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultFlowSpeed_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultFlowIntensity_MetaData[] = {
		{ "Category", "AURACRON|PCG|PrismalFlow" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultPathWidth_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultPathVisibility_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultPathFluctuation_MetaData[] = {
		{ "Category", "AURACRON|PCG|EtherealPath" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultConnectionWidth_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultConnectionStrength_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultConnectionPulseRate_MetaData[] = {
		{ "Category", "AURACRON|PCG|NexusConnection" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverlappingPlayers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Lista de jogadores atualmente na trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lista de jogadores atualmente na trilha" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NumOverlappingPlayers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Contador de jogadores na trilha\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGTrail.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Contador de jogadores na trilha" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SplineComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TrailSettings;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowSpeed;
	static void NewProp_bHasFlowObstacles_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasFlowObstacles;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PathVisibility;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PathWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PathFluctuation;
	static void NewProp_bHasPathGuides_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasPathGuides;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConnectionStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConnectionWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConnectionPulseRate;
	static void NewProp_bHasConnectionNodes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasConnectionNodes;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractionVolume;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TrailType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TrailType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivityScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeSinceLastUpdate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UpdateInterval;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultFlowWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultFlowSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultFlowIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultPathWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultPathVisibility;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultPathFluctuation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultConnectionWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultConnectionStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultConnectionPulseRate;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OverlappingPlayers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_OverlappingPlayers;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumOverlappingPlayers;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_AddControlPoint, "AddControlPoint" }, // 3025825409
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_ApplyTrailEffectsToPlayer, "ApplyTrailEffectsToPlayer" }, // 4089865556
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_ClearControlPoints, "ClearControlPoints" }, // 1509989518
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_GenerateTrail, "GenerateTrail" }, // 2019022312
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_GetPlayerPositionAlongTrail, "GetPlayerPositionAlongTrail" }, // 3130572048
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_GetTrailType, "GetTrailType" }, // 1959267595
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerEndOverlap, "HandlePlayerEndOverlap" }, // 285582431
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_HandlePlayerOverlap, "HandlePlayerOverlap" }, // 3751224785
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_IsPlayerInTrail, "IsPlayerInTrail" }, // 1089466054
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_SetActivityScale, "SetActivityScale" }, // 1026238632
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailEndpoints, "SetTrailEndpoints" }, // 3002389301
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailType, "SetTrailType" }, // 1212200692
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_SetTrailVisibility, "SetTrailVisibility" }, // 2665716046
		{ &Z_Construct_UFunction_AAURACRONPCGTrail_UpdateForMapPhase, "UpdateForMapPhase" }, // 795017983
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGTrail>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PCGComponent = { "PCGComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, PCGComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponent_MetaData), NewProp_PCGComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_SplineComponent = { "SplineComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, SplineComponent), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineComponent_MetaData), NewProp_SplineComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_TrailSettings = { "TrailSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, TrailSettings), Z_Construct_UClass_UPCGSettings_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrailSettings_MetaData), NewProp_TrailSettings_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_FlowIntensity = { "FlowIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, FlowIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowIntensity_MetaData), NewProp_FlowIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_FlowWidth = { "FlowWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, FlowWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowWidth_MetaData), NewProp_FlowWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_FlowSpeed = { "FlowSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, FlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpeed_MetaData), NewProp_FlowSpeed_MetaData) };
void Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasFlowObstacles_SetBit(void* Obj)
{
	((AAURACRONPCGTrail*)Obj)->bHasFlowObstacles = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasFlowObstacles = { "bHasFlowObstacles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGTrail), &Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasFlowObstacles_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasFlowObstacles_MetaData), NewProp_bHasFlowObstacles_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathVisibility = { "PathVisibility", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, PathVisibility), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathVisibility_MetaData), NewProp_PathVisibility_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathWidth = { "PathWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, PathWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathWidth_MetaData), NewProp_PathWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathFluctuation = { "PathFluctuation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, PathFluctuation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PathFluctuation_MetaData), NewProp_PathFluctuation_MetaData) };
void Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasPathGuides_SetBit(void* Obj)
{
	((AAURACRONPCGTrail*)Obj)->bHasPathGuides = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasPathGuides = { "bHasPathGuides", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGTrail), &Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasPathGuides_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasPathGuides_MetaData), NewProp_bHasPathGuides_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ConnectionStrength = { "ConnectionStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, ConnectionStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionStrength_MetaData), NewProp_ConnectionStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ConnectionWidth = { "ConnectionWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, ConnectionWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionWidth_MetaData), NewProp_ConnectionWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ConnectionPulseRate = { "ConnectionPulseRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, ConnectionPulseRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionPulseRate_MetaData), NewProp_ConnectionPulseRate_MetaData) };
void Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasConnectionNodes_SetBit(void* Obj)
{
	((AAURACRONPCGTrail*)Obj)->bHasConnectionNodes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasConnectionNodes = { "bHasConnectionNodes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGTrail), &Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasConnectionNodes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasConnectionNodes_MetaData), NewProp_bHasConnectionNodes_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_InteractionVolume = { "InteractionVolume", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, InteractionVolume), Z_Construct_UClass_UBoxComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionVolume_MetaData), NewProp_InteractionVolume_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_TrailType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_TrailType = { "TrailType", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, TrailType), Z_Construct_UEnum_AURACRON_EAURACRONTrailType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrailType_MetaData), NewProp_TrailType_MetaData) }; // 321700334
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ActivityScale = { "ActivityScale", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, ActivityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivityScale_MetaData), NewProp_ActivityScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_TimeSinceLastUpdate = { "TimeSinceLastUpdate", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, TimeSinceLastUpdate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeSinceLastUpdate_MetaData), NewProp_TimeSinceLastUpdate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_UpdateInterval = { "UpdateInterval", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, UpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdateInterval_MetaData), NewProp_UpdateInterval_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_StartLocation = { "StartLocation", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, StartLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartLocation_MetaData), NewProp_StartLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_EndLocation = { "EndLocation", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, EndLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndLocation_MetaData), NewProp_EndLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultFlowWidth = { "DefaultFlowWidth", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultFlowWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultFlowWidth_MetaData), NewProp_DefaultFlowWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultFlowSpeed = { "DefaultFlowSpeed", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultFlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultFlowSpeed_MetaData), NewProp_DefaultFlowSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultFlowIntensity = { "DefaultFlowIntensity", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultFlowIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultFlowIntensity_MetaData), NewProp_DefaultFlowIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultPathWidth = { "DefaultPathWidth", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultPathWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultPathWidth_MetaData), NewProp_DefaultPathWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultPathVisibility = { "DefaultPathVisibility", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultPathVisibility), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultPathVisibility_MetaData), NewProp_DefaultPathVisibility_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultPathFluctuation = { "DefaultPathFluctuation", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultPathFluctuation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultPathFluctuation_MetaData), NewProp_DefaultPathFluctuation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultConnectionWidth = { "DefaultConnectionWidth", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultConnectionWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultConnectionWidth_MetaData), NewProp_DefaultConnectionWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultConnectionStrength = { "DefaultConnectionStrength", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultConnectionStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultConnectionStrength_MetaData), NewProp_DefaultConnectionStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultConnectionPulseRate = { "DefaultConnectionPulseRate", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, DefaultConnectionPulseRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultConnectionPulseRate_MetaData), NewProp_DefaultConnectionPulseRate_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OverlappingPlayers_Inner = { "OverlappingPlayers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ACharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OverlappingPlayers = { "OverlappingPlayers", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, OverlappingPlayers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverlappingPlayers_MetaData), NewProp_OverlappingPlayers_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_NumOverlappingPlayers = { "NumOverlappingPlayers", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGTrail, NumOverlappingPlayers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NumOverlappingPlayers_MetaData), NewProp_NumOverlappingPlayers_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGTrail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PCGComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_SplineComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_TrailSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_FlowIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_FlowWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_FlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasFlowObstacles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathVisibility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_PathFluctuation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasPathGuides,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ConnectionStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ConnectionWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ConnectionPulseRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_bHasConnectionNodes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_InteractionVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_TrailType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_TrailType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_ActivityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_TimeSinceLastUpdate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_UpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_StartLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_EndLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultFlowWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultFlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultFlowIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultPathWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultPathVisibility,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultPathFluctuation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultConnectionWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultConnectionStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_DefaultConnectionPulseRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OverlappingPlayers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_OverlappingPlayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGTrail_Statics::NewProp_NumOverlappingPlayers,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGTrail_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGTrail_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGTrail_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGTrail_Statics::ClassParams = {
	&AAURACRONPCGTrail::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGTrail_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGTrail_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGTrail_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGTrail_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGTrail()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGTrail.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGTrail.OuterSingleton, Z_Construct_UClass_AAURACRONPCGTrail_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGTrail.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGTrail);
AAURACRONPCGTrail::~AAURACRONPCGTrail() {}
// ********** End Class AAURACRONPCGTrail **********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h__Script_AURACRON_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGTrail, AAURACRONPCGTrail::StaticClass, TEXT("AAURACRONPCGTrail"), &Z_Registration_Info_UClass_AAURACRONPCGTrail, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGTrail), 4032777138U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h__Script_AURACRON_3335454531(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
