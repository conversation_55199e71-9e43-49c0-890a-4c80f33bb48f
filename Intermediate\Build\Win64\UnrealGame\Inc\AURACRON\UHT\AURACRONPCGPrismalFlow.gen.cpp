// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGPrismalFlow.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGPrismalFlow() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FPrismalFlowSegment();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FPrismalFlowSegment ***********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FPrismalFlowSegment;
class UScriptStruct* FPrismalFlowSegment::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPrismalFlowSegment, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("PrismalFlowSegment"));
	}
	return Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para definir um segmento do Prismal Flow\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para definir um segmento do Prismal Flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "Category", "PrismalFlowSegment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o central do segmento */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o central do segmento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Width_MetaData[] = {
		{ "Category", "PrismalFlowSegment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Largura do flow neste segmento */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Largura do flow neste segmento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Intensity_MetaData[] = {
		{ "Category", "PrismalFlowSegment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade do flow neste segmento */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade do flow neste segmento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpeed_MetaData[] = {
		{ "Category", "PrismalFlowSegment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade do flow neste segmento */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade do flow neste segmento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentType_MetaData[] = {
		{ "Category", "PrismalFlowSegment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo de ambiente que este segmento atravessa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de ambiente que este segmento atravessa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlowSpeed;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPrismalFlowSegment>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, Width), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Width_MetaData), NewProp_Width_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, Intensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Intensity_MetaData), NewProp_Intensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_FlowSpeed = { "FlowSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, FlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpeed_MetaData), NewProp_FlowSpeed_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrismalFlowSegment, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentType_MetaData), NewProp_EnvironmentType_MetaData) }; // 2415364844
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_FlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewProp_EnvironmentType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"PrismalFlowSegment",
	Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::PropPointers),
	sizeof(FPrismalFlowSegment),
	alignof(FPrismalFlowSegment),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPrismalFlowSegment()
{
	if (!Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.InnerSingleton, Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FPrismalFlowSegment.InnerSingleton;
}
// ********** End ScriptStruct FPrismalFlowSegment *************************************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GeneratePrismalFlow *********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GeneratePrismalFlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar o Prismal Flow completo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar o Prismal Flow completo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GeneratePrismalFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GeneratePrismalFlow", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GeneratePrismalFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GeneratePrismalFlow_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GeneratePrismalFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GeneratePrismalFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGeneratePrismalFlow)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GeneratePrismalFlow();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GeneratePrismalFlow ***********************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetFlowPositionAtT **********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetFlowPositionAtT_Parms
	{
		float T;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter posi\xc3\xa7\xc3\xa3o no flow baseada em par\xc3\xa2metro T (0-1) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter posi\xc3\xa7\xc3\xa3o no flow baseada em par\xc3\xa2metro T (0-1)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_T;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::NewProp_T = { "T", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetFlowPositionAtT_Parms, T), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetFlowPositionAtT_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::NewProp_T,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetFlowPositionAtT", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::AURACRONPCGPrismalFlow_eventGetFlowPositionAtT_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::AURACRONPCGPrismalFlow_eventGetFlowPositionAtT_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetFlowPositionAtT)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_T);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetFlowPositionAtT(Z_Param_T);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetFlowPositionAtT ************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetFlowWidthAtT *************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetFlowWidthAtT_Parms
	{
		float T;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter largura do flow em uma posi\xc3\xa7\xc3\xa3o espec\xc3\xad""fica */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter largura do flow em uma posi\xc3\xa7\xc3\xa3o espec\xc3\xad""fica" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_T;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::NewProp_T = { "T", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetFlowWidthAtT_Parms, T), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetFlowWidthAtT_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::NewProp_T,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetFlowWidthAtT", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::AURACRONPCGPrismalFlow_eventGetFlowWidthAtT_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::AURACRONPCGPrismalFlow_eventGetFlowWidthAtT_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetFlowWidthAtT)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_T);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetFlowWidthAtT(Z_Param_T);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetFlowWidthAtT ***************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function GetPCGComponent *************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics
{
	struct AURACRONPCGPrismalFlow_eventGetPCGComponent_Parms
	{
		UPCGComponent* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter componente PCG para acesso externo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter componente PCG para acesso externo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReturnValue_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000080588, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventGetPCGComponent_Parms, ReturnValue), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReturnValue_MetaData), NewProp_ReturnValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "GetPCGComponent", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::AURACRONPCGPrismalFlow_eventGetPCGComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::AURACRONPCGPrismalFlow_eventGetPCGComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execGetPCGComponent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UPCGComponent**)Z_Param__Result=P_THIS->GetPCGComponent();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function GetPCGComponent ***************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function IsPositionInFlow ************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics
{
	struct AURACRONPCGPrismalFlow_eventIsPositionInFlow_Parms
	{
		FVector Position;
		float Tolerance;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se uma posi\xc3\xa7\xc3\xa3o est\xc3\xa1 dentro do flow */" },
#endif
		{ "CPP_Default_Tolerance", "100.000000" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se uma posi\xc3\xa7\xc3\xa3o est\xc3\xa1 dentro do flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Position_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Position;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Tolerance;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_Position = { "Position", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventIsPositionInFlow_Parms, Position), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Position_MetaData), NewProp_Position_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_Tolerance = { "Tolerance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventIsPositionInFlow_Parms, Tolerance), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGPrismalFlow_eventIsPositionInFlow_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGPrismalFlow_eventIsPositionInFlow_Parms), &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_Position,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_Tolerance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "IsPositionInFlow", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::AURACRONPCGPrismalFlow_eventIsPositionInFlow_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::AURACRONPCGPrismalFlow_eventIsPositionInFlow_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execIsPositionInFlow)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Position);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Tolerance);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPositionInFlow(Z_Param_Out_Position,Z_Param_Tolerance);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function IsPositionInFlow **************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function SetActivityScale ************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics
{
	struct AURACRONPCGPrismalFlow_eventSetActivityScale_Parms
	{
		float NewActivityScale;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir escala de atividade */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir escala de atividade" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewActivityScale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::NewProp_NewActivityScale = { "NewActivityScale", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventSetActivityScale_Parms, NewActivityScale), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::NewProp_NewActivityScale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "SetActivityScale", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::AURACRONPCGPrismalFlow_eventSetActivityScale_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::AURACRONPCGPrismalFlow_eventSetActivityScale_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execSetActivityScale)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewActivityScale);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetActivityScale(Z_Param_NewActivityScale);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function SetActivityScale **************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function SetFlowIntensity ************************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics
{
	struct AURACRONPCGPrismalFlow_eventSetFlowIntensity_Parms
	{
		float NewIntensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Definir intensidade global do flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir intensidade global do flow" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewIntensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::NewProp_NewIntensity = { "NewIntensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventSetFlowIntensity_Parms, NewIntensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::NewProp_NewIntensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "SetFlowIntensity", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::AURACRONPCGPrismalFlow_eventSetFlowIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::AURACRONPCGPrismalFlow_eventSetFlowIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execSetFlowIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewIntensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetFlowIntensity(Z_Param_NewIntensity);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function SetFlowIntensity **************************

// ********** Begin Class AAURACRONPCGPrismalFlow Function UpdateForMapPhase ***********************
struct Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics
{
	struct AURACRONPCGPrismalFlow_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar o flow para uma fase espec\xc3\xad""fica do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar o flow para uma fase espec\xc3\xad""fica do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGPrismalFlow_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 657470012
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGPrismalFlow, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::AURACRONPCGPrismalFlow_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::AURACRONPCGPrismalFlow_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGPrismalFlow::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGPrismalFlow Function UpdateForMapPhase *************************

// ********** Begin Class AAURACRONPCGPrismalFlow **************************************************
void AAURACRONPCGPrismalFlow::StaticRegisterNativesAAURACRONPCGPrismalFlow()
{
	UClass* Class = AAURACRONPCGPrismalFlow::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GeneratePrismalFlow", &AAURACRONPCGPrismalFlow::execGeneratePrismalFlow },
		{ "GetFlowPositionAtT", &AAURACRONPCGPrismalFlow::execGetFlowPositionAtT },
		{ "GetFlowWidthAtT", &AAURACRONPCGPrismalFlow::execGetFlowWidthAtT },
		{ "GetPCGComponent", &AAURACRONPCGPrismalFlow::execGetPCGComponent },
		{ "IsPositionInFlow", &AAURACRONPCGPrismalFlow::execIsPositionInFlow },
		{ "SetActivityScale", &AAURACRONPCGPrismalFlow::execSetActivityScale },
		{ "SetFlowIntensity", &AAURACRONPCGPrismalFlow::execSetFlowIntensity },
		{ "UpdateForMapPhase", &AAURACRONPCGPrismalFlow::execUpdateForMapPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow;
UClass* AAURACRONPCGPrismalFlow::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGPrismalFlow;
	if (!Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGPrismalFlow"),
			Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGPrismalFlow,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister()
{
	return AAURACRONPCGPrismalFlow::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Ator para gerenciar o Prismal Flow serpentino que conecta os tr\xc3\xaas ambientes\n * O flow tem largura vari\xc3\xa1vel, curvas matem\xc3\xa1ticas precisas e ilhas estrat\xc3\xa9gicas\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGPrismalFlow.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator para gerenciar o Prismal Flow serpentino que conecta os tr\xc3\xaas ambientes\nO flow tem largura vari\xc3\xa1vel, curvas matem\xc3\xa1ticas precisas e ilhas estrat\xc3\xa9gicas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponent_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente PCG principal */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente PCG principal" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSpline_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Spline que define o caminho do flow */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spline que define o caminho do flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MainFlowEffect_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componente de efeitos visuais principais */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente de efeitos visuais principais" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowMesh_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Mesh do flow (alias para compatibilidade) */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Mesh do flow (alias para compatibilidade)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowEffect_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Efeito do flow (alias para compatibilidade) */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Efeito do flow (alias para compatibilidade)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalFlowIntensity_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
		{ "ClampMax", "2.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intensidade global do flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intensidade global do flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseFlowSpeed_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
		{ "ClampMax", "5.0" },
		{ "ClampMin", "0.1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Velocidade base do flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Velocidade base do flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NumControlPoints_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
		{ "ClampMax", "50" },
		{ "ClampMin", "10" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero de pontos de controle para a curva serpentina */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero de pontos de controle para a curva serpentina" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SerpentineAmplitude_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
		{ "ClampMax", "3000.0" },
		{ "ClampMin", "500.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Amplitude das curvas serpentinas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Amplitude das curvas serpentinas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SerpentineFrequency_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
		{ "ClampMax", "6.0" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Frequ\xc3\xaancia das curvas serpentinas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Frequ\xc3\xaancia das curvas serpentinas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivityScale_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Escala de atividade do flow */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala de atividade do flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlowSegments_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Segmentos do flow gerados */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Segmentos do flow gerados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedComponents_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes gerados dinamicamente */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes gerados dinamicamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccumulatedTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo acumulado para anima\xc3\xa7\xc3\xb5""es */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGPrismalFlow.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo acumulado para anima\xc3\xa7\xc3\xb5""es" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowSpline;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MainFlowEffect;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FlowEffect;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalFlowIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseFlowSpeed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NumControlPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SerpentineAmplitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SerpentineFrequency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivityScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowSegments_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FlowSegments;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GeneratedComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GeneratedComponents;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccumulatedTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GeneratePrismalFlow, "GeneratePrismalFlow" }, // 3092910253
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowPositionAtT, "GetFlowPositionAtT" }, // 416255606
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetFlowWidthAtT, "GetFlowWidthAtT" }, // 2420087261
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_GetPCGComponent, "GetPCGComponent" }, // 3566791138
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_IsPositionInFlow, "IsPositionInFlow" }, // 839614461
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetActivityScale, "SetActivityScale" }, // 4212426650
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_SetFlowIntensity, "SetFlowIntensity" }, // 2076129306
		{ &Z_Construct_UFunction_AAURACRONPCGPrismalFlow_UpdateForMapPhase, "UpdateForMapPhase" }, // 1479627522
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGPrismalFlow>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_PCGComponent = { "PCGComponent", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, PCGComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponent_MetaData), NewProp_PCGComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowSpline = { "FlowSpline", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, FlowSpline), Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSpline_MetaData), NewProp_FlowSpline_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_MainFlowEffect = { "MainFlowEffect", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, MainFlowEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MainFlowEffect_MetaData), NewProp_MainFlowEffect_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowMesh = { "FlowMesh", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, FlowMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowMesh_MetaData), NewProp_FlowMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowEffect = { "FlowEffect", nullptr, (EPropertyFlags)0x01240800000a001d, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, FlowEffect), Z_Construct_UClass_UNiagaraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowEffect_MetaData), NewProp_FlowEffect_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_GlobalFlowIntensity = { "GlobalFlowIntensity", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, GlobalFlowIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalFlowIntensity_MetaData), NewProp_GlobalFlowIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_BaseFlowSpeed = { "BaseFlowSpeed", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, BaseFlowSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseFlowSpeed_MetaData), NewProp_BaseFlowSpeed_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_NumControlPoints = { "NumControlPoints", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, NumControlPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NumControlPoints_MetaData), NewProp_NumControlPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_SerpentineAmplitude = { "SerpentineAmplitude", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, SerpentineAmplitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SerpentineAmplitude_MetaData), NewProp_SerpentineAmplitude_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_SerpentineFrequency = { "SerpentineFrequency", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, SerpentineFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SerpentineFrequency_MetaData), NewProp_SerpentineFrequency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_ActivityScale = { "ActivityScale", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, ActivityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivityScale_MetaData), NewProp_ActivityScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowSegments_Inner = { "FlowSegments", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPrismalFlowSegment, METADATA_PARAMS(0, nullptr) }; // 2187783889
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowSegments = { "FlowSegments", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, FlowSegments), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlowSegments_MetaData), NewProp_FlowSegments_MetaData) }; // 2187783889
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_GeneratedComponents_Inner = { "GeneratedComponents", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UActorComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_GeneratedComponents = { "GeneratedComponents", nullptr, (EPropertyFlags)0x0040008000000008, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, GeneratedComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedComponents_MetaData), NewProp_GeneratedComponents_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 657470012
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_AccumulatedTime = { "AccumulatedTime", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGPrismalFlow, AccumulatedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccumulatedTime_MetaData), NewProp_AccumulatedTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_PCGComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowSpline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_MainFlowEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_GlobalFlowIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_BaseFlowSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_NumControlPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_SerpentineAmplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_SerpentineFrequency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_ActivityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowSegments_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_FlowSegments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_GeneratedComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_GeneratedComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_CurrentMapPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::NewProp_AccumulatedTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::ClassParams = {
	&AAURACRONPCGPrismalFlow::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow.OuterSingleton, Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGPrismalFlow);
AAURACRONPCGPrismalFlow::~AAURACRONPCGPrismalFlow() {}
// ********** End Class AAURACRONPCGPrismalFlow ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPrismalFlowSegment::StaticStruct, Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics::NewStructOps, TEXT("PrismalFlowSegment"), &Z_Registration_Info_UScriptStruct_FPrismalFlowSegment, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPrismalFlowSegment), 2187783889U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGPrismalFlow, AAURACRONPCGPrismalFlow::StaticClass, TEXT("AAURACRONPCGPrismalFlow"), &Z_Registration_Info_UClass_AAURACRONPCGPrismalFlow, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGPrismalFlow), 2255076671U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h__Script_AURACRON_1814722818(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
