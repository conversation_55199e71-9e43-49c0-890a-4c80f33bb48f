// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGPrismalFlow.h"

#ifdef AURACRON_AURACRONPCGPrismalFlow_generated_h
#error "AURACRONPCGPrismalFlow.generated.h already included, missing '#pragma once' in AURACRONPCGPrismalFlow.h"
#endif
#define AURACRON_AURACRONPCGPrismalFlow_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class UPCGComponent;
enum class EAURACRONMapPhase : uint8;

// ********** Begin ScriptStruct FPrismalFlowSegment ***********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_28_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPrismalFlowSegment_Statics; \
	static class UScriptStruct* StaticStruct();


struct FPrismalFlowSegment;
// ********** End ScriptStruct FPrismalFlowSegment *************************************************

// ********** Begin Class AAURACRONPCGPrismalFlow **************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_67_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetActivityScale); \
	DECLARE_FUNCTION(execGetPCGComponent); \
	DECLARE_FUNCTION(execIsPositionInFlow); \
	DECLARE_FUNCTION(execGetFlowWidthAtT); \
	DECLARE_FUNCTION(execGetFlowPositionAtT); \
	DECLARE_FUNCTION(execSetFlowIntensity); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execGeneratePrismalFlow);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_67_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGPrismalFlow(); \
	friend struct Z_Construct_UClass_AAURACRONPCGPrismalFlow_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGPrismalFlow, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGPrismalFlow_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGPrismalFlow)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_67_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGPrismalFlow(AAURACRONPCGPrismalFlow&&) = delete; \
	AAURACRONPCGPrismalFlow(const AAURACRONPCGPrismalFlow&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGPrismalFlow); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGPrismalFlow); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGPrismalFlow) \
	NO_API virtual ~AAURACRONPCGPrismalFlow();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_64_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_67_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_67_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_67_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h_67_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGPrismalFlow;

// ********** End Class AAURACRONPCGPrismalFlow ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGPrismalFlow_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
