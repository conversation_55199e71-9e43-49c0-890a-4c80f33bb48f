// AURACRONPCGUtility.cpp
// Implementação da Classe Utilitária PCG - UE 5.6

#include "PCG/AURACRONPCGUtility.h"
#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGTrail.h"
#include "PCG/AURACRONPCGIsland.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "PCGComponent.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/Engine.h"

// Definição de variáveis estáticas
TMap<UWorld*, FAURACRONPCGActorReferences> UAURACRONPCGUtility::ActorCache;
TMap<UWorld*, float> UAURACRONPCGUtility::CacheTimestamps;

FAURACRONPCGActorReferences UAURACRONPCGUtility::FindPCGActors(UWorld* World, const FAURACRONPCGSearchOptions& SearchOptions)
{
    FAURACRONPCGActorReferences ActorReferences;

    if (!IsValid(World))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGUtility::FindPCGActors - World inválido"));
        return ActorReferences;
    }

    // Verificar cache primeiro
    if (IsCacheValid(World))
    {
        if (SearchOptions.bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGUtility: Usando cache de atores PCG"));
        }
        return ActorCache[World];
    }

    // Limpar referências
    ActorReferences.Clear();

    TArray<AActor*> FoundActors;

    // Encontrar ambientes
    if (SearchOptions.bSearchEnvironments)
    {
        FoundActors.Empty();
        UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGEnvironment::StaticClass(), FoundActors);
        
        for (AActor* Actor : FoundActors)
        {
            if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(Actor))
            {
                if (SearchOptions.bIncludeInactiveActors || IsValidPCGActor(Environment))
                {
                    ActorReferences.EnvironmentActors.Add(Environment);
                }
            }
        }

        if (SearchOptions.bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGUtility: Encontrados %d ambientes"), 
                   ActorReferences.EnvironmentActors.Num());
        }
    }

    // Encontrar trilhas
    if (SearchOptions.bSearchTrails)
    {
        FoundActors.Empty();
        UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGTrail::StaticClass(), FoundActors);
        
        for (AActor* Actor : FoundActors)
        {
            if (AAURACRONPCGTrail* Trail = Cast<AAURACRONPCGTrail>(Actor))
            {
                if (SearchOptions.bIncludeInactiveActors || IsValidPCGActor(Trail))
                {
                    ActorReferences.TrailActors.Add(Trail);
                }
            }
        }

        if (SearchOptions.bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGUtility: Encontradas %d trilhas"), 
                   ActorReferences.TrailActors.Num());
        }
    }

    // Encontrar ilhas
    if (SearchOptions.bSearchIslands)
    {
        FoundActors.Empty();
        UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGIsland::StaticClass(), FoundActors);
        
        for (AActor* Actor : FoundActors)
        {
            if (AAURACRONPCGIsland* Island = Cast<AAURACRONPCGIsland>(Actor))
            {
                if (SearchOptions.bIncludeInactiveActors || IsValidPCGActor(Island))
                {
                    ActorReferences.IslandActors.Add(Island);
                }
            }
        }

        if (SearchOptions.bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGUtility: Encontradas %d ilhas"), 
                   ActorReferences.IslandActors.Num());
        }
    }

    // Encontrar Prismal Flow
    if (SearchOptions.bSearchPrismalFlow)
    {
        FoundActors.Empty();
        UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGPrismalFlow::StaticClass(), FoundActors);
        
        if (FoundActors.Num() > 0)
        {
            for (AActor* Actor : FoundActors)
            {
                if (AAURACRONPCGPrismalFlow* PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(Actor))
                {
                    if (SearchOptions.bIncludeInactiveActors || IsValidPCGActor(PrismalFlow))
                    {
                        ActorReferences.PrismalFlowActor = PrismalFlow;
                        break; // Apenas um Prismal Flow por mundo
                    }
                }
            }
        }

        if (SearchOptions.bVerboseLogging)
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGUtility: Prismal Flow %s"), 
                   ActorReferences.PrismalFlowActor ? TEXT("encontrado") : TEXT("não encontrado"));
        }
    }

    // Atualizar cache
    UpdateActorCache(World, ActorReferences);

    // Log final
    if (SearchOptions.bVerboseLogging)
    {
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGUtility: Total de atores PCG encontrados: %d"), 
               ActorReferences.GetTotalActorCount());
    }

    return ActorReferences;
}

FAURACRONPCGActorReferences UAURACRONPCGUtility::FindAllPCGActors(UWorld* World)
{
    FAURACRONPCGSearchOptions DefaultOptions;
    return FindPCGActors(World, DefaultOptions);
}

TArray<AActor*> UAURACRONPCGUtility::FindPCGActorsOfClass(UWorld* World, UClass* ActorClass, bool bIncludeInactive)
{
    TArray<AActor*> FoundActors;

    if (!IsValid(World) || !IsValid(ActorClass))
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGUtility::FindPCGActorsOfClass - Parâmetros inválidos"));
        return FoundActors;
    }

    TArray<AActor*> AllActors;
    UGameplayStatics::GetAllActorsOfClass(World, ActorClass, AllActors);

    for (AActor* Actor : AllActors)
    {
        if (bIncludeInactive || IsValidPCGActor(Actor))
        {
            FoundActors.Add(Actor);
        }
    }

    return FoundActors;
}

bool UAURACRONPCGUtility::IsValidPCGActor(AActor* Actor)
{
    if (!IsValid(Actor))
    {
        return false;
    }

    // Verificar se o ator está ativo
    if (!Actor->IsActorTickEnabled() || Actor->IsActorBeingDestroyed())
    {
        return false;
    }

    // Verificações específicas para tipos PCG
    if (AAURACRONPCGEnvironment* Environment = Cast<AAURACRONPCGEnvironment>(Actor))
    {
        // Verificar se tem componente PCG válido
        UPCGComponent* PCGComp = Environment->GetPCGComponent();
        return IsValid(Cast<UObject>(PCGComp));
    }
    else if (AAURACRONPCGTrail* Trail = Cast<AAURACRONPCGTrail>(Actor))
    {
        // Verificar se a trilha está configurada
        UPCGComponent* PCGComp = Trail->GetPCGComponent();
        return IsValid(Cast<UObject>(PCGComp));
    }
    else if (AAURACRONPCGIsland* Island = Cast<AAURACRONPCGIsland>(Actor))
    {
        // Verificar se a ilha está configurada
        UPCGComponent* PCGComp = Island->GetPCGComponent();
        return IsValid(Cast<UObject>(PCGComp));
    }
    else if (AAURACRONPCGPrismalFlow* PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(Actor))
    {
        // Verificar se o Prismal Flow está configurado
        UPCGComponent* PCGComp = PrismalFlow->GetPCGComponent();
        return IsValid(Cast<UObject>(PCGComp));
    }

    // Para outros tipos de atores PCG, verificação básica
    return true;
}

FString UAURACRONPCGUtility::GetPCGActorStatistics(UWorld* World)
{
    if (!IsValid(World))
    {
        return TEXT("Mundo inválido");
    }

    FAURACRONPCGActorReferences ActorRefs = FindPCGActors(World, FAURACRONPCGSearchOptions::All());

    FString Stats = FString::Printf(
        TEXT("=== ESTATÍSTICAS PCG ===\n")
        TEXT("Ambientes: %d\n")
        TEXT("Trilhas: %d\n")
        TEXT("Ilhas: %d\n")
        TEXT("Prismal Flow: %s\n")
        TEXT("Total: %d atores"),
        ActorRefs.EnvironmentActors.Num(),
        ActorRefs.TrailActors.Num(),
        ActorRefs.IslandActors.Num(),
        ActorRefs.PrismalFlowActor ? TEXT("1") : TEXT("0"),
        ActorRefs.GetTotalActorCount()
    );

    return Stats;
}

void UAURACRONPCGUtility::ApplyConfigurationToAllActors(const FAURACRONPCGActorReferences& ActorReferences, const FString& ConfigurationName, float Value)
{
    // Aplicar configuração aos ambientes
    for (AAURACRONPCGEnvironment* Environment : ActorReferences.EnvironmentActors)
    {
        if (IsValid(Environment))
        {
            if (ConfigurationName == TEXT("ActivityScale"))
            {
                Environment->SetActivityScale(Value);
            }
            // Adicionar mais configurações conforme necessário
        }
    }

    // Aplicar configuração às trilhas
    for (AAURACRONPCGTrail* Trail : ActorReferences.TrailActors)
    {
        if (IsValid(Trail))
        {
            if (ConfigurationName == TEXT("ActivityScale"))
            {
                Trail->SetActivityScale(Value);
            }
            // Adicionar mais configurações conforme necessário
        }
    }

    // Aplicar configuração às ilhas
    for (AAURACRONPCGIsland* Island : ActorReferences.IslandActors)
    {
        if (IsValid(Island))
        {
            if (ConfigurationName == TEXT("ActivityScale"))
            {
                Island->SetActivityScale(Value);
            }
            // Adicionar mais configurações conforme necessário
        }
    }

    // Aplicar configuração ao Prismal Flow
    if (IsValid(ActorReferences.PrismalFlowActor))
    {
        if (ConfigurationName == TEXT("ActivityScale"))
        {
            ActorReferences.PrismalFlowActor->SetActivityScale(Value);
        }
        // Adicionar mais configurações conforme necessário
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGUtility: Configuração '%s' = %.2f aplicada a %d atores"), 
           *ConfigurationName, Value, ActorReferences.GetTotalActorCount());
}

void UAURACRONPCGUtility::RegisterPCGActorChangeCallback(UWorld* World, const FString& CallbackName)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGUtility: Tentativa de registrar callback '%s' com World nulo"), *CallbackName);
        return;
    }
    
    // Implementar sistema de callbacks para mudanças em atores PCG
    static TMap<UWorld*, TArray<FString>> RegisteredCallbacks;
    
    if (!RegisteredCallbacks.Contains(World))
    {
        RegisteredCallbacks.Add(World, TArray<FString>());
    }
    
    TArray<FString>& WorldCallbacks = RegisteredCallbacks[World];
    
    if (!WorldCallbacks.Contains(CallbackName))
    {
        WorldCallbacks.Add(CallbackName);
        
        // Configurar delegate para monitorar mudanças
        if (World->OnActorSpawned.IsBoundToObject(World))
        {
            World->OnActorSpawned.AddUObject(World, FOnActorSpawned::FDelegate::CreateLambda(
                [CallbackName](AActor* SpawnedActor)
                {
                    if (IsValidPCGActor(SpawnedActor))
                    {
                        UE_LOG(LogTemp, Verbose, TEXT("PCG Actor spawned - Callback '%s': %s"), 
                               *CallbackName, *SpawnedActor->GetName());
                    }
                }
            ));
        }
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGUtility: Callback '%s' registrado para mundo %s"), 
               *CallbackName, *World->GetName());
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGUtility: Callback '%s' já registrado para mundo %s"), 
               *CallbackName, *World->GetName());
    }
}

bool UAURACRONPCGUtility::IsCacheValid(UWorld* World)
{
    if (!ActorCache.Contains(World) || !CacheTimestamps.Contains(World))
    {
        return false;
    }

    float CurrentTime = World->GetTimeSeconds();
    float CacheTime = CacheTimestamps[World];

    return (CurrentTime - CacheTime) < CACHE_LIFETIME;
}

void UAURACRONPCGUtility::UpdateActorCache(UWorld* World, const FAURACRONPCGActorReferences& NewReferences)
{
    ActorCache.Add(World, NewReferences);
    CacheTimestamps.Add(World, World->GetTimeSeconds());

    // Limpar cache expirado periodicamente
    CleanExpiredCache();
}

void UAURACRONPCGUtility::CleanExpiredCache()
{
    TArray<UWorld*> ExpiredWorlds;

    for (auto& CacheEntry : CacheTimestamps)
    {
        UWorld* World = CacheEntry.Key;
        float CacheTime = CacheEntry.Value;

        if (!IsValid(World))
        {
            ExpiredWorlds.Add(World);
            continue;
        }

        float CurrentTime = World->GetTimeSeconds();
        if ((CurrentTime - CacheTime) >= CACHE_LIFETIME)
        {
            ExpiredWorlds.Add(World);
        }
    }

    // Remover entradas expiradas
    for (UWorld* ExpiredWorld : ExpiredWorlds)
    {
        ActorCache.Remove(ExpiredWorld);
        CacheTimestamps.Remove(ExpiredWorld);
    }
}
