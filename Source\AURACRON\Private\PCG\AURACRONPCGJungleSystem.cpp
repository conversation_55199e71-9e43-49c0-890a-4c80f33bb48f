// AURACRONPCGJungleSystem.cpp
// Implementação do sistema de jungle baseado no LoL

#include "PCG/AURACRONPCGJungleSystem.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGObjectiveSystem.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "Engine/World.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Kismet/GameplayStatics.h"

AAURACRONPCGJungleSystem::AAURACRONPCGJungleSystem()
    : bAutoGenerate(true)
    , CurrentEnvironment(EAURACRONEnvironmentType::RadiantPlains)
    , CurrentMapPhase(EAURACRONMapPhase::Awakening)
{
    PrimaryActorTick.bCanEverTick = true;
    
    // Configurar replicação para multiplayer
    bReplicates = true;
    SetReplicateMovement(false);
    
    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));
}

void AAURACRONPCGJungleSystem::BeginPlay()
{
    Super::BeginPlay();
    
    // Gerar sistema apenas no servidor
    if (HasAuthority() && bAutoGenerate)
    {
        // Delay pequeno para garantir que outros sistemas estejam prontos
        FTimerHandle GenerationTimer;
        GetWorld()->GetTimerManager().SetTimer(GenerationTimer, this, 
            &AAURACRONPCGJungleSystem::GenerateJungleCamps, 1.5f, false);
    }
}

void AAURACRONPCGJungleSystem::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Atualizar timers de respawn
    if (HasAuthority())
    {
        for (int32 i = 0; i < JungleCamps.Num(); ++i)
        {
            if (!JungleCamps[i].bIsActive && JungleCamps[i].TimeUntilRespawn > 0.0f)
            {
                JungleCamps[i].TimeUntilRespawn -= DeltaTime;
                
                if (JungleCamps[i].TimeUntilRespawn <= 0.0f)
                {
                    OnCampRespawn(i);
                }
            }
        }
        
        // Atualizar sistema de IA adaptativa
        if (bAdaptiveSystemEnabled)
        {
            UpdateAdaptiveData(DeltaTime);
        }
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES PÚBLICAS
// ========================================

void AAURACRONPCGJungleSystem::GenerateJungleCamps()
{
    if (!HasAuthority())
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGJungleSystem: Gerando jungle camps baseados no LoL"));
    
    // Limpar camps anteriores
    for (const auto& EnvPair : CampMeshesByEnvironment)
    {
        ClearCampsForEnvironment(EnvPair.Key);
    }
    
    // Inicializar informações dos camps
    InitializeCampInfos();
    
    // Gerar camps para todos os 3 ambientes
    for (int32 EnvIndex = 0; EnvIndex < 3; ++EnvIndex)
    {
        EAURACRONEnvironmentType Environment = static_cast<EAURACRONEnvironmentType>(EnvIndex);
        GenerateCampsForEnvironment(Environment);
    }
    
    // Iniciar com Radiant Plains ativo
    UpdateForEnvironment(EAURACRONEnvironmentType::RadiantPlains);
    
    // Inicializar sistema de IA adaptativa
    InitializeAdaptiveSystem();
    
    // Configurar integração com World Partition e Data Layers
    ConfigureWorldPartitionStreaming();
    AssociateWithDataLayer();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGJungleSystem: Gerados %d jungle camps para 3 ambientes"), JungleCamps.Num());
}

void AAURACRONPCGJungleSystem::GenerateCampsForEnvironment(EAURACRONEnvironmentType Environment)
{
    if (!HasAuthority())
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGJungleSystem: Gerando camps para ambiente %d"), static_cast<int32>(Environment));
    
    // Criar componentes visuais para este ambiente
    TArray<UStaticMeshComponent*> EnvironmentMeshes;
    
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        CreateCamp(i, Environment);
    }
    
    // Aplicar características específicas do ambiente
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        ApplyEnvironmentCharacteristics(i, Environment);
    }
}

TArray<FAURACRONJungleCampInfo> AAURACRONPCGJungleSystem::GetCampsByType(EAURACRONJungleCampType CampType) const
{
    TArray<FAURACRONJungleCampInfo> FilteredCamps;
    
    for (const FAURACRONJungleCampInfo& Camp : JungleCamps)
    {
        if (Camp.CampType == CampType)
        {
            FilteredCamps.Add(Camp);
        }
    }
    
    return FilteredCamps;
}

TArray<FAURACRONJungleCampInfo> AAURACRONPCGJungleSystem::GetCampsBySide(int32 MapSide) const
{
    TArray<FAURACRONJungleCampInfo> FilteredCamps;
    
    for (const FAURACRONJungleCampInfo& Camp : JungleCamps)
    {
        if (Camp.MapSide == MapSide)
        {
            FilteredCamps.Add(Camp);
        }
    }
    
    return FilteredCamps;
}

void AAURACRONPCGJungleSystem::ClearCamp(int32 CampIndex)
{
    if (!HasAuthority() || CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        return;
    }
    
    FAURACRONJungleCampInfo& Camp = JungleCamps[CampIndex];
    
    if (Camp.bIsActive)
    {
        Camp.bIsActive = false;
        Camp.TimeUntilRespawn = Camp.RespawnTime;
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGJungleSystem: Camp %d cleared, respawn in %.1fs"), 
            CampIndex, Camp.RespawnTime);
        
        // Atualizar visibilidade
        UpdateCampVisibility();
    }
}

bool AAURACRONPCGJungleSystem::IsCampAvailable(int32 CampIndex) const
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        return false;
    }
    
    return JungleCamps[CampIndex].bIsActive;
}

void AAURACRONPCGJungleSystem::UpdateForEnvironment(EAURACRONEnvironmentType NewEnvironment)
{
    if (CurrentEnvironment == NewEnvironment)
    {
        return;
    }
    
    CurrentEnvironment = NewEnvironment;
    UpdateCampVisibility();
    
    UE_LOG(LogTemp, Log, TEXT("AURACRONPCGJungleSystem: Atualizado para ambiente %d"), static_cast<int32>(NewEnvironment));
}

void AAURACRONPCGJungleSystem::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    if (CurrentMapPhase != MapPhase)
    {
        CurrentMapPhase = MapPhase;
        ApplyMapPhaseEffects();
        
        UE_LOG(LogTemp, Log, TEXT("AURACRONPCGJungleSystem: Atualizado para fase %d"), static_cast<int32>(MapPhase));
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES INTERNAS
// ========================================

void AAURACRONPCGJungleSystem::InitializeCampInfos()
{
    JungleCamps.Empty();
    
    // BUFF CAMPS (2 camps - equivalentes ao Blue/Red Buff do LoL)
    
    // Radiant Essence (Blue Buff equivalent) - Team 1 side
    FAURACRONJungleCampInfo RadiantEssence = GetDefaultCampConfig(EAURACRONJungleCampType::RadiantEssence);
    RadiantEssence.MapSide = 0; // Team 1
    JungleCamps.Add(RadiantEssence);
    
    // Chaos Essence (Red Buff equivalent) - Team 2 side
    FAURACRONJungleCampInfo ChaosEssence = GetDefaultCampConfig(EAURACRONJungleCampType::ChaosEssence);
    ChaosEssence.MapSide = 1; // Team 2
    JungleCamps.Add(ChaosEssence);
    
    // CAMPS NORMAIS (10 camps distribuídos simetricamente)
    
    // Team 1 Jungle (5 camps)
    for (int32 i = 0; i < 5; ++i)
    {
        EAURACRONJungleCampType CampTypes[] = {
            EAURACRONJungleCampType::StoneGuardians,
            EAURACRONJungleCampType::PrismalToad,
            EAURACRONJungleCampType::SpectralPack,
            EAURACRONJungleCampType::WindSpirits,
            EAURACRONJungleCampType::FluxCrawler
        };
        
        FAURACRONJungleCampInfo Camp = GetDefaultCampConfig(CampTypes[i]);
        Camp.MapSide = 0; // Team 1
        JungleCamps.Add(Camp);
    }
    
    // Team 2 Jungle (5 camps - espelho)
    for (int32 i = 0; i < 5; ++i)
    {
        EAURACRONJungleCampType CampTypes[] = {
            EAURACRONJungleCampType::StoneGuardians,
            EAURACRONJungleCampType::PrismalToad,
            EAURACRONJungleCampType::SpectralPack,
            EAURACRONJungleCampType::WindSpirits,
            EAURACRONJungleCampType::FluxCrawler
        };
        
        FAURACRONJungleCampInfo Camp = GetDefaultCampConfig(CampTypes[i]);
        Camp.MapSide = 1; // Team 2
        JungleCamps.Add(Camp);
    }
    
    // Calcular posições para todos os ambientes
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        for (int32 EnvIndex = 0; EnvIndex < 3; ++EnvIndex)
        {
            EAURACRONEnvironmentType Environment = static_cast<EAURACRONEnvironmentType>(EnvIndex);
            FVector Position = CalculateCampPosition(JungleCamps[i].CampType, JungleCamps[i].MapSide, Environment);
            JungleCamps[i].PositionsByEnvironment.Add(Environment, Position);
        }
    }
}

FAURACRONJungleCampInfo AAURACRONPCGJungleSystem::GetDefaultCampConfig(EAURACRONJungleCampType CampType)
{
    FAURACRONJungleCampInfo Config;
    Config.CampType = CampType;

    switch (CampType)
    {
    case EAURACRONJungleCampType::RadiantEssence:
        Config.CampRadius = FAURACRONMapDimensions::BUFF_CAMP_RADIUS_CM;
        Config.RespawnTime = FAURACRONMapDimensions::BUFF_CAMP_RESPAWN_TIME;
        Config.bIsBuffCamp = true;
        Config.DifficultyLevel = 3;
        Config.Rewards.Add(TEXT("ManaRegen"), 50.0f);
        Config.Rewards.Add(TEXT("CooldownReduction"), 20.0f);
        Config.Rewards.Add(TEXT("Experience"), 200.0f);
        break;

    case EAURACRONJungleCampType::ChaosEssence:
        Config.CampRadius = FAURACRONMapDimensions::BUFF_CAMP_RADIUS_CM;
        Config.RespawnTime = FAURACRONMapDimensions::BUFF_CAMP_RESPAWN_TIME;
        Config.bIsBuffCamp = true;
        Config.DifficultyLevel = 3;
        Config.Rewards.Add(TEXT("AttackDamage"), 30.0f);
        Config.Rewards.Add(TEXT("SlowOnHit"), 15.0f);
        Config.Rewards.Add(TEXT("Experience"), 200.0f);
        break;

    case EAURACRONJungleCampType::StoneGuardians:
        Config.CampRadius = FAURACRONMapDimensions::JUNGLE_CAMP_RADIUS_CM;
        Config.RespawnTime = FAURACRONMapDimensions::NORMAL_CAMP_RESPAWN_TIME;
        Config.DifficultyLevel = 2;
        Config.Rewards.Add(TEXT("Gold"), 100.0f);
        Config.Rewards.Add(TEXT("Experience"), 150.0f);
        break;

    case EAURACRONJungleCampType::PrismalToad:
        Config.CampRadius = FAURACRONMapDimensions::JUNGLE_CAMP_RADIUS_CM;
        Config.RespawnTime = FAURACRONMapDimensions::NORMAL_CAMP_RESPAWN_TIME;
        Config.DifficultyLevel = 2;
        Config.Rewards.Add(TEXT("Gold"), 80.0f);
        Config.Rewards.Add(TEXT("Experience"), 120.0f);
        break;

    case EAURACRONJungleCampType::SpectralPack:
        Config.CampRadius = FAURACRONMapDimensions::JUNGLE_CAMP_RADIUS_CM;
        Config.RespawnTime = FAURACRONMapDimensions::NORMAL_CAMP_RESPAWN_TIME;
        Config.DifficultyLevel = 1;
        Config.Rewards.Add(TEXT("Gold"), 60.0f);
        Config.Rewards.Add(TEXT("Experience"), 100.0f);
        break;

    case EAURACRONJungleCampType::WindSpirits:
        Config.CampRadius = FAURACRONMapDimensions::JUNGLE_CAMP_RADIUS_CM;
        Config.RespawnTime = FAURACRONMapDimensions::NORMAL_CAMP_RESPAWN_TIME;
        Config.DifficultyLevel = 2;
        Config.Rewards.Add(TEXT("Gold"), 90.0f);
        Config.Rewards.Add(TEXT("Experience"), 130.0f);
        break;

    case EAURACRONJungleCampType::FluxCrawler:
        Config.CampRadius = FAURACRONMapDimensions::JUNGLE_CAMP_RADIUS_CM * 0.8f; // Menor
        Config.RespawnTime = 150.0f; // 2.5 minutos (como Scuttle)
        Config.DifficultyLevel = 1;
        Config.Rewards.Add(TEXT("Gold"), 70.0f);
        Config.Rewards.Add(TEXT("Experience"), 80.0f);
        Config.Rewards.Add(TEXT("Vision"), 120.0f); // Fornece visão como Scuttle
        break;
    }

    return Config;
}

FVector AAURACRONPCGJungleSystem::CalculateCampPosition(EAURACRONJungleCampType CampType, int32 MapSide, EAURACRONEnvironmentType Environment)
{
    FVector BasePosition = FAURACRONMapDimensions::MAP_CENTER;
    float EnvironmentHeight = 0.0f;

    // Obter altura do ambiente
    switch (Environment)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        EnvironmentHeight = FAURACRONMapDimensions::RADIANT_PLAINS_HEIGHT_CM;
        break;
    case EAURACRONEnvironmentType::ZephyrFirmament:
        EnvironmentHeight = FAURACRONMapDimensions::ZEPHYR_FIRMAMENT_HEIGHT_CM;
        break;
    case EAURACRONEnvironmentType::PurgatoryRealm:
        EnvironmentHeight = FAURACRONMapDimensions::PURGATORY_REALM_HEIGHT_CM;
        break;
    }

    // Calcular posição baseada no tipo de camp e lado do mapa
    FVector Position = BasePosition;
    Position.Z = EnvironmentHeight;

    switch (CampType)
    {
    case EAURACRONJungleCampType::RadiantEssence:
        // Sempre no lado Team 1 (posição fixa do LoL)
        Position += FVector(FAURACRONMapDimensions::RADIANT_ESSENCE_X, FAURACRONMapDimensions::RADIANT_ESSENCE_Y, 0.0f);
        break;

    case EAURACRONJungleCampType::ChaosEssence:
        // Sempre no lado Team 2 (posição fixa do LoL)
        Position += FVector(FAURACRONMapDimensions::CHAOS_ESSENCE_X, FAURACRONMapDimensions::CHAOS_ESSENCE_Y, 0.0f);
        break;

    case EAURACRONJungleCampType::StoneGuardians:
        // Krugs equivalent - cantos do mapa
        if (MapSide == 0) // Team 1
            Position += FVector(-3500.0f, -1500.0f, 0.0f);
        else // Team 2
            Position += FVector(3500.0f, 1500.0f, 0.0f);
        break;

    case EAURACRONJungleCampType::PrismalToad:
        // Gromp equivalent - próximo aos buffs
        if (MapSide == 0) // Team 1
            Position += FVector(-2800.0f, 800.0f, 0.0f);
        else // Team 2
            Position += FVector(2800.0f, -800.0f, 0.0f);
        break;

    case EAURACRONJungleCampType::SpectralPack:
        // Wolves equivalent - entre lanes
        if (MapSide == 0) // Team 1
            Position += FVector(-1800.0f, -3200.0f, 0.0f);
        else // Team 2
            Position += FVector(1800.0f, 3200.0f, 0.0f);
        break;

    case EAURACRONJungleCampType::WindSpirits:
        // Raptors equivalent - jungle central
        if (MapSide == 0) // Team 1
            Position += FVector(-1200.0f, 2500.0f, 0.0f);
        else // Team 2
            Position += FVector(1200.0f, -2500.0f, 0.0f);
        break;

    case EAURACRONJungleCampType::FluxCrawler:
        // Scuttle equivalent - no Prismal Flow (river)
        if (MapSide == 0) // Superior
            Position += FVector(0.0f, 3000.0f, 0.0f);
        else // Inferior
            Position += FVector(0.0f, -3000.0f, 0.0f);
        break;
    }

    // Aplicar variações específicas do ambiente
    if (Environment == EAURACRONEnvironmentType::ZephyrFirmament)
    {
        // Adicionar variação de altura para plataformas
        Position.Z += FMath::RandRange(-200.0f, 500.0f);
    }
    else if (Environment == EAURACRONEnvironmentType::PurgatoryRealm)
    {
        // Adicionar deslocamento lateral para túneis
        FVector Offset = FVector(FMath::RandRange(-300.0f, 300.0f), FMath::RandRange(-300.0f, 300.0f), 0.0f);
        Position += Offset;
    }

    return Position;
}

// Implementação das funções restantes
void AAURACRONPCGJungleSystem::CreateCamp(int32 CampIndex, EAURACRONEnvironmentType Environment)
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        return;
    }
    
    // Obter informações do camp
    FAURACRONJungleCampInfo& CampInfo = JungleCamps[CampIndex];
    
    // Calcular posição do camp
    FVector CampPosition = CalculateCampPosition(CampInfo.CampType, CampInfo.MapSide, Environment);
    
    // Criar componente de colisão
    if (CampCollisionComponents.Num() <= CampIndex)
    {
        USphereComponent* CollisionComponent = NewObject<USphereComponent>(this);
        CollisionComponent->RegisterComponent();
        CollisionComponent->SetSphereRadius(CampInfo.CampRadius);
        CollisionComponent->SetCollisionProfileName(TEXT("OverlapAll"));
        CollisionComponent->SetGenerateOverlapEvents(true);
        CollisionComponent->SetWorldLocation(CampPosition);
        CollisionComponent->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepWorldTransform);
        
        CampCollisionComponents.Add(CollisionComponent);
    }
    else
    {
        CampCollisionComponents[CampIndex]->SetWorldLocation(CampPosition);
        CampCollisionComponents[CampIndex]->SetSphereRadius(CampInfo.CampRadius);
    }
    
    // Criar mesh visual do camp
    UStaticMesh* CampMesh = GetCampMesh(CampInfo.CampType, Environment);
    if (CampMesh)
    {
        // Verificar se já existe um array para este ambiente
        if (!CampMeshesByEnvironment.Contains(Environment))
        {
            CampMeshesByEnvironment.Add(Environment, FAURACRONMeshComponentArray());
        }
        
        // Verificar se já existe um componente para este camp
        FAURACRONMeshComponentArray& MeshArray = CampMeshesByEnvironment[Environment];
        if (MeshArray.MeshComponents.Num() <= CampIndex)
        {
            UStaticMeshComponent* MeshComponent = NewObject<UStaticMeshComponent>(this);
            MeshComponent->RegisterComponent();
            MeshComponent->SetStaticMesh(CampMesh);
            MeshComponent->SetWorldLocation(CampPosition);
            MeshComponent->AttachToComponent(RootComponent, FAttachmentTransformRules::KeepWorldTransform);
            
            MeshArray.MeshComponents.Add(MeshComponent);
        }
        else
        {
            MeshArray.MeshComponents[CampIndex]->SetStaticMesh(CampMesh);
            MeshArray.MeshComponents[CampIndex]->SetWorldLocation(CampPosition);
        }
    }
    
    // Aplicar características específicas do ambiente
    ApplyEnvironmentCharacteristics(CampIndex, Environment);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::CreateCamp - Camp %d criado na posição %s"), 
           CampIndex, *CampPosition.ToString());
}

void AAURACRONPCGJungleSystem::ApplyEnvironmentCharacteristics(int32 CampIndex, EAURACRONEnvironmentType Environment)
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        return;
    }
    
    FAURACRONJungleCampInfo& CampInfo = JungleCamps[CampIndex];
    
    // Aplicar características específicas do ambiente
    switch (Environment)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        // Camps mais visíveis e com recompensas padrão
        CampInfo.AdaptiveRewardMultiplier = 1.0f;
        break;
        
    case EAURACRONEnvironmentType::ZephyrFirmament:
        // Camps mais difíceis mas com recompensas maiores
        CampInfo.AdaptiveDamageMultiplier = 1.2f;
        CampInfo.AdaptiveRewardMultiplier = 1.3f;
        break;
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        // Camps muito difíceis com recompensas significativas
        CampInfo.AdaptiveDamageMultiplier = 1.5f;
        CampInfo.AdaptiveHealthMultiplier = 1.3f;
        CampInfo.AdaptiveRewardMultiplier = 1.5f;
        break;
    }
    
    // Aplicar comportamento adaptativo inicial
    ApplyAdaptiveBehavior(CampIndex, CampInfo.CurrentBehavior);
}

void AAURACRONPCGJungleSystem::OnCampRespawn(int32 CampIndex)
{
    if (CampIndex >= 0 && CampIndex < JungleCamps.Num())
    {
        JungleCamps[CampIndex].bIsActive = true;
        JungleCamps[CampIndex].TimeUntilRespawn = 0.0f;
        
        // Recalcular comportamento adaptativo ao respawnar
        if (bAdaptiveSystemEnabled)
        {
            EAURACRONJungleAdaptiveBehavior NewBehavior = CalculateAdaptiveBehavior(CampIndex);
            ApplyAdaptiveBehavior(CampIndex, NewBehavior);
        }
        
        UpdateCampVisibility();
        
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::OnCampRespawn - Camp %d respawned com comportamento %d"), 
               CampIndex, (int32)JungleCamps[CampIndex].CurrentBehavior);
    }
}

void AAURACRONPCGJungleSystem::UpdateCampVisibility()
{
    // Atualizar visibilidade de todos os camps em todos os ambientes
    for (EAURACRONEnvironmentType Environment : TEnumRange<EAURACRONEnvironmentType>())
    {
        if (CampMeshesByEnvironment.Contains(Environment))
        {
            FAURACRONMeshComponentArray& MeshArray = CampMeshesByEnvironment[Environment];
            
            for (int32 i = 0; i < MeshArray.MeshComponents.Num(); ++i)
            {
                if (i < JungleCamps.Num() && MeshArray.MeshComponents[i])
                {
                    // Mostrar apenas se o ambiente atual corresponder e o camp estiver ativo
                    bool bShouldBeVisible = (Environment == CurrentEnvironment && JungleCamps[i].bIsActive);
                    MeshArray.MeshComponents[i]->SetVisibility(bShouldBeVisible);
                }
            }
        }
    }
}

void AAURACRONPCGJungleSystem::ClearCampsForEnvironment(EAURACRONEnvironmentType Environment)
{
    // Remover componentes visuais para o ambiente especificado
    if (CampMeshesByEnvironment.Contains(Environment))
    {
        FAURACRONMeshComponentArray& MeshArray = CampMeshesByEnvironment[Environment];
        
        for (UStaticMeshComponent* MeshComponent : MeshArray.MeshComponents)
        {
            if (MeshComponent)
            {
                MeshComponent->DestroyComponent();
            }
        }
        
        MeshArray.MeshComponents.Empty();
        CampMeshesByEnvironment.Remove(Environment);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::ClearCampsForEnvironment - Camps limpos para ambiente %d"), 
           (int32)Environment);
}

void AAURACRONPCGJungleSystem::ApplyMapPhaseEffects()
{
    // Aplicar efeitos visuais e comportamentais baseados na fase do mapa
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        FAURACRONJungleCampInfo& CampInfo = JungleCamps[i];
        
        // Ajustar multiplicadores baseados na fase do mapa
        switch (CurrentMapPhase)
        {
        case EAURACRONMapPhase::Awakening:
            // Fase inicial - camps mais fáceis
            CampInfo.AdaptiveDamageMultiplier *= 0.8f;
            CampInfo.AdaptiveHealthMultiplier *= 0.8f;
            break;
            
        case EAURACRONMapPhase::Convergence:
            // Fase intermediária - dificuldade padrão
            // Manter multiplicadores como estão
            break;
            
        case EAURACRONMapPhase::Intensification:
            // Fase avançada - camps mais difíceis
            CampInfo.AdaptiveDamageMultiplier *= 1.2f;
            CampInfo.AdaptiveHealthMultiplier *= 1.1f;
            CampInfo.AdaptiveRewardMultiplier *= 1.2f;
            break;
            
        case EAURACRONMapPhase::Resolution:
            // Fase final - camps muito difíceis mas recompensadores
            CampInfo.AdaptiveDamageMultiplier *= 1.5f;
            CampInfo.AdaptiveHealthMultiplier *= 1.3f;
            CampInfo.AdaptiveRewardMultiplier *= 1.5f;
            break;
        }
        
        // Aplicar efeitos visuais aos componentes de mesh
        if (CampMeshesByEnvironment.Contains(CurrentEnvironment))
        {
            FAURACRONMeshComponentArray& MeshArray = CampMeshesByEnvironment[CurrentEnvironment];
            
            if (i < MeshArray.MeshComponents.Num() && MeshArray.MeshComponents[i])
            {
                // Ajustar escala baseado na fase
                float ScaleFactor = 1.0f + (0.1f * (int32)CurrentMapPhase);
                MeshArray.MeshComponents[i]->SetWorldScale3D(FVector(ScaleFactor));
                
                // Aplicar efeito de emissão baseado na fase
                // Isso seria implementado com materiais dinâmicos em uma versão completa
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::ApplyMapPhaseEffects - Efeitos aplicados para fase %d"), 
           (int32)CurrentMapPhase);
}

// ========================================
// FUNÇÕES INTERNAS - IA ADAPTATIVA
// ========================================

void AAURACRONPCGJungleSystem::InitializeAdaptiveSystem()
{
    if (!bAdaptiveSystemEnabled)
    {
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::InitializeAdaptiveSystem - Sistema adaptativo desabilitado"));
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::InitializeAdaptiveSystem - Inicializando sistema de IA adaptativa"));
    
    // Inicializar dados adaptativos
    AdaptiveData.PlayerProfile = EAURACRONJunglePlayerProfile::Balanced;
    AdaptiveData.AdaptiveBehavior = EAURACRONJungleAdaptiveBehavior::Standard;
    AdaptiveData.AdaptiveDifficulty = 1.0f;
    AdaptiveData.InvasionCount = 0;
    AdaptiveData.ObjectivesSecured = 0;
    AdaptiveData.TimeSinceLastAdaptation = 0.0f;
    
    // Limpar dados históricos
    AdaptiveData.CampInteractionCount.Empty();
    AdaptiveData.AverageClearTime.Empty();
    AdaptiveData.PreferredClearPath.Empty();
    
    // Inicializar dados para cada tipo de camp
    for (EAURACRONJungleCampType CampType : TEnumRange<EAURACRONJungleCampType>())
    {
        AdaptiveData.CampInteractionCount.Add(CampType, 0);
        AdaptiveData.AverageClearTime.Add(CampType, 0.0f);
    }
    
    // Configurar comportamento inicial para cada camp
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        JungleCamps[i].CurrentBehavior = EAURACRONJungleAdaptiveBehavior::Standard;
        JungleCamps[i].AdaptiveDamageMultiplier = 1.0f;
        JungleCamps[i].AdaptiveHealthMultiplier = 1.0f;
        JungleCamps[i].AdaptiveRewardMultiplier = 1.0f;
        JungleCamps[i].AdaptationPriority = 0.5f;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::InitializeAdaptiveSystem - Sistema inicializado com sucesso"));
}

void AAURACRONPCGJungleSystem::AnalyzePlayerPattern()
{
    if (!bAdaptiveSystemEnabled || AdaptiveData.CampInteractionCount.Num() == 0)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::AnalyzePlayerPattern - Analisando padrões de comportamento"));
    
    // Analisar frequência de interação com cada tipo de camp
    int32 TotalInteractions = 0;
    EAURACRONJungleCampType MostInteractedCamp = EAURACRONJungleCampType::SpectralPack;
    int32 MaxInteractions = 0;
    
    for (const TPair<EAURACRONJungleCampType, int32>& Pair : AdaptiveData.CampInteractionCount)
    {
        TotalInteractions += Pair.Value;
        
        if (Pair.Value > MaxInteractions)
        {
            MaxInteractions = Pair.Value;
            MostInteractedCamp = Pair.Key;
        }
    }
    
    // Analisar tempo médio de limpeza
    float AverageOverallClearTime = 0.0f;
    int32 CampTypesWithData = 0;
    
    for (const TPair<EAURACRONJungleCampType, float>& Pair : AdaptiveData.AverageClearTime)
    {
        if (Pair.Value > 0.0f)
        {
            AverageOverallClearTime += Pair.Value;
            CampTypesWithData++;
        }
    }
    
    if (CampTypesWithData > 0)
    {
        AverageOverallClearTime /= CampTypesWithData;
    }
    
    // Analisar padrão de rota
    bool bHasConsistentPath = false;
    if (AdaptiveData.PreferredClearPath.Num() >= 3)
    {
        // Verificar se o jogador segue uma rota consistente
        // Em uma implementação completa, isso usaria algoritmos mais sofisticados
        bHasConsistentPath = true;
    }
    
    // Determinar perfil do jogador com base nos dados analisados
    AdaptiveData.PlayerProfile = DeterminePlayerProfile();
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::AnalyzePlayerPattern - Perfil determinado: %d, Interações: %d, Tempo médio: %f"), 
           (int32)AdaptiveData.PlayerProfile, TotalInteractions, AverageOverallClearTime);
}

EAURACRONJunglePlayerProfile AAURACRONPCGJungleSystem::DeterminePlayerProfile()
{
    // Calcular pontuações para cada perfil com base nos dados coletados
    float AggressiveScore = 0.0f;
    float ControlScore = 0.0f;
    float FarmingScore = 0.0f;
    float BalancedScore = 0.0f;
    
    // Analisar interações com camps
    for (const TPair<EAURACRONJungleCampType, int32>& Pair : AdaptiveData.CampInteractionCount)
    {
        // Camps de buff (RadiantEssence, ChaosEssence) favorecem perfil agressivo
        if (Pair.Key == EAURACRONJungleCampType::RadiantEssence || Pair.Key == EAURACRONJungleCampType::ChaosEssence)
        {
            AggressiveScore += Pair.Value * 1.5f;
            ControlScore += Pair.Value * 0.5f;
        }
        // Camps de visão (FluxCrawler) favorecem perfil de controle
        else if (Pair.Key == EAURACRONJungleCampType::FluxCrawler)
        {
            ControlScore += Pair.Value * 2.0f;
        }
        // Outros camps favorecem perfil de farming
        else
        {
            FarmingScore += Pair.Value * 1.0f;
        }
    }
    
    // Analisar tempo de limpeza
    for (const TPair<EAURACRONJungleCampType, float>& Pair : AdaptiveData.AverageClearTime)
    {
        if (Pair.Value > 0.0f)
        {
            // Tempos mais rápidos favorecem perfil agressivo
            if (Pair.Value < 15.0f)
            {
                AggressiveScore += 2.0f;
            }
            // Tempos médios favorecem perfil balanceado
            else if (Pair.Value < 30.0f)
            {
                BalancedScore += 1.5f;
            }
            // Tempos lentos favorecem perfil de farming ou controle
            else
            {
                FarmingScore += 1.0f;
                ControlScore += 1.0f;
            }
        }
    }
    
    // Analisar invasões e objetivos
    AggressiveScore += AdaptiveData.InvasionCount * 3.0f;
    ControlScore += AdaptiveData.ObjectivesSecured * 2.0f;
    
    // Determinar perfil com maior pontuação
    float MaxScore = FMath::Max(FMath::Max(AggressiveScore, ControlScore), FMath::Max(FarmingScore, BalancedScore));
    
    if (MaxScore == AggressiveScore)
    {
        return EAURACRONJunglePlayerProfile::Aggressive;
    }
    else if (MaxScore == ControlScore)
    {
        return EAURACRONJunglePlayerProfile::Control;
    }
    else if (MaxScore == FarmingScore)
    {
        return EAURACRONJunglePlayerProfile::Farming;
    }
    else
    {
        return EAURACRONJunglePlayerProfile::Balanced;
    }
}

EAURACRONJungleAdaptiveBehavior AAURACRONPCGJungleSystem::CalculateAdaptiveBehavior(int32 CampIndex)
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num() || !bAdaptiveSystemEnabled)
    {
        return EAURACRONJungleAdaptiveBehavior::Standard;
    }
    
    FAURACRONJungleCampInfo& CampInfo = JungleCamps[CampIndex];
    
    // Calcular comportamento baseado no perfil do jogador e histórico de interações
    switch (AdaptiveData.PlayerProfile)
    {
    case EAURACRONJunglePlayerProfile::Aggressive:
        // Para jogadores agressivos, aumentar a dificuldade dos camps mais visitados
        if (AdaptiveData.CampInteractionCount.Contains(CampInfo.CampType) && 
            AdaptiveData.CampInteractionCount[CampInfo.CampType] > 3)
        {
            // Se o jogador visita muito este camp, torná-lo mais desafiador
            return EAURACRONJungleAdaptiveBehavior::Challenging;
        }
        // Para camps de buff, torná-los mais valiosos mas mais difíceis
        else if (CampInfo.CampType == EAURACRONJungleCampType::RadiantEssence || 
                 CampInfo.CampType == EAURACRONJungleCampType::ChaosEssence)
        {
            return EAURACRONJungleAdaptiveBehavior::Rewarding;
        }
        break;
        
    case EAURACRONJunglePlayerProfile::Control:
        // Para jogadores de controle, tornar camps estratégicos mais valiosos
        if (CampInfo.CampType == EAURACRONJungleCampType::FluxCrawler)
        {
            return EAURACRONJungleAdaptiveBehavior::Strategic;
        }
        // Tornar camps menos visitados mais atraentes
        else if (AdaptiveData.CampInteractionCount.Contains(CampInfo.CampType) && 
                 AdaptiveData.CampInteractionCount[CampInfo.CampType] < 2)
        {
            return EAURACRONJungleAdaptiveBehavior::Rewarding;
        }
        break;
        
    case EAURACRONJunglePlayerProfile::Farming:
        // Para jogadores de farming, tornar camps frequentemente visitados mais eficientes
        if (AdaptiveData.CampInteractionCount.Contains(CampInfo.CampType) && 
            AdaptiveData.CampInteractionCount[CampInfo.CampType] > 5)
        {
            return EAURACRONJungleAdaptiveBehavior::Efficient;
        }
        // Tornar camps não visitados mais atraentes
        else if (!AdaptiveData.CampInteractionCount.Contains(CampInfo.CampType) || 
                 AdaptiveData.CampInteractionCount[CampInfo.CampType] == 0)
        {
            return EAURACRONJungleAdaptiveBehavior::Rewarding;
        }
        break;
        
    case EAURACRONJunglePlayerProfile::Balanced:
    default:
        // Para jogadores balanceados, manter um equilíbrio com pequenas variações
        // Adicionar alguma variação baseada na fase do mapa
        if ((int32)CurrentMapPhase >= 2) // Fases mais avançadas
        {
            // Alternar entre comportamentos para manter o interesse
            int32 BehaviorIndex = (CampIndex + (int32)CurrentMapPhase) % 4;
            switch (BehaviorIndex)
            {
            case 0: return EAURACRONJungleAdaptiveBehavior::Standard;
            case 1: return EAURACRONJungleAdaptiveBehavior::Challenging;
            case 2: return EAURACRONJungleAdaptiveBehavior::Rewarding;
            case 3: return EAURACRONJungleAdaptiveBehavior::Strategic;
            }
        }
        break;
    }
    
    // Comportamento padrão se nenhuma regra específica for aplicada
    return EAURACRONJungleAdaptiveBehavior::Standard;
}

void AAURACRONPCGJungleSystem::ApplyAdaptiveBehavior(int32 CampIndex, EAURACRONJungleAdaptiveBehavior Behavior)
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        return;
    }
    
    FAURACRONJungleCampInfo& CampInfo = JungleCamps[CampIndex];
    CampInfo.CurrentBehavior = Behavior;
    
    // Aplicar modificações baseadas no comportamento
    switch (Behavior)
    {
    case EAURACRONJungleAdaptiveBehavior::Standard:
        // Valores padrão
        CampInfo.AdaptiveDamageMultiplier = 1.0f;
        CampInfo.AdaptiveHealthMultiplier = 1.0f;
        CampInfo.AdaptiveRewardMultiplier = 1.0f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Challenging:
        // Mais difícil, recompensas um pouco maiores
        CampInfo.AdaptiveDamageMultiplier = 1.3f;
        CampInfo.AdaptiveHealthMultiplier = 1.5f;
        CampInfo.AdaptiveRewardMultiplier = 1.2f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Rewarding:
        // Recompensas significativamente maiores
        CampInfo.AdaptiveDamageMultiplier = 1.1f;
        CampInfo.AdaptiveHealthMultiplier = 1.2f;
        CampInfo.AdaptiveRewardMultiplier = 1.5f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Efficient:
        // Mais fácil de limpar, recompensas normais
        CampInfo.AdaptiveDamageMultiplier = 0.8f;
        CampInfo.AdaptiveHealthMultiplier = 0.7f;
        CampInfo.AdaptiveRewardMultiplier = 1.0f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Strategic:
        // Recompensas estratégicas (visão, buffs especiais)
        CampInfo.AdaptiveDamageMultiplier = 1.0f;
        CampInfo.AdaptiveHealthMultiplier = 1.0f;
        CampInfo.AdaptiveRewardMultiplier = 1.3f;
        // Em uma implementação completa, adicionaria buffs especiais aqui
        break;
    }
    
    // Aplicar multiplicador de dificuldade global
    CampInfo.AdaptiveDamageMultiplier *= AdaptiveData.AdaptiveDifficulty;
    CampInfo.AdaptiveHealthMultiplier *= AdaptiveData.AdaptiveDifficulty;
    
    // Calcular prioridade de adaptação
    CampInfo.AdaptationPriority = CalculateAdaptationPriority(CampIndex);
    
    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGJungleSystem::ApplyAdaptiveBehavior - Camp %d: Comportamento %d aplicado"), 
           CampIndex, (int32)Behavior);
}

float AAURACRONPCGJungleSystem::CalculateAdaptationPriority(int32 CampIndex) const
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num())
    {
        return 0.5f;
    }
    
    const FAURACRONJungleCampInfo& CampInfo = JungleCamps[CampIndex];
    float Priority = 0.5f; // Valor base
    
    // Aumentar prioridade para camps frequentemente visitados
    if (AdaptiveData.CampInteractionCount.Contains(CampInfo.CampType))
    {
        int32 InteractionCount = AdaptiveData.CampInteractionCount[CampInfo.CampType];
        Priority += FMath::Min(InteractionCount * 0.05f, 0.3f);
    }
    
    // Aumentar prioridade para camps estratégicos
    if (CampInfo.CampType == EAURACRONJungleCampType::RadiantEssence || 
        CampInfo.CampType == EAURACRONJungleCampType::ChaosEssence || 
        CampInfo.CampType == EAURACRONJungleCampType::FluxCrawler)
    {
        Priority += 0.1f;
    }
    
    // Aumentar prioridade baseado na fase do mapa
    Priority += (int32)CurrentMapPhase * 0.05f;
    
    // Limitar ao intervalo [0.1, 1.0]
    return FMath::Clamp(Priority, 0.1f, 1.0f);
}

void AAURACRONPCGJungleSystem::UpdateAdaptiveData(float DeltaTime)
{
    if (!bAdaptiveSystemEnabled)
    {
        return;
    }
    
    // Atualizar tempo desde a última adaptação
    AdaptiveData.TimeSinceLastAdaptation += DeltaTime;
    
    // Verificar se é hora de adaptar
    if (ShouldAdapt())
    {
        // Analisar padrões do jogador
        AnalyzePlayerPattern();
        
        // Adaptar comportamento dos camps
        AdaptCampBehaviors();
        
        // Resetar timer
        AdaptiveData.TimeSinceLastAdaptation = 0.0f;
        
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::UpdateAdaptiveData - Adaptação realizada"));
    }
}

bool AAURACRONPCGJungleSystem::ShouldAdapt() const
{
    // Adaptar se passou tempo suficiente desde a última adaptação
    if (AdaptiveData.TimeSinceLastAdaptation >= AdaptiveSystemConfig.AdaptationInterval)
    {
        return true;
    }
    
    // Adaptar se houve mudanças significativas no comportamento do jogador
    if (AdaptiveData.InvasionCount >= AdaptiveSystemConfig.InvasionThreshold || 
        AdaptiveData.ObjectivesSecured >= AdaptiveSystemConfig.ObjectiveThreshold)
    {
        return true;
    }
    
    return false;
}

void AAURACRONPCGJungleSystem::RegisterClearPath(int32 CampIndex)
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num() || !bAdaptiveSystemEnabled)
    {
        return;
    }
    
    // Adicionar tipo de camp à rota preferida
    EAURACRONJungleCampType CampType = JungleCamps[CampIndex].CampType;
    AdaptiveData.PreferredClearPath.Add(CampType);
    
    // Manter apenas os últimos 10 camps na rota
    if (AdaptiveData.PreferredClearPath.Num() > 10)
    {
        AdaptiveData.PreferredClearPath.RemoveAt(0);
    }
    
    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGJungleSystem::RegisterClearPath - Camp %d adicionado à rota"), CampIndex);
}

void AAURACRONPCGJungleSystem::AdaptCampBehaviors()
{
    if (!bAdaptiveSystemEnabled || JungleCamps.Num() == 0)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::AdaptCampBehaviors - Adaptando comportamento dos camps"));
    
    // Ordenar camps por prioridade de adaptação (opcional em uma implementação completa)
    // Isso permitiria focar a adaptação nos camps mais importantes primeiro
    
    // Adaptar cada camp com base no perfil do jogador
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        // Calcular comportamento adaptativo para este camp
        EAURACRONJungleAdaptiveBehavior NewBehavior = CalculateAdaptiveBehavior(i);
        
        // Aplicar o comportamento calculado
        ApplyAdaptiveBehavior(i, NewBehavior);
    }
    
    // Ajustar dificuldade global com base na fase do mapa e perfil do jogador
    float DifficultyMultiplier = 1.0f;
    
    // Aumentar dificuldade com base na fase do mapa
    DifficultyMultiplier += (int32)CurrentMapPhase * 0.1f;
    
    // Ajustar com base no perfil do jogador
    switch (AdaptiveData.PlayerProfile)
    {
    case EAURACRONJunglePlayerProfile::Aggressive:
        // Jogadores agressivos recebem desafios maiores
        DifficultyMultiplier += 0.2f;
        break;
        
    case EAURACRONJunglePlayerProfile::Control:
        // Jogadores de controle recebem desafios estratégicos
        DifficultyMultiplier += 0.1f;
        break;
        
    case EAURACRONJunglePlayerProfile::Farming:
        // Jogadores de farming recebem desafios menores
        DifficultyMultiplier -= 0.1f;
        break;
        
    case EAURACRONJunglePlayerProfile::Balanced:
    default:
        // Sem ajuste adicional
        break;
    }
    
    // Limitar o multiplicador de dificuldade
    AdaptiveData.AdaptiveDifficulty = FMath::Clamp(DifficultyMultiplier, 0.5f, 2.0f);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::AdaptCampBehaviors - Dificuldade global ajustada para %f"), 
           AdaptiveData.AdaptiveDifficulty);
}

void AAURACRONPCGJungleSystem::RecordCampInteraction(int32 CampIndex, float ClearTime)
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num() || !bAdaptiveSystemEnabled)
    {
        return;
    }
    
    EAURACRONJungleCampType CampType = JungleCamps[CampIndex].CampType;
    
    // Incrementar contador de interações
    if (!AdaptiveData.CampInteractionCount.Contains(CampType))
    {
        AdaptiveData.CampInteractionCount.Add(CampType, 0);
    }
    AdaptiveData.CampInteractionCount[CampType]++;
    
    // Atualizar tempo médio de limpeza
    if (!AdaptiveData.AverageClearTime.Contains(CampType))
    {
        AdaptiveData.AverageClearTime.Add(CampType, ClearTime);
    }
    else
    {
        // Média ponderada (70% do valor anterior, 30% do novo valor)
        float CurrentAverage = AdaptiveData.AverageClearTime[CampType];
        float NewAverage = (CurrentAverage * 0.7f) + (ClearTime * 0.3f);
        AdaptiveData.AverageClearTime[CampType] = NewAverage;
    }
    
    // Registrar na rota de limpeza
    RegisterClearPath(CampIndex);
    
    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGJungleSystem::RecordCampInteraction - Camp %d interação registrada, tempo: %f"), 
           CampIndex, ClearTime);
}

void AAURACRONPCGJungleSystem::RecordInvasion()
{
    if (!bAdaptiveSystemEnabled)
    {
        return;
    }
    
    AdaptiveData.InvasionCount++;
    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGJungleSystem::RecordInvasion - Invasão registrada: %d"), 
           AdaptiveData.InvasionCount);
}

void AAURACRONPCGJungleSystem::RecordObjectiveSecured()
{
    if (!bAdaptiveSystemEnabled)
    {
        return;
    }
    
    AdaptiveData.ObjectivesSecured++;
    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGJungleSystem::RecordObjectiveSecured - Objetivo registrado: %d"), 
           AdaptiveData.ObjectivesSecured);
}

void AAURACRONPCGJungleSystem::CalculateAdaptiveMultipliers(int32 CampIndex)
{
    if (CampIndex < 0 || CampIndex >= JungleCamps.Num() || !bAdaptiveSystemEnabled)
    {
        return;
    }
    
    FAURACRONJungleCampInfo& CampInfo = JungleCamps[CampIndex];
    
    // Calcular multiplicadores base com base no comportamento atual
    switch (CampInfo.CurrentBehavior)
    {
    case EAURACRONJungleAdaptiveBehavior::Standard:
        CampInfo.AdaptiveDamageMultiplier = 1.0f;
        CampInfo.AdaptiveHealthMultiplier = 1.0f;
        CampInfo.AdaptiveRewardMultiplier = 1.0f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Challenging:
        CampInfo.AdaptiveDamageMultiplier = 1.3f;
        CampInfo.AdaptiveHealthMultiplier = 1.5f;
        CampInfo.AdaptiveRewardMultiplier = 1.2f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Rewarding:
        CampInfo.AdaptiveDamageMultiplier = 1.1f;
        CampInfo.AdaptiveHealthMultiplier = 1.2f;
        CampInfo.AdaptiveRewardMultiplier = 1.5f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Efficient:
        CampInfo.AdaptiveDamageMultiplier = 0.8f;
        CampInfo.AdaptiveHealthMultiplier = 0.7f;
        CampInfo.AdaptiveRewardMultiplier = 1.0f;
        break;
        
    case EAURACRONJungleAdaptiveBehavior::Strategic:
        CampInfo.AdaptiveDamageMultiplier = 1.0f;
        CampInfo.AdaptiveHealthMultiplier = 1.0f;
        CampInfo.AdaptiveRewardMultiplier = 1.3f;
        break;
    }
    
    // Ajustar com base na fase do mapa
    float PhaseMultiplier = 1.0f + ((int32)CurrentMapPhase * 0.1f);
    CampInfo.AdaptiveDamageMultiplier *= PhaseMultiplier;
    CampInfo.AdaptiveHealthMultiplier *= PhaseMultiplier;
    
    // Ajustar com base na dificuldade global
    CampInfo.AdaptiveDamageMultiplier *= AdaptiveData.AdaptiveDifficulty;
    CampInfo.AdaptiveHealthMultiplier *= AdaptiveData.AdaptiveDifficulty;
    
    // Ajustar com base no ambiente
    switch (CurrentEnvironment)
    {
    case EAURACRONEnvironmentType::ZephyrFirmament:
        // Ambiente aéreo - inimigos mais rápidos mas menos resistentes
        CampInfo.AdaptiveDamageMultiplier *= 1.2f;
        CampInfo.AdaptiveHealthMultiplier *= 0.9f;
        break;
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        // Ambiente sombrio - inimigos mais resistentes e perigosos
        CampInfo.AdaptiveDamageMultiplier *= 1.3f;
        CampInfo.AdaptiveHealthMultiplier *= 1.2f;
        break;
        
    case EAURACRONEnvironmentType::PrismaticNexus:
        // Ambiente mágico - recompensas maiores
        CampInfo.AdaptiveRewardMultiplier *= 1.3f;
        break;
        
    case EAURACRONEnvironmentType::CelestialBasin:
    default:
        // Ambiente padrão - sem modificações
        break;
    }
    
    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGJungleSystem::CalculateAdaptiveMultipliers - Camp %d: Dano %.2f, Vida %.2f, Recompensa %.2f"), 
           CampIndex, CampInfo.AdaptiveDamageMultiplier, CampInfo.AdaptiveHealthMultiplier, CampInfo.AdaptiveRewardMultiplier);
}

void AAURACRONPCGJungleSystem::PredictStrategy(const TArray<FString>& TeamComposition)
{
    if (!bAdaptiveSystemEnabled)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::PredictStrategy - Prevendo estratégia baseada na composição da equipe"));
    
    // Analisar composição da equipe para prever estratégia
    bool bHasAssassin = false;
    bool bHasTank = false;
    bool bHasMage = false;
    bool bHasSupport = false;
    bool bHasMarksman = false;
    
    // Classificar composição da equipe
    for (const FString& Champion : TeamComposition)
    {
        // Simplificado para demonstração - em uma implementação real, 
        // isso seria baseado em dados reais dos campeões
        if (Champion.Contains("Assassin") || Champion.Contains("Slayer"))
        {
            bHasAssassin = true;
        }
        else if (Champion.Contains("Tank") || Champion.Contains("Juggernaut"))
        {
            bHasTank = true;
        }
        else if (Champion.Contains("Mage") || Champion.Contains("Battlemage"))
        {
            bHasMage = true;
        }
        else if (Champion.Contains("Support") || Champion.Contains("Enchanter"))
        {
            bHasSupport = true;
        }
        else if (Champion.Contains("Marksman") || Champion.Contains("ADC"))
        {
            bHasMarksman = true;
        }
    }
    
    // Prever estratégia baseada na composição
    if (bHasAssassin && !bHasTank)
    {   
        // Equipe com assassinos tende a focar em invasões e ganks
        AdaptiveData.PredictedStrategy = EAURACRONJungleStrategy::Aggressive;
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::PredictStrategy - Estratégia prevista: Agressiva"));
        
        // Adaptar camps para contra-estratégia
        for (int32 i = 0; i < JungleCamps.Num(); ++i)
        {
            if (JungleCamps[i].CampType == EAURACRONJungleCampType::RadiantEssence || 
                JungleCamps[i].CampType == EAURACRONJungleCampType::ChaosEssence)
            {
                // Tornar buffs mais desafiadores para equipes agressivas
                JungleCamps[i].AdaptiveHealthMultiplier = 1.3f;
                JungleCamps[i].AdaptiveDamageMultiplier = 1.2f;
            }
        }
    }
    else if (bHasTank && bHasSupport)
    {
        // Equipe com tanks e suportes tende a focar em objetivos
        AdaptiveData.PredictedStrategy = EAURACRONJungleStrategy::Objective;
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::PredictStrategy - Estratégia prevista: Objetivos"));
        
        // Adaptar camps para contra-estratégia
        for (int32 i = 0; i < JungleCamps.Num(); ++i)
        {
            if (JungleCamps[i].CampType == EAURACRONJungleCampType::FluxCrawler || 
                JungleCamps[i].CampType == EAURACRONJungleCampType::VoidHarpy)
            {
                // Aumentar recompensas para incentivar farming
                JungleCamps[i].AdaptiveRewardMultiplier = 1.4f;
            }
        }
    }
    else if (bHasMage && bHasMarksman)
    {
        // Equipe com magos e atiradores tende a focar em farming
        AdaptiveData.PredictedStrategy = EAURACRONJungleStrategy::Farming;
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::PredictStrategy - Estratégia prevista: Farming"));
        
        // Adaptar camps para contra-estratégia
        for (int32 i = 0; i < JungleCamps.Num(); ++i)
        {
            if (JungleCamps[i].CampType == EAURACRONJungleCampType::ShadowWolf || 
                JungleCamps[i].CampType == EAURACRONJungleCampType::CrystalGolem)
            {
                // Tornar camps menores mais recompensadores
                JungleCamps[i].AdaptiveRewardMultiplier = 1.3f;
                JungleCamps[i].AdaptiveHealthMultiplier = 0.9f;
            }
        }
    }
    else
    {
        // Equipe balanceada ou indefinida
        AdaptiveData.PredictedStrategy = EAURACRONJungleStrategy::Balanced;
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::PredictStrategy - Estratégia prevista: Balanceada"));
        
        // Manter configurações padrão
        for (int32 i = 0; i < JungleCamps.Num(); ++i)
        {
            JungleCamps[i].AdaptiveHealthMultiplier = 1.0f;
            JungleCamps[i].AdaptiveDamageMultiplier = 1.0f;
            JungleCamps[i].AdaptiveRewardMultiplier = 1.0f;
        }
    }
    
    // Gerar objetivos dinâmicos baseados na estratégia prevista
    GenerateDynamicObjectives();
}

void AAURACRONPCGJungleSystem::GenerateDynamicObjectives()
{
    if (!bAdaptiveSystemEnabled)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::GenerateDynamicObjectives - Gerando objetivos dinâmicos"));
    
    // Buscar referência ao sistema de objetivos
    AAURACRONPCGObjectiveSystem* ObjectiveSystem = nullptr;
    TArray<AActor*> FoundActors;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), AAURACRONPCGObjectiveSystem::StaticClass(), FoundActors);
    
    if (FoundActors.Num() > 0)
    {
        ObjectiveSystem = Cast<AAURACRONPCGObjectiveSystem>(FoundActors[0]);
    }
    
    if (!ObjectiveSystem)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGJungleSystem::GenerateDynamicObjectives - Sistema de objetivos não encontrado"));
        return;
    }
    
    // Gerar objetivos dinâmicos baseados na estratégia prevista
    switch (AdaptiveData.PredictedStrategy)
    {
    case EAURACRONJungleStrategy::Aggressive:
        // Para estratégia agressiva, gerar objetivos de captura rápida
        ObjectiveSystem->ForceGenerateObjective(EAURACRONObjectiveType::CapturePoint);
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::GenerateDynamicObjectives - Gerado objetivo de captura para estratégia agressiva"));
        break;
        
    case EAURACRONJungleStrategy::Objective:
        // Para estratégia de objetivos, gerar objetivos de alto valor
        ObjectiveSystem->ForceGenerateObjective(EAURACRONObjectiveType::PowerCore);
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::GenerateDynamicObjectives - Gerado objetivo de poder para estratégia de objetivos"));
        break;
        
    case EAURACRONJungleStrategy::Farming:
        // Para estratégia de farming, gerar nós de recursos
        ObjectiveSystem->ForceGenerateObjective(EAURACRONObjectiveType::ResourceNode);
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::GenerateDynamicObjectives - Gerado nó de recursos para estratégia de farming"));
        break;
        
    case EAURACRONJungleStrategy::Balanced:
    default:
        // Para estratégia balanceada, gerar objetivos variados
        ObjectiveSystem->ForceGenerateObjective(EAURACRONObjectiveType::AncientRelic);
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::GenerateDynamicObjectives - Gerado relíquia antiga para estratégia balanceada"));
        break;
    }
}

void AAURACRONPCGJungleSystem::AdaptSpawnsBasedOnClearPatterns()
{
    if (!bAdaptiveSystemEnabled || AdaptiveData.PreferredClearPath.Num() < 3)
    {
        return;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::AdaptSpawnsBasedOnClearPatterns - Adaptando spawns baseado em padrões de clear"));
    
    // Analisar padrão de clear para identificar camps frequentemente ignorados
    TMap<EAURACRONJungleCampType, int32> ClearCounts;
    
    // Inicializar contagem para todos os tipos de camp
    for (int32 i = 0; i < (int32)EAURACRONJungleCampType::MAX; ++i)
    {
        ClearCounts.Add((EAURACRONJungleCampType)i, 0);
    }
    
    // Contar ocorrências na rota de clear
    for (const EAURACRONJungleCampType& CampType : AdaptiveData.PreferredClearPath)
    {
        if (ClearCounts.Contains(CampType))
        {
            ClearCounts[CampType]++;
        }
    }
    
    // Identificar camps menos visitados
    TArray<EAURACRONJungleCampType> LeastVisitedCamps;
    int32 MinVisits = TNumericLimits<int32>::Max();
    
    for (const auto& Pair : ClearCounts)
    {
        if (Pair.Value < MinVisits && Pair.Value >= 0)
        {
            MinVisits = Pair.Value;
        }
    }
    
    for (const auto& Pair : ClearCounts)
    {
        if (Pair.Value == MinVisits)
        {
            LeastVisitedCamps.Add(Pair.Key);
        }
    }
    
    // Adaptar camps menos visitados para torná-los mais atraentes
    for (int32 i = 0; i < JungleCamps.Num(); ++i)
    {
        if (LeastVisitedCamps.Contains(JungleCamps[i].CampType))
        {
            // Aumentar recompensas para incentivar visitas
            JungleCamps[i].AdaptiveRewardMultiplier = 1.5f;
            
            // Reduzir dificuldade para facilitar clear
            JungleCamps[i].AdaptiveHealthMultiplier = 0.8f;
            JungleCamps[i].AdaptiveDamageMultiplier = 0.9f;
            
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::AdaptSpawnsBasedOnClearPatterns - Camp %d adaptado para ser mais atraente"), i);
        }
    }
}

void AAURACRONPCGJungleSystem::ConfigureWorldPartitionStreaming()
{
    // Configurar streaming para World Partition
    // Esta função seria implementada em um projeto que utiliza World Partition
    // para gerenciar o streaming de conteúdo baseado na posição do jogador
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::ConfigureWorldPartitionStreaming - Configurando streaming"));
    
    // Em uma implementação completa, isso configuraria:
    // 1. Distâncias de streaming para os camps
    // 2. Prioridades de streaming baseadas no comportamento adaptativo
    // 3. Integração com o sistema de World Partition
    
    // Exemplo de pseudocódigo:
    /*
    if (UWorldPartitionSubsystem* WPSubsystem = GetWorld()->GetSubsystem<UWorldPartitionSubsystem>())
    {
        // Configurar streaming cells para os camps
        for (int32 i = 0; i < JungleCamps.Num(); ++i)
        {
            FVector CampLocation = JungleCamps[i].Position;
            float StreamingPriority = JungleCamps[i].AdaptationPriority;
            
            // Ajustar prioridade de streaming baseado na importância adaptativa
            WPSubsystem->SetStreamingPriority(CampLocation, StreamingPriority);
        }
    }
    */
}

void AAURACRONPCGJungleSystem::AssociateWithDataLayer()
{
    // Associar sistema com Data Layer para gerenciamento de conteúdo
    // Esta função seria implementada em um projeto que utiliza Data Layers
    // para organizar e gerenciar conteúdo do mundo
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGJungleSystem::AssociateWithDataLayer - Associando com Data Layer"));
    
    // Em uma implementação completa, isso:
    // 1. Associaria os camps com Data Layers específicas baseadas no ambiente
    // 2. Permitiria ativar/desativar grupos de camps baseados em eventos do jogo
    // 3. Organizaria o conteúdo para melhor gerenciamento no editor
    
    // Exemplo de pseudocódigo:
    /*
    if (UDataLayerSubsystem* DataLayerSubsystem = GetWorld()->GetSubsystem<UDataLayerSubsystem>())
    {
        // Criar ou obter Data Layer para cada ambiente
        for (EAURACRONEnvironmentType Environment : TEnumRange<EAURACRONEnvironmentType>())
        {
            FString LayerName = FString::Printf(TEXT("JungleCamps_%s"), *UEnum::GetValueAsString(Environment));
            UDataLayerAsset* DataLayer = DataLayerSubsystem->GetDataLayerFromName(FName(*LayerName));
            
            if (DataLayer)
            {
                // Associar camps deste ambiente com a Data Layer
                for (int32 i = 0; i < JungleCamps.Num(); ++i)
                {
                    if (JungleCamps[i].Environment == Environment)
                    {
                        // Associar actor do camp com a Data Layer
                        if (CampActors.IsValidIndex(i) && CampActors[i])
                        {
                            DataLayerSubsystem->AddActorToDataLayer(CampActors[i], DataLayer);
                        }
                    }
                }
            }
        }
    }
    */
}
