// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "DamageZoneComponent.generated.h"

/**
 * Componente responsável por aplicar dano a jogadores que saem da área segura do mapa
 * durante a fase de Resolução quando ocorre a contração do mapa.
 */
UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class AURACRON_API UDamageZoneComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    // Sets default values for this component's properties
    UDamageZoneComponent();

    // Called every frame
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    /**
     * Define o raio da área segura onde jogadores não recebem dano
     * @param NewRadius - Novo raio da área segura em unidades do mundo
     */
    UFUNCTION(BlueprintCallable, Category = "Damage Zone")
    void SetSafeRadius(float NewRadius);

    /**
     * Define a quantidade de dano aplicado por segundo aos jogadores fora da área segura
     * @param NewDamagePerSecond - Novo valor de dano por segundo
     */
    UFUNCTION(BlueprintCallable, Category = "Damage Zone")
    void SetDamagePerSecond(float NewDamagePerSecond);

    /**
     * Define o fator de escala do dano conforme o jogador se afasta da área segura
     * @param NewScalingFactor - Novo fator de escala (1.0 = sem escala)
     */
    UFUNCTION(BlueprintCallable, Category = "Damage Zone")
    void SetDamageScalingFactor(float NewScalingFactor);

    /**
     * Define o raio da área de aviso, onde jogadores recebem alertas visuais
     * @param NewWarningRadius - Novo raio da área de aviso em unidades do mundo
     */
    UFUNCTION(BlueprintCallable, Category = "Damage Zone")
    void SetWarningRadius(float NewWarningRadius);

    /**
     * Ativa ou desativa o componente de zona de dano
     * @param bNewActive - Novo estado de ativação
     */
    UFUNCTION(BlueprintCallable, Category = "Damage Zone")
    void SetActive(bool bNewActive);

protected:
    // Called when the game starts
    virtual void BeginPlay() override;

    /** Raio da área segura onde jogadores não recebem dano */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone")
    float SafeRadius;

    /** Raio da área de aviso, onde jogadores recebem alertas visuais */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone")
    float WarningRadius;

    /** Quantidade de dano aplicado por segundo aos jogadores fora da área segura */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone")
    float DamagePerSecond;

    /** Fator de escala do dano conforme o jogador se afasta da área segura */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone")
    float DamageScalingFactor;

    /** Determina se o componente está ativo */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone")
    bool bIsActive;

    /** Tempo acumulado desde o último tick de dano */
    UPROPERTY()
    float AccumulatedTime;

    /** Intervalo entre aplicações de dano em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Damage Zone")
    float DamageInterval;

    /** Aplica dano aos jogadores fora da área segura */
    void ApplyDamageToPlayersOutsideSafeZone(float DeltaTime);

    /** Verifica se um jogador está fora da área segura */
    bool IsPlayerOutsideSafeZone(AActor* Player) const;

    /** Calcula o multiplicador de dano baseado na distância do jogador da área segura */
    float CalculateDamageMultiplier(AActor* Player) const;

    /** Obtém todos os jogadores no mundo */
    TArray<AActor*> GetAllPlayers() const;

    /** Centro da zona de dano, normalmente o centro do mapa */
    UPROPERTY()
    FVector ZoneCenter;
};