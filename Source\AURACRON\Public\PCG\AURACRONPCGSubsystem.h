// AURACRONPCGSubsystem.h
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Subsistema principal para gerenciar a geração procedural do mapa

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
// Forward declaration para evitar dependência direta
class UPCGComponent;
#include "PCGSettings.h"
#include "PCGPoint.h"
#include "PCGVolume.h"
#include "PCG/AURACRONPCGTypes.h"
#include "AURACRONPCGSubsystem.generated.h"

class UPCGComponent;
class APCGVolume;

UENUM(BlueprintType)
enum class EAURACRONTrailType : uint8
{
    None            UMETA(DisplayName = "None"),
    Solar           UMETA(DisplayName = "Solar Trail"),
    Axis            UMETA(DisplayName = "Axis Trail"),
    Lunar           UMETA(DisplayName = "Lunar Trail"),
    PrismalFlow     UMETA(DisplayName = "Prismal Flow Trail"),
    EtherealPath    UMETA(DisplayName = "Ethereal Path Trail"),
    NexusConnection UMETA(DisplayName = "Nexus Connection Trail"),
};

UENUM(BlueprintType)
enum class EAURACRONIslandType : uint8
{
    None        UMETA(DisplayName = "None"),
    Nexus       UMETA(DisplayName = "Nexus Island"),
    Sanctuary   UMETA(DisplayName = "Sanctuary Island"),
    Arsenal     UMETA(DisplayName = "Arsenal Island"),
    Chaos       UMETA(DisplayName = "Chaos Island"),
    Battlefield UMETA(DisplayName = "Battlefield Island"),
};

/**
 * Subsistema para gerenciar a geração procedural do mapa AURACRON
 * Responsável por coordenar os diferentes ambientes, trilhas e o Prismal Flow
 */
UCLASS()
class AURACRON_API UAURACRONPCGSubsystem : public UWorldSubsystem
{
    GENERATED_BODY()

public:
    UAURACRONPCGSubsystem();

    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    virtual bool ShouldCreateSubsystem(UObject* Outer) const override;

    // Funções para iniciar a geração do mapa
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void GenerateMap(FVector MapCenter, float MapRadius);

    // Funções para gerenciar as fases do mapa
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void AdvanceToNextPhase();

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void SetMapPhase(EAURACRONMapPhase NewPhase);

    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG")
    EAURACRONMapPhase GetCurrentMapPhase() const { return CurrentMapPhase; }

    // Funções para gerenciar os ambientes
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void GenerateEnvironment(EAURACRONEnvironmentType EnvironmentType, FVector Center, float Radius);

    // Funções para gerenciar as trilhas
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void GenerateTrail(EAURACRONTrailType TrailType, TArray<FVector> ControlPoints);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void UpdateTrailPositions(float DeltaTime);

    // Funções para gerenciar o Prismal Flow
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void GeneratePrismalFlow(TArray<FVector> FlowControlPoints, float Width);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void UpdatePrismalFlow(float DeltaTime);

    // Funções para gerenciar as ilhas
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG")
    void GenerateIsland(EAURACRONIslandType IslandType, FVector Location, float Radius);

    /** Obter nível de qualidade atual */
    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Quality")
    int32 GetCurrentQualityLevel() const;

private:
    // Componentes PCG para cada ambiente
    UPROPERTY()
    TMap<EAURACRONEnvironmentType, UPCGComponent*> EnvironmentComponents;

    // Componentes PCG para as trilhas
    UPROPERTY()
    TMap<EAURACRONTrailType, UPCGComponent*> TrailComponents;

    // Componente PCG para o Prismal Flow
    UPROPERTY()
    UPCGComponent* PrismalFlowComponent;

    // Volumes PCG para cada ambiente
    UPROPERTY()
    TMap<EAURACRONEnvironmentType, APCGVolume*> EnvironmentVolumes;

    // Fase atual do mapa
    UPROPERTY()
    EAURACRONMapPhase CurrentMapPhase;

    // Tempo decorrido desde o início da partida
    UPROPERTY()
    float ElapsedTime;

    // Funções internas para geração procedural
    void SetupPCGComponents();
    void UpdateEnvironmentBasedOnPhase();
    void UpdateTrailsBasedOnPhase();
    void UpdatePrismalFlowBasedOnPhase();
};