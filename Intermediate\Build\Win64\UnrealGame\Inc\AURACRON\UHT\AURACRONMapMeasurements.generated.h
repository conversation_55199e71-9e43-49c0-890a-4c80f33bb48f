// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONMapMeasurements.h"

#ifdef AURACRON_AURACRONMapMeasurements_generated_h
#error "AURACRONMapMeasurements.generated.h already included, missing '#pragma once' in AURACRONMapMeasurements.h"
#endif
#define AURACRON_AURACRONMapMeasurements_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FAURACRONMapDimensions ********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h_30_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAURACRONMapDimensions_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAURACRONMapDimensions;
// ********** End ScriptStruct FAURACRONMapDimensions **********************************************

// ********** Begin Class UAURACRONMapMeasurements *************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h_452_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetClosestLane); \
	DECLARE_FUNCTION(execIsPositionInLane); \
	DECLARE_FUNCTION(execGetLanePosition); \
	DECLARE_FUNCTION(execGetBasePositions); \
	DECLARE_FUNCTION(execGetStrategicObjectivePositions); \
	DECLARE_FUNCTION(execGetTowerPositions); \
	DECLARE_FUNCTION(execGetJungleCampPositions); \
	DECLARE_FUNCTION(execGetBotLanePoints); \
	DECLARE_FUNCTION(execGetMidLanePoints); \
	DECLARE_FUNCTION(execGetTopLanePoints); \
	DECLARE_FUNCTION(execGetMapScaleFactorForPhase); \
	DECLARE_FUNCTION(execGetMapPhaseFromTime); \
	DECLARE_FUNCTION(execGetDistanceInMeters); \
	DECLARE_FUNCTION(execIsPositionWithinMapBounds); \
	DECLARE_FUNCTION(execGetIslandPositions); \
	DECLARE_FUNCTION(execGetTrailPositions); \
	DECLARE_FUNCTION(execGetPrismalFlowWidth); \
	DECLARE_FUNCTION(execGetPrismalFlowPosition); \
	DECLARE_FUNCTION(execGetEnvironmentRadius); \
	DECLARE_FUNCTION(execGetEnvironmentCenter); \
	DECLARE_FUNCTION(execUnrealUnitsToMeters); \
	DECLARE_FUNCTION(execMetersToUnrealUnits);


AURACRON_API UClass* Z_Construct_UClass_UAURACRONMapMeasurements_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h_452_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAURACRONMapMeasurements(); \
	friend struct Z_Construct_UClass_UAURACRONMapMeasurements_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_UAURACRONMapMeasurements_NoRegister(); \
public: \
	DECLARE_CLASS2(UAURACRONMapMeasurements, UBlueprintFunctionLibrary, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_UAURACRONMapMeasurements_NoRegister) \
	DECLARE_SERIALIZER(UAURACRONMapMeasurements)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h_452_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UAURACRONMapMeasurements(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UAURACRONMapMeasurements(UAURACRONMapMeasurements&&) = delete; \
	UAURACRONMapMeasurements(const UAURACRONMapMeasurements&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAURACRONMapMeasurements); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAURACRONMapMeasurements); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAURACRONMapMeasurements) \
	NO_API virtual ~UAURACRONMapMeasurements();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h_449_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h_452_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h_452_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h_452_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h_452_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UAURACRONMapMeasurements;

// ********** End Class UAURACRONMapMeasurements ***************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONMapMeasurements_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
