// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGObjectiveSystem.h"
#include "Engine/TimerHandle.h"
#include "PCG/AURACRONPCGJungleSystem.h"
#include "PCG/AURACRONPCGUtility.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGObjectiveSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGObjectiveSystem();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGObjectiveSystem_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature();
AURACRON_API UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMeshComponentArray();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONObjectiveInfo();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGActorReferences();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONProceduralObjective();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UBoxComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAURACRONObjectiveType ****************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONObjectiveType;
static UEnum* EAURACRONObjectiveType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONObjectiveType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONObjectiveType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONObjectiveType>()
{
	return EAURACRONObjectiveType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AncientRelic.Comment", "// N\xc3\xba""cleos de energia\n" },
		{ "AncientRelic.DisplayName", "Ancient Relic" },
		{ "AncientRelic.Name", "EAURACRONObjectiveType::AncientRelic" },
		{ "AncientRelic.ToolTip", "N\xc3\xba""cleos de energia" },
		{ "ArsenalIsland.Comment", "// Pontos de cura/buff\n" },
		{ "ArsenalIsland.DisplayName", "Arsenal Island" },
		{ "ArsenalIsland.Name", "EAURACRONObjectiveType::ArsenalIsland" },
		{ "ArsenalIsland.ToolTip", "Pontos de cura/buff" },
		{ "BlueprintType", "true" },
		{ "CapturePoint.Comment", "// OBJETIVOS PROCEDURAIS (do ObjectiveManager)\n" },
		{ "CapturePoint.DisplayName", "Capture Point" },
		{ "CapturePoint.Name", "EAURACRONObjectiveType::CapturePoint" },
		{ "CapturePoint.ToolTip", "OBJETIVOS PROCEDURAIS (do ObjectiveManager)" },
		{ "ChaosIsland.Comment", "// Pontos de equipamento\n" },
		{ "ChaosIsland.DisplayName", "Chaos Island" },
		{ "ChaosIsland.Name", "EAURACRONObjectiveType::ChaosIsland" },
		{ "ChaosIsland.ToolTip", "Pontos de equipamento" },
		{ "ChaosRift.Comment", "// Santu\xc3\xa1rios de cura\n" },
		{ "ChaosRift.DisplayName", "Chaos Rift" },
		{ "ChaosRift.Name", "EAURACRONObjectiveType::ChaosRift" },
		{ "ChaosRift.ToolTip", "Santu\xc3\xa1rios de cura" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de objetivos estrat\xc3\xa9gicos consolidados\n * CONSOLIDADO: Combina objetivos estrat\xc3\xa9gicos e procedurais\n */" },
#endif
		{ "DefenseTower.Comment", "// Condutos de energia\n" },
		{ "DefenseTower.DisplayName", "Defense Tower" },
		{ "DefenseTower.Name", "EAURACRONObjectiveType::DefenseTower" },
		{ "DefenseTower.ToolTip", "Condutos de energia" },
		{ "EnergyConduit.Comment", "// Rel\xc3\xadquias antigas\n" },
		{ "EnergyConduit.DisplayName", "Energy Conduit" },
		{ "EnergyConduit.Name", "EAURACRONObjectiveType::EnergyConduit" },
		{ "EnergyConduit.ToolTip", "Rel\xc3\xadquias antigas" },
		{ "HealingShrine.Comment", "// Torres defensivas\n" },
		{ "HealingShrine.DisplayName", "Healing Shrine" },
		{ "HealingShrine.Name", "EAURACRONObjectiveType::HealingShrine" },
		{ "HealingShrine.ToolTip", "Torres defensivas" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
		{ "NexusIsland.Comment", "// OBJETIVOS \xc3\x9aNICOS DO AURACRON\n" },
		{ "NexusIsland.DisplayName", "Nexus Island" },
		{ "NexusIsland.Name", "EAURACRONObjectiveType::NexusIsland" },
		{ "NexusIsland.ToolTip", "OBJETIVOS \xc3\x9aNICOS DO AURACRON" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAURACRONObjectiveType::None" },
		{ "PowerCore.Comment", "// N\xc3\xb3s de recursos\n" },
		{ "PowerCore.DisplayName", "Power Core" },
		{ "PowerCore.Name", "EAURACRONObjectiveType::PowerCore" },
		{ "PowerCore.ToolTip", "N\xc3\xb3s de recursos" },
		{ "PrismalNexus.Comment", "// OBJETIVO PRINCIPAL (Baron equivalent)\n" },
		{ "PrismalNexus.DisplayName", "Prismal Nexus" },
		{ "PrismalNexus.Name", "EAURACRONObjectiveType::PrismalNexus" },
		{ "PrismalNexus.ToolTip", "OBJETIVO PRINCIPAL (Baron equivalent)" },
		{ "PurgatoryAnchor.Comment", "// Air/Lightning Dragon equivalent\n" },
		{ "PurgatoryAnchor.DisplayName", "Purgatory Anchor" },
		{ "PurgatoryAnchor.Name", "EAURACRONObjectiveType::PurgatoryAnchor" },
		{ "PurgatoryAnchor.ToolTip", "Air/Lightning Dragon equivalent" },
		{ "RadiantAnchor.Comment", "// OBJETIVOS SECUND\xc3\x81RIOS (Dragon equivalents)\n" },
		{ "RadiantAnchor.DisplayName", "Radiant Anchor" },
		{ "RadiantAnchor.Name", "EAURACRONObjectiveType::RadiantAnchor" },
		{ "RadiantAnchor.ToolTip", "OBJETIVOS SECUND\xc3\x81RIOS (Dragon equivalents)" },
		{ "ResourceNode.Comment", "// Pontos de captura din\xc3\xa2micos\n" },
		{ "ResourceNode.DisplayName", "Resource Node" },
		{ "ResourceNode.Name", "EAURACRONObjectiveType::ResourceNode" },
		{ "ResourceNode.ToolTip", "Pontos de captura din\xc3\xa2micos" },
		{ "SanctuaryIsland.Comment", "// Centro do mapa\n" },
		{ "SanctuaryIsland.DisplayName", "Sanctuary Island" },
		{ "SanctuaryIsland.Name", "EAURACRONObjectiveType::SanctuaryIsland" },
		{ "SanctuaryIsland.ToolTip", "Centro do mapa" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de objetivos estrat\xc3\xa9gicos consolidados\nCONSOLIDADO: Combina objetivos estrat\xc3\xa9gicos e procedurais" },
#endif
		{ "ZephyrAnchor.Comment", "// Fire/Earth Dragon equivalent\n" },
		{ "ZephyrAnchor.DisplayName", "Zephyr Anchor" },
		{ "ZephyrAnchor.Name", "EAURACRONObjectiveType::ZephyrAnchor" },
		{ "ZephyrAnchor.ToolTip", "Fire/Earth Dragon equivalent" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONObjectiveType::None", (int64)EAURACRONObjectiveType::None },
		{ "EAURACRONObjectiveType::PrismalNexus", (int64)EAURACRONObjectiveType::PrismalNexus },
		{ "EAURACRONObjectiveType::RadiantAnchor", (int64)EAURACRONObjectiveType::RadiantAnchor },
		{ "EAURACRONObjectiveType::ZephyrAnchor", (int64)EAURACRONObjectiveType::ZephyrAnchor },
		{ "EAURACRONObjectiveType::PurgatoryAnchor", (int64)EAURACRONObjectiveType::PurgatoryAnchor },
		{ "EAURACRONObjectiveType::NexusIsland", (int64)EAURACRONObjectiveType::NexusIsland },
		{ "EAURACRONObjectiveType::SanctuaryIsland", (int64)EAURACRONObjectiveType::SanctuaryIsland },
		{ "EAURACRONObjectiveType::ArsenalIsland", (int64)EAURACRONObjectiveType::ArsenalIsland },
		{ "EAURACRONObjectiveType::ChaosIsland", (int64)EAURACRONObjectiveType::ChaosIsland },
		{ "EAURACRONObjectiveType::CapturePoint", (int64)EAURACRONObjectiveType::CapturePoint },
		{ "EAURACRONObjectiveType::ResourceNode", (int64)EAURACRONObjectiveType::ResourceNode },
		{ "EAURACRONObjectiveType::PowerCore", (int64)EAURACRONObjectiveType::PowerCore },
		{ "EAURACRONObjectiveType::AncientRelic", (int64)EAURACRONObjectiveType::AncientRelic },
		{ "EAURACRONObjectiveType::EnergyConduit", (int64)EAURACRONObjectiveType::EnergyConduit },
		{ "EAURACRONObjectiveType::DefenseTower", (int64)EAURACRONObjectiveType::DefenseTower },
		{ "EAURACRONObjectiveType::HealingShrine", (int64)EAURACRONObjectiveType::HealingShrine },
		{ "EAURACRONObjectiveType::ChaosRift", (int64)EAURACRONObjectiveType::ChaosRift },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONObjectiveType",
	"EAURACRONObjectiveType",
	Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONObjectiveType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveType.InnerSingleton;
}
// ********** End Enum EAURACRONObjectiveType ******************************************************

// ********** Begin Enum EAURACRONObjectiveState ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONObjectiveState;
static UEnum* EAURACRONObjectiveState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONObjectiveState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONObjectiveState"));
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveState.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONObjectiveState>()
{
	return EAURACRONObjectiveState_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Active.Comment", "// Dispon\xc3\xadvel para captura\n" },
		{ "Active.DisplayName", "Active" },
		{ "Active.Name", "EAURACRONObjectiveState::Active" },
		{ "Active.ToolTip", "Dispon\xc3\xadvel para captura" },
		{ "Available.Comment", "// N\xc3\xa3o dispon\xc3\xadvel ainda\n" },
		{ "Available.DisplayName", "Available" },
		{ "Available.Name", "EAURACRONObjectiveState::Available" },
		{ "Available.ToolTip", "N\xc3\xa3o dispon\xc3\xadvel ainda" },
		{ "BlueprintType", "true" },
		{ "Captured.Comment", "// Disputado (para objetivos procedurais)\n" },
		{ "Captured.DisplayName", "Captured" },
		{ "Captured.Name", "EAURACRONObjectiveState::Captured" },
		{ "Captured.ToolTip", "Disputado (para objetivos procedurais)" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estados do objetivo consolidados\n * CONSOLIDADO: Combina estados de ambos os sistemas\n */" },
#endif
		{ "Contested.Comment", "// Sendo atacado\n" },
		{ "Contested.DisplayName", "Contested" },
		{ "Contested.Name", "EAURACRONObjectiveState::Contested" },
		{ "Contested.ToolTip", "Sendo atacado" },
		{ "Destroyed.Comment", "// Capturado por um time\n" },
		{ "Destroyed.DisplayName", "Destroyed" },
		{ "Destroyed.Name", "EAURACRONObjectiveState::Destroyed" },
		{ "Destroyed.ToolTip", "Capturado por um time" },
		{ "Evolving.Comment", "// Destru\xc3\xad""do (para objetivos procedurais)\n" },
		{ "Evolving.DisplayName", "Evolving" },
		{ "Evolving.Name", "EAURACRONObjectiveState::Evolving" },
		{ "Evolving.ToolTip", "Destru\xc3\xad""do (para objetivos procedurais)" },
		{ "Inactive.DisplayName", "Inactive" },
		{ "Inactive.Name", "EAURACRONObjectiveState::Inactive" },
		{ "InCombat.Comment", "// Ativo (para objetivos procedurais)\n" },
		{ "InCombat.DisplayName", "In Combat" },
		{ "InCombat.Name", "EAURACRONObjectiveState::InCombat" },
		{ "InCombat.ToolTip", "Ativo (para objetivos procedurais)" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
		{ "Respawning.Comment", "// Evoluindo (para objetivos procedurais)\n" },
		{ "Respawning.DisplayName", "Respawning" },
		{ "Respawning.Name", "EAURACRONObjectiveState::Respawning" },
		{ "Respawning.ToolTip", "Evoluindo (para objetivos procedurais)" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estados do objetivo consolidados\nCONSOLIDADO: Combina estados de ambos os sistemas" },
#endif
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONObjectiveState::Inactive", (int64)EAURACRONObjectiveState::Inactive },
		{ "EAURACRONObjectiveState::Available", (int64)EAURACRONObjectiveState::Available },
		{ "EAURACRONObjectiveState::Active", (int64)EAURACRONObjectiveState::Active },
		{ "EAURACRONObjectiveState::InCombat", (int64)EAURACRONObjectiveState::InCombat },
		{ "EAURACRONObjectiveState::Contested", (int64)EAURACRONObjectiveState::Contested },
		{ "EAURACRONObjectiveState::Captured", (int64)EAURACRONObjectiveState::Captured },
		{ "EAURACRONObjectiveState::Destroyed", (int64)EAURACRONObjectiveState::Destroyed },
		{ "EAURACRONObjectiveState::Evolving", (int64)EAURACRONObjectiveState::Evolving },
		{ "EAURACRONObjectiveState::Respawning", (int64)EAURACRONObjectiveState::Respawning },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONObjectiveState",
	"EAURACRONObjectiveState",
	Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState()
{
	if (!Z_Registration_Info_UEnum_EAURACRONObjectiveState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONObjectiveState.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONObjectiveState.InnerSingleton;
}
// ********** End Enum EAURACRONObjectiveState *****************************************************

// ********** Begin ScriptStruct FAURACRONObjectiveInfo ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo;
class UScriptStruct* FAURACRONObjectiveInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONObjectiveInfo, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONObjectiveInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Informa\xc3\xa7\xc3\xb5""es de um objetivo estrat\xc3\xa9gico\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es de um objetivo estrat\xc3\xa9gico" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveType_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionsByEnvironment_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o do objetivo para cada ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o do objetivo para cada ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado atual do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado atual do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveRadius_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio do pit/\xc3\xa1rea do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio do pit/\xc3\xa1rea do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PitDepth_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Profundidade do pit (se aplic\xc3\xa1vel) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Profundidade do pit (se aplic\xc3\xa1vel)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RespawnTime_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de respawn em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de respawn em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeUntilRespawn_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo at\xc3\xa9 pr\xc3\xb3ximo respawn */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo at\xc3\xa9 pr\xc3\xb3ximo respawn" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** HP do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "HP do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentHealth_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** HP atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "HP atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ControllingTeam_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Time que capturou o objetivo (0=Team1, 1=Team2, -1=Neutro) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Time que capturou o objetivo (0=Team1, 1=Team2, -1=Neutro)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveBuffs_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Buffs fornecidos pelo objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Buffs fornecidos pelo objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o objetivo est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o objetivo est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveActor_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ator do objetivo no mundo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator do objetivo no mundo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BuffDuration_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o dos buffs em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o dos buffs em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActiveInCurrentPhase_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o objetivo est\xc3\xa1 ativo na fase atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o objetivo est\xc3\xa1 ativo na fase atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinimumPhaseForActivation_MetaData[] = {
		{ "Category", "AURACRONObjectiveInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase m\xc3\xadnima para ativa\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase m\xc3\xadnima para ativa\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PositionsByEnvironment_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PositionsByEnvironment_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PositionsByEnvironment_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PositionsByEnvironment;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectiveRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PitDepth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RespawnTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeUntilRespawn;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentHealth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ControllingTeam;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ObjectiveBuffs_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ObjectiveBuffs_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ObjectiveBuffs;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ObjectiveActor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BuffDuration;
	static void NewProp_bIsActiveInCurrentPhase_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActiveInCurrentPhase;
	static const UECodeGen_Private::FBytePropertyParams NewProp_MinimumPhaseForActivation_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MinimumPhaseForActivation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONObjectiveInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveType = { "ObjectiveType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, ObjectiveType), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveType_MetaData), NewProp_ObjectiveType_MetaData) }; // 2308998292
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment_ValueProp = { "PositionsByEnvironment", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp = { "PositionsByEnvironment_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2415364844
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment = { "PositionsByEnvironment", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, PositionsByEnvironment), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionsByEnvironment_MetaData), NewProp_PositionsByEnvironment_MetaData) }; // 2415364844
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, CurrentState), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // 600658364
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveRadius = { "ObjectiveRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, ObjectiveRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveRadius_MetaData), NewProp_ObjectiveRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PitDepth = { "PitDepth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, PitDepth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PitDepth_MetaData), NewProp_PitDepth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_RespawnTime = { "RespawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, RespawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RespawnTime_MetaData), NewProp_RespawnTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_TimeUntilRespawn = { "TimeUntilRespawn", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, TimeUntilRespawn), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeUntilRespawn_MetaData), NewProp_TimeUntilRespawn_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_CurrentHealth = { "CurrentHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, CurrentHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentHealth_MetaData), NewProp_CurrentHealth_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ControllingTeam = { "ControllingTeam", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, ControllingTeam), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ControllingTeam_MetaData), NewProp_ControllingTeam_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveBuffs_ValueProp = { "ObjectiveBuffs", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveBuffs_Key_KeyProp = { "ObjectiveBuffs_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveBuffs = { "ObjectiveBuffs", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, ObjectiveBuffs), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveBuffs_MetaData), NewProp_ObjectiveBuffs_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAURACRONObjectiveInfo*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONObjectiveInfo), &Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveActor = { "ObjectiveActor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, ObjectiveActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveActor_MetaData), NewProp_ObjectiveActor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_BuffDuration = { "BuffDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, BuffDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BuffDuration_MetaData), NewProp_BuffDuration_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActiveInCurrentPhase_SetBit(void* Obj)
{
	((FAURACRONObjectiveInfo*)Obj)->bIsActiveInCurrentPhase = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActiveInCurrentPhase = { "bIsActiveInCurrentPhase", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONObjectiveInfo), &Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActiveInCurrentPhase_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActiveInCurrentPhase_MetaData), NewProp_bIsActiveInCurrentPhase_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_MinimumPhaseForActivation_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_MinimumPhaseForActivation = { "MinimumPhaseForActivation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveInfo, MinimumPhaseForActivation), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinimumPhaseForActivation_MetaData), NewProp_MinimumPhaseForActivation_MetaData) }; // 657470012
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PositionsByEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_CurrentState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_PitDepth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_RespawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_TimeUntilRespawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_CurrentHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ControllingTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveBuffs_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveBuffs_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveBuffs,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_ObjectiveActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_BuffDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_bIsActiveInCurrentPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_MinimumPhaseForActivation_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewProp_MinimumPhaseForActivation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONObjectiveInfo",
	Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::PropPointers),
	sizeof(FAURACRONObjectiveInfo),
	alignof(FAURACRONObjectiveInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONObjectiveInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONObjectiveInfo **********************************************

// ********** Begin ScriptStruct FAURACRONProceduralObjective **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective;
class UScriptStruct* FAURACRONProceduralObjective::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONProceduralObjective, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONProceduralObjective"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para objetivos procedurais (do ObjectiveManager)\n * CONSOLIDADO: Adiciona suporte a objetivos din\xc3\xa2micos\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para objetivos procedurais (do ObjectiveManager)\nCONSOLIDADO: Adiciona suporte a objetivos din\xc3\xa2micos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveID_MetaData[] = {
		{ "Category", "AURACRONProceduralObjective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** ID \xc3\xbanico do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "ID \xc3\xbanico do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveType_MetaData[] = {
		{ "Category", "AURACRONProceduralObjective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "Category", "AURACRONProceduralObjective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Estado atual */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estado atual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPosition_MetaData[] = {
		{ "Category", "AURACRONProceduralObjective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o no mundo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o no mundo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentType_MetaData[] = {
		{ "Category", "AURACRONProceduralObjective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ambiente onde est\xc3\xa1 localizado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambiente onde est\xc3\xa1 localizado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StrategicValue_MetaData[] = {
		{ "Category", "AURACRONProceduralObjective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Valor estrat\xc3\xa9gico (0.0 - 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Valor estrat\xc3\xa9gico (0.0 - 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaptureDifficulty_MetaData[] = {
		{ "Category", "AURACRONProceduralObjective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dificuldade de captura (0.0 - 1.0) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dificuldade de captura (0.0 - 1.0)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CaptureTime_MetaData[] = {
		{ "Category", "AURACRONProceduralObjective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo para capturar */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo para capturar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeAlive_MetaData[] = {
		{ "Category", "AURACRONProceduralObjective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo que o objetivo est\xc3\xa1 vivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo que o objetivo est\xc3\xa1 vivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDuration_MetaData[] = {
		{ "Category", "AURACRONProceduralObjective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dura\xc3\xa7\xc3\xa3o m\xc3\xa1xima do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dura\xc3\xa7\xc3\xa3o m\xc3\xa1xima do objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rewards_MetaData[] = {
		{ "Category", "AURACRONProceduralObjective" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensas do objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensas do objetivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ObjectiveID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldPosition;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StrategicValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CaptureDifficulty;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CaptureTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeAlive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Rewards_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Rewards_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Rewards;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONProceduralObjective>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveID = { "ObjectiveID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, ObjectiveID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveID_MetaData), NewProp_ObjectiveID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveType = { "ObjectiveType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, ObjectiveType), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveType_MetaData), NewProp_ObjectiveType_MetaData) }; // 2308998292
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, CurrentState), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // 600658364
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_WorldPosition = { "WorldPosition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, WorldPosition), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPosition_MetaData), NewProp_WorldPosition_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentType_MetaData), NewProp_EnvironmentType_MetaData) }; // 2415364844
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_StrategicValue = { "StrategicValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, StrategicValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StrategicValue_MetaData), NewProp_StrategicValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CaptureDifficulty = { "CaptureDifficulty", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, CaptureDifficulty), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaptureDifficulty_MetaData), NewProp_CaptureDifficulty_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CaptureTime = { "CaptureTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, CaptureTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CaptureTime_MetaData), NewProp_CaptureTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_TimeAlive = { "TimeAlive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, TimeAlive), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeAlive_MetaData), NewProp_TimeAlive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_MaxDuration = { "MaxDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, MaxDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDuration_MetaData), NewProp_MaxDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Rewards_ValueProp = { "Rewards", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Rewards_Key_KeyProp = { "Rewards_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Rewards = { "Rewards", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONProceduralObjective, Rewards), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rewards_MetaData), NewProp_Rewards_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_ObjectiveType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CurrentState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_WorldPosition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_EnvironmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_StrategicValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CaptureDifficulty,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_CaptureTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_TimeAlive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_MaxDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Rewards_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Rewards_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewProp_Rewards,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONProceduralObjective",
	Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::PropPointers),
	sizeof(FAURACRONProceduralObjective),
	alignof(FAURACRONProceduralObjective),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONProceduralObjective()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONProceduralObjective ****************************************

// ********** Begin ScriptStruct FAURACRONObjectiveGenerationConfig ********************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig;
class UScriptStruct* FAURACRONObjectiveGenerationConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONObjectiveGenerationConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xb5""es de gera\xc3\xa7\xc3\xa3o procedural (do ObjectiveManager)\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de gera\xc3\xa7\xc3\xa3o procedural (do ObjectiveManager)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinActiveObjectives_MetaData[] = {
		{ "Category", "AURACRONObjectiveGenerationConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xadnimo de objetivos ativos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xadnimo de objetivos ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxActiveObjectives_MetaData[] = {
		{ "Category", "AURACRONObjectiveGenerationConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xbamero m\xc3\xa1ximo de objetivos ativos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xbamero m\xc3\xa1ximo de objetivos ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationInterval_MetaData[] = {
		{ "Category", "AURACRONObjectiveGenerationConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo de gera\xc3\xa7\xc3\xa3o em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de gera\xc3\xa7\xc3\xa3o em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TypeProbabilities_MetaData[] = {
		{ "Category", "AURACRONObjectiveGenerationConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Probabilidades por tipo de objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Probabilidades por tipo de objetivo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PhaseModifiers_MetaData[] = {
		{ "Category", "AURACRONObjectiveGenerationConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Modificadores por fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Modificadores por fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinActiveObjectives;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxActiveObjectives;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GenerationInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TypeProbabilities_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TypeProbabilities_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TypeProbabilities_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TypeProbabilities;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PhaseModifiers_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PhaseModifiers_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PhaseModifiers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PhaseModifiers;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONObjectiveGenerationConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MinActiveObjectives = { "MinActiveObjectives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, MinActiveObjectives), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinActiveObjectives_MetaData), NewProp_MinActiveObjectives_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MaxActiveObjectives = { "MaxActiveObjectives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, MaxActiveObjectives), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxActiveObjectives_MetaData), NewProp_MaxActiveObjectives_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_GenerationInterval = { "GenerationInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, GenerationInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationInterval_MetaData), NewProp_GenerationInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_TypeProbabilities_ValueProp = { "TypeProbabilities", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_TypeProbabilities_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_TypeProbabilities_Key_KeyProp = { "TypeProbabilities_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, METADATA_PARAMS(0, nullptr) }; // 2308998292
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_TypeProbabilities = { "TypeProbabilities", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, TypeProbabilities), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TypeProbabilities_MetaData), NewProp_TypeProbabilities_MetaData) }; // 2308998292
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_PhaseModifiers_ValueProp = { "PhaseModifiers", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_PhaseModifiers_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_PhaseModifiers_Key_KeyProp = { "PhaseModifiers_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 657470012
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_PhaseModifiers = { "PhaseModifiers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONObjectiveGenerationConfig, PhaseModifiers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PhaseModifiers_MetaData), NewProp_PhaseModifiers_MetaData) }; // 657470012
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MinActiveObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_MaxActiveObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_GenerationInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_TypeProbabilities_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_TypeProbabilities_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_TypeProbabilities_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_TypeProbabilities,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_PhaseModifiers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_PhaseModifiers_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_PhaseModifiers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewProp_PhaseModifiers,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONObjectiveGenerationConfig",
	Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::PropPointers),
	sizeof(FAURACRONObjectiveGenerationConfig),
	alignof(FAURACRONObjectiveGenerationConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONObjectiveGenerationConfig **********************************

// ********** Begin Delegate FOnObjectiveCreated ***************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnObjectiveCreated_Parms
	{
		FAURACRONProceduralObjective Objective;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Delegates consolidados para eventos de objetivos\n * CONSOLIDADO: Combina delegates de ambos os sistemas\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delegates consolidados para eventos de objetivos\nCONSOLIDADO: Combina delegates de ambos os sistemas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Objective_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Objective;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::NewProp_Objective = { "Objective", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveCreated_Parms, Objective), Z_Construct_UScriptStruct_FAURACRONProceduralObjective, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Objective_MetaData), NewProp_Objective_MetaData) }; // 4017967595
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::NewProp_Objective,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnObjectiveCreated__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveCreated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveCreated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnObjectiveCreated_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveCreated, FAURACRONProceduralObjective const& Objective)
{
	struct _Script_AURACRON_eventOnObjectiveCreated_Parms
	{
		FAURACRONProceduralObjective Objective;
	};
	_Script_AURACRON_eventOnObjectiveCreated_Parms Parms;
	Parms.Objective=Objective;
	OnObjectiveCreated.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnObjectiveCreated *****************************************************

// ********** Begin Delegate FOnObjectiveDestroyed *************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnObjectiveDestroyed_Parms
	{
		FAURACRONProceduralObjective Objective;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Objective_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Objective;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::NewProp_Objective = { "Objective", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveDestroyed_Parms, Objective), Z_Construct_UScriptStruct_FAURACRONProceduralObjective, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Objective_MetaData), NewProp_Objective_MetaData) }; // 4017967595
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::NewProp_Objective,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnObjectiveDestroyed__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveDestroyed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveDestroyed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnObjectiveDestroyed_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveDestroyed, FAURACRONProceduralObjective const& Objective)
{
	struct _Script_AURACRON_eventOnObjectiveDestroyed_Parms
	{
		FAURACRONProceduralObjective Objective;
	};
	_Script_AURACRON_eventOnObjectiveDestroyed_Parms Parms;
	Parms.Objective=Objective;
	OnObjectiveDestroyed.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnObjectiveDestroyed ***************************************************

// ********** Begin Delegate FOnObjectiveStateChanged **********************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnObjectiveStateChanged_Parms
	{
		FAURACRONProceduralObjective Objective;
		EAURACRONObjectiveState OldState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Objective_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Objective;
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::NewProp_Objective = { "Objective", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveStateChanged_Parms, Objective), Z_Construct_UScriptStruct_FAURACRONProceduralObjective, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Objective_MetaData), NewProp_Objective_MetaData) }; // 4017967595
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::NewProp_OldState = { "OldState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveStateChanged_Parms, OldState), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState, METADATA_PARAMS(0, nullptr) }; // 600658364
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::NewProp_Objective,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::NewProp_OldState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnObjectiveStateChanged__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00530000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnObjectiveStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveStateChanged, FAURACRONProceduralObjective const& Objective, EAURACRONObjectiveState OldState)
{
	struct _Script_AURACRON_eventOnObjectiveStateChanged_Parms
	{
		FAURACRONProceduralObjective Objective;
		EAURACRONObjectiveState OldState;
	};
	_Script_AURACRON_eventOnObjectiveStateChanged_Parms Parms;
	Parms.Objective=Objective;
	Parms.OldState=OldState;
	OnObjectiveStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnObjectiveStateChanged ************************************************

// ********** Begin Delegate FOnObjectiveCaptured **************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnObjectiveCaptured_Parms
	{
		int32 ObjectiveIndex;
		int32 CapturingTeam;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CapturingTeam;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveCaptured_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::NewProp_CapturingTeam = { "CapturingTeam", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnObjectiveCaptured_Parms, CapturingTeam), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::NewProp_CapturingTeam,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnObjectiveCaptured__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveCaptured_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::_Script_AURACRON_eventOnObjectiveCaptured_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnObjectiveCaptured_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveCaptured, int32 ObjectiveIndex, int32 CapturingTeam)
{
	struct _Script_AURACRON_eventOnObjectiveCaptured_Parms
	{
		int32 ObjectiveIndex;
		int32 CapturingTeam;
	};
	_Script_AURACRON_eventOnObjectiveCaptured_Parms Parms;
	Parms.ObjectiveIndex=ObjectiveIndex;
	Parms.CapturingTeam=CapturingTeam;
	OnObjectiveCaptured.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnObjectiveCaptured ****************************************************

// ********** Begin Delegate FOnChaosIslandEvent ***************************************************
struct Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics
{
	struct _Script_AURACRON_eventOnChaosIslandEvent_Parms
	{
		int32 IslandIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_IslandIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FDelegateFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::NewProp_IslandIndex = { "IslandIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_AURACRON_eventOnChaosIslandEvent_Parms, IslandIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::NewProp_IslandIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FDelegateFunctionParams Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UPackage__Script_AURACRON, nullptr, "OnChaosIslandEvent__DelegateSignature", Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::_Script_AURACRON_eventOnChaosIslandEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::_Script_AURACRON_eventOnChaosIslandEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUDelegateFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnChaosIslandEvent_DelegateWrapper(const FMulticastScriptDelegate& OnChaosIslandEvent, int32 IslandIndex)
{
	struct _Script_AURACRON_eventOnChaosIslandEvent_Parms
	{
		int32 IslandIndex;
	};
	_Script_AURACRON_eventOnChaosIslandEvent_Parms Parms;
	Parms.IslandIndex=IslandIndex;
	OnChaosIslandEvent.ProcessMulticastDelegate<UObject>(&Parms);
}
// ********** End Delegate FOnChaosIslandEvent *****************************************************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function AttackObjective *********************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics
{
	struct AURACRONPCGObjectiveSystem_eventAttackObjective_Parms
	{
		int32 ObjectiveIndex;
		float Damage;
		int32 AttackingTeam;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atacar objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atacar objetivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AttackingTeam;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventAttackObjective_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_Damage = { "Damage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventAttackObjective_Parms, Damage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_AttackingTeam = { "AttackingTeam", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventAttackObjective_Parms, AttackingTeam), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGObjectiveSystem_eventAttackObjective_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGObjectiveSystem_eventAttackObjective_Parms), &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_Damage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_AttackingTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "AttackObjective", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::AURACRONPCGObjectiveSystem_eventAttackObjective_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::AURACRONPCGObjectiveSystem_eventAttackObjective_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execAttackObjective)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ObjectiveIndex);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Damage);
	P_GET_PROPERTY(FIntProperty,Z_Param_AttackingTeam);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AttackObjective(Z_Param_ObjectiveIndex,Z_Param_Damage,Z_Param_AttackingTeam);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function AttackObjective ***********************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function CaptureObjective ********************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics
{
	struct AURACRONPCGObjectiveSystem_eventCaptureObjective_Parms
	{
		int32 ObjectiveIndex;
		int32 CapturingTeam;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Capturar objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Capturar objetivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CapturingTeam;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventCaptureObjective_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_CapturingTeam = { "CapturingTeam", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventCaptureObjective_Parms, CapturingTeam), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGObjectiveSystem_eventCaptureObjective_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGObjectiveSystem_eventCaptureObjective_Parms), &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_CapturingTeam,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "CaptureObjective", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::AURACRONPCGObjectiveSystem_eventCaptureObjective_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::AURACRONPCGObjectiveSystem_eventCaptureObjective_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execCaptureObjective)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ObjectiveIndex);
	P_GET_PROPERTY(FIntProperty,Z_Param_CapturingTeam);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CaptureObjective(Z_Param_ObjectiveIndex,Z_Param_CapturingTeam);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function CaptureObjective **********************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function ForceGenerateObjective **************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics
{
	struct AURACRONPCGObjectiveSystem_eventForceGenerateObjective_Parms
	{
		EAURACRONObjectiveType ObjectiveType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** For\xc3\xa7""ar gera\xc3\xa7\xc3\xa3o de objetivo procedural */" },
#endif
		{ "CPP_Default_ObjectiveType", "None" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "For\xc3\xa7""ar gera\xc3\xa7\xc3\xa3o de objetivo procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::NewProp_ObjectiveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::NewProp_ObjectiveType = { "ObjectiveType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventForceGenerateObjective_Parms, ObjectiveType), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, METADATA_PARAMS(0, nullptr) }; // 2308998292
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::NewProp_ObjectiveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::NewProp_ObjectiveType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "ForceGenerateObjective", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::AURACRONPCGObjectiveSystem_eventForceGenerateObjective_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::AURACRONPCGObjectiveSystem_eventForceGenerateObjective_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execForceGenerateObjective)
{
	P_GET_ENUM(EAURACRONObjectiveType,Z_Param_ObjectiveType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceGenerateObjective(EAURACRONObjectiveType(Z_Param_ObjectiveType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function ForceGenerateObjective ****************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GenerateObjectives ******************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectives_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar todos os objetivos estrat\xc3\xa9gicos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar todos os objetivos estrat\xc3\xa9gicos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectives_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GenerateObjectives", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectives_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectives_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectives()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectives_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGenerateObjectives)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateObjectives();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GenerateObjectives ********************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GenerateObjectivesForEnvironment ****
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics
{
	struct AURACRONPCGObjectiveSystem_eventGenerateObjectivesForEnvironment_Parms
	{
		EAURACRONEnvironmentType Environment;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar objetivos para ambiente espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar objetivos para ambiente espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGenerateObjectivesForEnvironment_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2415364844
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::NewProp_Environment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GenerateObjectivesForEnvironment", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::AURACRONPCGObjectiveSystem_eventGenerateObjectivesForEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::AURACRONPCGObjectiveSystem_eventGenerateObjectivesForEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGenerateObjectivesForEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateObjectivesForEnvironment(EAURACRONEnvironmentType(Z_Param_Environment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GenerateObjectivesForEnvironment ******

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GetAllObjectives ********************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics
{
	struct AURACRONPCGObjectiveSystem_eventGetAllObjectives_Parms
	{
		TArray<FAURACRONObjectiveInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter informa\xc3\xa7\xc3\xb5""es de todos os objetivos estrat\xc3\xa9gicos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter informa\xc3\xa7\xc3\xb5""es de todos os objetivos estrat\xc3\xa9gicos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONObjectiveInfo, METADATA_PARAMS(0, nullptr) }; // 3803898084
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetAllObjectives_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3803898084
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GetAllObjectives", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::AURACRONPCGObjectiveSystem_eventGetAllObjectives_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::AURACRONPCGObjectiveSystem_eventGetAllObjectives_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGetAllObjectives)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONObjectiveInfo>*)Z_Param__Result=P_THIS->GetAllObjectives();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GetAllObjectives **********************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GetAllProceduralObjectives **********
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics
{
	struct AURACRONPCGObjectiveSystem_eventGetAllProceduralObjectives_Parms
	{
		TArray<FAURACRONProceduralObjective> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter todos os objetivos procedurais */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter todos os objetivos procedurais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONProceduralObjective, METADATA_PARAMS(0, nullptr) }; // 4017967595
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetAllProceduralObjectives_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 4017967595
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GetAllProceduralObjectives", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::AURACRONPCGObjectiveSystem_eventGetAllProceduralObjectives_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::AURACRONPCGObjectiveSystem_eventGetAllProceduralObjectives_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGetAllProceduralObjectives)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONProceduralObjective>*)Z_Param__Result=P_THIS->GetAllProceduralObjectives();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GetAllProceduralObjectives ************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GetObjectiveBuffs *******************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics
{
	struct AURACRONPCGObjectiveSystem_eventGetObjectiveBuffs_Parms
	{
		int32 ObjectiveIndex;
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter buffs de um objetivo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter buffs de um objetivo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetObjectiveBuffs_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetObjectiveBuffs_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GetObjectiveBuffs", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::AURACRONPCGObjectiveSystem_eventGetObjectiveBuffs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::AURACRONPCGObjectiveSystem_eventGetObjectiveBuffs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGetObjectiveBuffs)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ObjectiveIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetObjectiveBuffs(Z_Param_ObjectiveIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GetObjectiveBuffs *********************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GetObjectivesByState ****************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics
{
	struct AURACRONPCGObjectiveSystem_eventGetObjectivesByState_Parms
	{
		EAURACRONObjectiveState State;
		TArray<FAURACRONObjectiveInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter objetivos por estado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter objetivos por estado" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_State_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_State;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_State_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetObjectivesByState_Parms, State), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveState, METADATA_PARAMS(0, nullptr) }; // 600658364
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONObjectiveInfo, METADATA_PARAMS(0, nullptr) }; // 3803898084
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetObjectivesByState_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3803898084
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_State_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GetObjectivesByState", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::AURACRONPCGObjectiveSystem_eventGetObjectivesByState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::AURACRONPCGObjectiveSystem_eventGetObjectivesByState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGetObjectivesByState)
{
	P_GET_ENUM(EAURACRONObjectiveState,Z_Param_State);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONObjectiveInfo>*)Z_Param__Result=P_THIS->GetObjectivesByState(EAURACRONObjectiveState(Z_Param_State));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GetObjectivesByState ******************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function GetObjectivesByType *****************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics
{
	struct AURACRONPCGObjectiveSystem_eventGetObjectivesByType_Parms
	{
		EAURACRONObjectiveType ObjectiveType;
		TArray<FAURACRONObjectiveInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter objetivos por tipo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter objetivos por tipo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ObjectiveType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ObjectiveType = { "ObjectiveType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetObjectivesByType_Parms, ObjectiveType), Z_Construct_UEnum_AURACRON_EAURACRONObjectiveType, METADATA_PARAMS(0, nullptr) }; // 2308998292
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONObjectiveInfo, METADATA_PARAMS(0, nullptr) }; // 3803898084
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventGetObjectivesByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3803898084
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ObjectiveType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ObjectiveType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "GetObjectivesByType", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::AURACRONPCGObjectiveSystem_eventGetObjectivesByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::AURACRONPCGObjectiveSystem_eventGetObjectivesByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execGetObjectivesByType)
{
	P_GET_ENUM(EAURACRONObjectiveType,Z_Param_ObjectiveType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONObjectiveInfo>*)Z_Param__Result=P_THIS->GetObjectivesByType(EAURACRONObjectiveType(Z_Param_ObjectiveType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function GetObjectivesByType *******************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function IsObjectiveAvailable ****************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics
{
	struct AURACRONPCGObjectiveSystem_eventIsObjectiveAvailable_Parms
	{
		int32 ObjectiveIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se objetivo est\xc3\xa1 dispon\xc3\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se objetivo est\xc3\xa1 dispon\xc3\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventIsObjectiveAvailable_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGObjectiveSystem_eventIsObjectiveAvailable_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGObjectiveSystem_eventIsObjectiveAvailable_Parms), &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "IsObjectiveAvailable", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::AURACRONPCGObjectiveSystem_eventIsObjectiveAvailable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::AURACRONPCGObjectiveSystem_eventIsObjectiveAvailable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execIsObjectiveAvailable)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ObjectiveIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsObjectiveAvailable(Z_Param_ObjectiveIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function IsObjectiveAvailable ******************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function OnProceduralGenerationTimerExpired **
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnProceduralGenerationTimerExpired_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Callback para timer de gera\xc3\xa7\xc3\xa3o procedural */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Callback para timer de gera\xc3\xa7\xc3\xa3o procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnProceduralGenerationTimerExpired_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "OnProceduralGenerationTimerExpired", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnProceduralGenerationTimerExpired_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnProceduralGenerationTimerExpired_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnProceduralGenerationTimerExpired()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnProceduralGenerationTimerExpired_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execOnProceduralGenerationTimerExpired)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnProceduralGenerationTimerExpired();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function OnProceduralGenerationTimerExpired ****

// ********** Begin Class AAURACRONPCGObjectiveSystem Function StartObjectiveSystem ****************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StartObjectiveSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Iniciar sistema de objetivos procedurais (do ObjectiveManager) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Iniciar sistema de objetivos procedurais (do ObjectiveManager)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StartObjectiveSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "StartObjectiveSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StartObjectiveSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StartObjectiveSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StartObjectiveSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StartObjectiveSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execStartObjectiveSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartObjectiveSystem();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function StartObjectiveSystem ******************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function StopObjectiveSystem *****************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StopObjectiveSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Parar sistema de objetivos procedurais */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parar sistema de objetivos procedurais" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StopObjectiveSystem_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "StopObjectiveSystem", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StopObjectiveSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StopObjectiveSystem_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StopObjectiveSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StopObjectiveSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execStopObjectiveSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopObjectiveSystem();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function StopObjectiveSystem *******************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function TriggerChaosIslandEvent *************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics
{
	struct AURACRONPCGObjectiveSystem_eventTriggerChaosIslandEvent_Parms
	{
		int32 ChaosIslandIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ativar evento de Chaos Island */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ativar evento de Chaos Island" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ChaosIslandIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::NewProp_ChaosIslandIndex = { "ChaosIslandIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventTriggerChaosIslandEvent_Parms, ChaosIslandIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::NewProp_ChaosIslandIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "TriggerChaosIslandEvent", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::AURACRONPCGObjectiveSystem_eventTriggerChaosIslandEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::AURACRONPCGObjectiveSystem_eventTriggerChaosIslandEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execTriggerChaosIslandEvent)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ChaosIslandIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TriggerChaosIslandEvent(Z_Param_ChaosIslandIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function TriggerChaosIslandEvent ***************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function UpdateForEnvironment ****************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics
{
	struct AURACRONPCGObjectiveSystem_eventUpdateForEnvironment_Parms
	{
		EAURACRONEnvironmentType NewEnvironment;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar para novo ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar para novo ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewEnvironment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment = { "NewEnvironment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventUpdateForEnvironment_Parms, NewEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2415364844
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "UpdateForEnvironment", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::AURACRONPCGObjectiveSystem_eventUpdateForEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::AURACRONPCGObjectiveSystem_eventUpdateForEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execUpdateForEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_NewEnvironment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForEnvironment(EAURACRONEnvironmentType(Z_Param_NewEnvironment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function UpdateForEnvironment ******************

// ********** Begin Class AAURACRONPCGObjectiveSystem Function UpdateForMapPhase *******************
struct Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics
{
	struct AURACRONPCGObjectiveSystem_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar para nova fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar para nova fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGObjectiveSystem_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 657470012
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGObjectiveSystem, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::AURACRONPCGObjectiveSystem_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::AURACRONPCGObjectiveSystem_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGObjectiveSystem::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGObjectiveSystem Function UpdateForMapPhase *********************

// ********** Begin Class AAURACRONPCGObjectiveSystem **********************************************
void AAURACRONPCGObjectiveSystem::StaticRegisterNativesAAURACRONPCGObjectiveSystem()
{
	UClass* Class = AAURACRONPCGObjectiveSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AttackObjective", &AAURACRONPCGObjectiveSystem::execAttackObjective },
		{ "CaptureObjective", &AAURACRONPCGObjectiveSystem::execCaptureObjective },
		{ "ForceGenerateObjective", &AAURACRONPCGObjectiveSystem::execForceGenerateObjective },
		{ "GenerateObjectives", &AAURACRONPCGObjectiveSystem::execGenerateObjectives },
		{ "GenerateObjectivesForEnvironment", &AAURACRONPCGObjectiveSystem::execGenerateObjectivesForEnvironment },
		{ "GetAllObjectives", &AAURACRONPCGObjectiveSystem::execGetAllObjectives },
		{ "GetAllProceduralObjectives", &AAURACRONPCGObjectiveSystem::execGetAllProceduralObjectives },
		{ "GetObjectiveBuffs", &AAURACRONPCGObjectiveSystem::execGetObjectiveBuffs },
		{ "GetObjectivesByState", &AAURACRONPCGObjectiveSystem::execGetObjectivesByState },
		{ "GetObjectivesByType", &AAURACRONPCGObjectiveSystem::execGetObjectivesByType },
		{ "IsObjectiveAvailable", &AAURACRONPCGObjectiveSystem::execIsObjectiveAvailable },
		{ "OnProceduralGenerationTimerExpired", &AAURACRONPCGObjectiveSystem::execOnProceduralGenerationTimerExpired },
		{ "StartObjectiveSystem", &AAURACRONPCGObjectiveSystem::execStartObjectiveSystem },
		{ "StopObjectiveSystem", &AAURACRONPCGObjectiveSystem::execStopObjectiveSystem },
		{ "TriggerChaosIslandEvent", &AAURACRONPCGObjectiveSystem::execTriggerChaosIslandEvent },
		{ "UpdateForEnvironment", &AAURACRONPCGObjectiveSystem::execUpdateForEnvironment },
		{ "UpdateForMapPhase", &AAURACRONPCGObjectiveSystem::execUpdateForMapPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem;
UClass* AAURACRONPCGObjectiveSystem::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGObjectiveSystem;
	if (!Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGObjectiveSystem"),
			Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGObjectiveSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGObjectiveSystem_NoRegister()
{
	return AAURACRONPCGObjectiveSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Sistema consolidado de objetivos estrat\xc3\xa9gicos para AURACRON\n * CONSOLIDADO: Combina AURACRONPCGObjectiveSystem e AURACRONPCGObjectiveManager\n * Baseado no sistema Baron/Dragon do LoL com objetivos procedurais \xc3\xbanicos\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGObjectiveSystem.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema consolidado de objetivos estrat\xc3\xa9gicos para AURACRON\nCONSOLIDADO: Combina AURACRONPCGObjectiveSystem e AURACRONPCGObjectiveManager\nBaseado no sistema Baron/Dragon do LoL com objetivos procedurais \xc3\xbanicos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnObjectiveCreated_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando objetivo procedural \xc3\xa9 criado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando objetivo procedural \xc3\xa9 criado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnObjectiveDestroyed_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando objetivo procedural \xc3\xa9 destru\xc3\xad""do */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando objetivo procedural \xc3\xa9 destru\xc3\xad""do" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnObjectiveStateChanged_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando estado de objetivo muda */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando estado de objetivo muda" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnObjectiveCaptured_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado quando objetivo \xc3\xa9 capturado */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado quando objetivo \xc3\xa9 capturado" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnChaosIslandEvent_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Evento disparado para Chaos Island */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Evento disparado para Chaos Island" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Objectives_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Informa\xc3\xa7\xc3\xb5""es de todos os objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es de todos os objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveMeshesByEnvironment_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes visuais dos objetivos por ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes visuais dos objetivos por ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveCollisionComponents_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes de colis\xc3\xa3o dos objetivos */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de colis\xc3\xa3o dos objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoGenerate_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve gerar automaticamente no BeginPlay */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve gerar automaticamente no BeginPlay" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEnvironment_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ambiente atualmente ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambiente atualmente ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveProceduralObjectives_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Objetivos procedurais ativos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objetivos procedurais ativos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GenerationConfig_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es de gera\xc3\xa7\xc3\xa3o procedural */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de gera\xc3\xa7\xc3\xa3o procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bProceduralSystemActive_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o sistema procedural est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o sistema procedural est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCatchUpMechanicsActive_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se catch-up mechanics est\xc3\xa3o ativas */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se catch-up mechanics est\xc3\xa3o ativas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoStart_MetaData[] = {
		{ "Category", "AURACRON|ObjectiveSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se auto-start est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se auto-start est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveRespawnTimers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timers de respawn dos objetivos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timers de respawn dos objetivos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChaosIslandEventTimer_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timer para eventos de Chaos Island */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timer para eventos de Chaos Island" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NextChaosIslandIndex_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x8dndice da pr\xc3\xb3xima Chaos Island a ativar */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x8dndice da pr\xc3\xb3xima Chaos Island a ativar" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProceduralGenerationTimer_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timer para gera\xc3\xa7\xc3\xa3o procedural */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timer para gera\xc3\xa7\xc3\xa3o procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ObjectiveIDCounter_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Contador para IDs \xc3\xbanicos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Contador para IDs \xc3\xbanicos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGActorReferences_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancias aos atores PCG (usando utility class) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGObjectiveSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancias aos atores PCG (usando utility class)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnObjectiveCreated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnObjectiveDestroyed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnObjectiveStateChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnObjectiveCaptured;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnChaosIslandEvent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Objectives_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Objectives;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectiveMeshesByEnvironment_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ObjectiveMeshesByEnvironment_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ObjectiveMeshesByEnvironment_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ObjectiveMeshesByEnvironment;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ObjectiveCollisionComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ObjectiveCollisionComponents;
	static void NewProp_bAutoGenerate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoGenerate;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentEnvironment;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveProceduralObjectives_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveProceduralObjectives;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GenerationConfig;
	static void NewProp_bProceduralSystemActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bProceduralSystemActive;
	static void NewProp_bCatchUpMechanicsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCatchUpMechanicsActive;
	static void NewProp_bAutoStart_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoStart;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ObjectiveRespawnTimers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ObjectiveRespawnTimers;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ChaosIslandEventTimer;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NextChaosIslandIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ProceduralGenerationTimer;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIDCounter;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PCGActorReferences;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_AttackObjective, "AttackObjective" }, // 3374826152
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_CaptureObjective, "CaptureObjective" }, // 1203343576
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_ForceGenerateObjective, "ForceGenerateObjective" }, // 3533114161
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectives, "GenerateObjectives" }, // 2957183820
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GenerateObjectivesForEnvironment, "GenerateObjectivesForEnvironment" }, // 3533215833
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllObjectives, "GetAllObjectives" }, // 15233202
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetAllProceduralObjectives, "GetAllProceduralObjectives" }, // 2562629112
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectiveBuffs, "GetObjectiveBuffs" }, // 3956939105
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByState, "GetObjectivesByState" }, // 3639293939
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_GetObjectivesByType, "GetObjectivesByType" }, // 1195555291
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_IsObjectiveAvailable, "IsObjectiveAvailable" }, // 2977368534
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_OnProceduralGenerationTimerExpired, "OnProceduralGenerationTimerExpired" }, // 3323085097
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StartObjectiveSystem, "StartObjectiveSystem" }, // 3307081285
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_StopObjectiveSystem, "StopObjectiveSystem" }, // 2940933908
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_TriggerChaosIslandEvent, "TriggerChaosIslandEvent" }, // 4157970345
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForEnvironment, "UpdateForEnvironment" }, // 1889251957
		{ &Z_Construct_UFunction_AAURACRONPCGObjectiveSystem_UpdateForMapPhase, "UpdateForMapPhase" }, // 4106036522
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGObjectiveSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveCreated = { "OnObjectiveCreated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, OnObjectiveCreated), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCreated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnObjectiveCreated_MetaData), NewProp_OnObjectiveCreated_MetaData) }; // 1057810904
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveDestroyed = { "OnObjectiveDestroyed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, OnObjectiveDestroyed), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveDestroyed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnObjectiveDestroyed_MetaData), NewProp_OnObjectiveDestroyed_MetaData) }; // 1026376366
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveStateChanged = { "OnObjectiveStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, OnObjectiveStateChanged), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnObjectiveStateChanged_MetaData), NewProp_OnObjectiveStateChanged_MetaData) }; // 2321211076
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveCaptured = { "OnObjectiveCaptured", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, OnObjectiveCaptured), Z_Construct_UDelegateFunction_AURACRON_OnObjectiveCaptured__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnObjectiveCaptured_MetaData), NewProp_OnObjectiveCaptured_MetaData) }; // 2868719883
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnChaosIslandEvent = { "OnChaosIslandEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, OnChaosIslandEvent), Z_Construct_UDelegateFunction_AURACRON_OnChaosIslandEvent__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnChaosIslandEvent_MetaData), NewProp_OnChaosIslandEvent_MetaData) }; // 1707412193
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_Objectives_Inner = { "Objectives", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONObjectiveInfo, METADATA_PARAMS(0, nullptr) }; // 3803898084
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_Objectives = { "Objectives", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, Objectives), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Objectives_MetaData), NewProp_Objectives_MetaData) }; // 3803898084
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment_ValueProp = { "ObjectiveMeshesByEnvironment", nullptr, (EPropertyFlags)0x0000008000020001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONMeshComponentArray, METADATA_PARAMS(0, nullptr) }; // 3350711660
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment_Key_KeyProp = { "ObjectiveMeshesByEnvironment_Key", nullptr, (EPropertyFlags)0x0000008000020001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2415364844
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment = { "ObjectiveMeshesByEnvironment", nullptr, (EPropertyFlags)0x0020088000020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ObjectiveMeshesByEnvironment), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveMeshesByEnvironment_MetaData), NewProp_ObjectiveMeshesByEnvironment_MetaData) }; // 2415364844 3350711660
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveCollisionComponents_Inner = { "ObjectiveCollisionComponents", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UBoxComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveCollisionComponents = { "ObjectiveCollisionComponents", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ObjectiveCollisionComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveCollisionComponents_MetaData), NewProp_ObjectiveCollisionComponents_MetaData) };
void Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoGenerate_SetBit(void* Obj)
{
	((AAURACRONPCGObjectiveSystem*)Obj)->bAutoGenerate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoGenerate = { "bAutoGenerate", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGObjectiveSystem), &Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoGenerate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoGenerate_MetaData), NewProp_bAutoGenerate_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentEnvironment = { "CurrentEnvironment", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, CurrentEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEnvironment_MetaData), NewProp_CurrentEnvironment_MetaData) }; // 2415364844
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ActiveProceduralObjectives_Inner = { "ActiveProceduralObjectives", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONProceduralObjective, METADATA_PARAMS(0, nullptr) }; // 4017967595
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ActiveProceduralObjectives = { "ActiveProceduralObjectives", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ActiveProceduralObjectives), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveProceduralObjectives_MetaData), NewProp_ActiveProceduralObjectives_MetaData) }; // 4017967595
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_GenerationConfig = { "GenerationConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, GenerationConfig), Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GenerationConfig_MetaData), NewProp_GenerationConfig_MetaData) }; // 1670482787
void Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bProceduralSystemActive_SetBit(void* Obj)
{
	((AAURACRONPCGObjectiveSystem*)Obj)->bProceduralSystemActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bProceduralSystemActive = { "bProceduralSystemActive", nullptr, (EPropertyFlags)0x0020080000000034, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGObjectiveSystem), &Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bProceduralSystemActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bProceduralSystemActive_MetaData), NewProp_bProceduralSystemActive_MetaData) };
void Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bCatchUpMechanicsActive_SetBit(void* Obj)
{
	((AAURACRONPCGObjectiveSystem*)Obj)->bCatchUpMechanicsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bCatchUpMechanicsActive = { "bCatchUpMechanicsActive", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGObjectiveSystem), &Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bCatchUpMechanicsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCatchUpMechanicsActive_MetaData), NewProp_bCatchUpMechanicsActive_MetaData) };
void Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoStart_SetBit(void* Obj)
{
	((AAURACRONPCGObjectiveSystem*)Obj)->bAutoStart = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoStart = { "bAutoStart", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGObjectiveSystem), &Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoStart_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoStart_MetaData), NewProp_bAutoStart_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveRespawnTimers_Inner = { "ObjectiveRespawnTimers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(0, nullptr) }; // 3834150579
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveRespawnTimers = { "ObjectiveRespawnTimers", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ObjectiveRespawnTimers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveRespawnTimers_MetaData), NewProp_ObjectiveRespawnTimers_MetaData) }; // 3834150579
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ChaosIslandEventTimer = { "ChaosIslandEventTimer", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ChaosIslandEventTimer), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChaosIslandEventTimer_MetaData), NewProp_ChaosIslandEventTimer_MetaData) }; // 3834150579
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 657470012
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_NextChaosIslandIndex = { "NextChaosIslandIndex", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, NextChaosIslandIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NextChaosIslandIndex_MetaData), NewProp_NextChaosIslandIndex_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ProceduralGenerationTimer = { "ProceduralGenerationTimer", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ProceduralGenerationTimer), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProceduralGenerationTimer_MetaData), NewProp_ProceduralGenerationTimer_MetaData) }; // 3834150579
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveIDCounter = { "ObjectiveIDCounter", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, ObjectiveIDCounter), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ObjectiveIDCounter_MetaData), NewProp_ObjectiveIDCounter_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_PCGActorReferences = { "PCGActorReferences", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGObjectiveSystem, PCGActorReferences), Z_Construct_UScriptStruct_FAURACRONPCGActorReferences, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGActorReferences_MetaData), NewProp_PCGActorReferences_MetaData) }; // 2729947204
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveCreated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveDestroyed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnObjectiveCaptured,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_OnChaosIslandEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_Objectives_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_Objectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveMeshesByEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveCollisionComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveCollisionComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoGenerate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ActiveProceduralObjectives_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ActiveProceduralObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_GenerationConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bProceduralSystemActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bCatchUpMechanicsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_bAutoStart,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveRespawnTimers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveRespawnTimers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ChaosIslandEventTimer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_CurrentMapPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_NextChaosIslandIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ProceduralGenerationTimer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_ObjectiveIDCounter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::NewProp_PCGActorReferences,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::ClassParams = {
	&AAURACRONPCGObjectiveSystem::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGObjectiveSystem()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem.OuterSingleton, Z_Construct_UClass_AAURACRONPCGObjectiveSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem.OuterSingleton;
}
#if VALIDATE_CLASS_REPS
void AAURACRONPCGObjectiveSystem::ValidateGeneratedRepEnums(const TArray<struct FRepRecord>& ClassReps) const
{
	static FName Name_ActiveProceduralObjectives(TEXT("ActiveProceduralObjectives"));
	static FName Name_bProceduralSystemActive(TEXT("bProceduralSystemActive"));
	const bool bIsValid = true
		&& Name_ActiveProceduralObjectives == ClassReps[(int32)ENetFields_Private::ActiveProceduralObjectives].Property->GetFName()
		&& Name_bProceduralSystemActive == ClassReps[(int32)ENetFields_Private::bProceduralSystemActive].Property->GetFName();
	checkf(bIsValid, TEXT("UHT Generated Rep Indices do not match runtime populated Rep Indices for properties in AAURACRONPCGObjectiveSystem"));
}
#endif
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGObjectiveSystem);
AAURACRONPCGObjectiveSystem::~AAURACRONPCGObjectiveSystem() {}
// ********** End Class AAURACRONPCGObjectiveSystem ************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAURACRONObjectiveType_StaticEnum, TEXT("EAURACRONObjectiveType"), &Z_Registration_Info_UEnum_EAURACRONObjectiveType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2308998292U) },
		{ EAURACRONObjectiveState_StaticEnum, TEXT("EAURACRONObjectiveState"), &Z_Registration_Info_UEnum_EAURACRONObjectiveState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 600658364U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONObjectiveInfo::StaticStruct, Z_Construct_UScriptStruct_FAURACRONObjectiveInfo_Statics::NewStructOps, TEXT("AURACRONObjectiveInfo"), &Z_Registration_Info_UScriptStruct_FAURACRONObjectiveInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONObjectiveInfo), 3803898084U) },
		{ FAURACRONProceduralObjective::StaticStruct, Z_Construct_UScriptStruct_FAURACRONProceduralObjective_Statics::NewStructOps, TEXT("AURACRONProceduralObjective"), &Z_Registration_Info_UScriptStruct_FAURACRONProceduralObjective, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONProceduralObjective), 4017967595U) },
		{ FAURACRONObjectiveGenerationConfig::StaticStruct, Z_Construct_UScriptStruct_FAURACRONObjectiveGenerationConfig_Statics::NewStructOps, TEXT("AURACRONObjectiveGenerationConfig"), &Z_Registration_Info_UScriptStruct_FAURACRONObjectiveGenerationConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONObjectiveGenerationConfig), 1670482787U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGObjectiveSystem, AAURACRONPCGObjectiveSystem::StaticClass, TEXT("AAURACRONPCGObjectiveSystem"), &Z_Registration_Info_UClass_AAURACRONPCGObjectiveSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGObjectiveSystem), 467269076U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h__Script_AURACRON_3685493128(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGObjectiveSystem_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
