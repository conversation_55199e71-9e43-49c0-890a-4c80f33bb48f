// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGTrail.h"

#ifdef AURACRON_AURACRONPCGTrail_generated_h
#error "AURACRONPCGTrail.generated.h already included, missing '#pragma once' in AURACRONPCGTrail.h"
#endif
#define AURACRON_AURACRONPCGTrail_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

class AActor;
class ACharacter;
enum class EAURACRONMapPhase : uint8;
enum class EAURACRONTrailType : uint8;

// ********** Begin Class AAURACRONPCGTrail ********************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_27_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetPlayerPositionAlongTrail); \
	DECLARE_FUNCTION(execIsPlayerInTrail); \
	DECLARE_FUNCTION(execApplyTrailEffectsToPlayer); \
	DECLARE_FUNCTION(execHandlePlayerEndOverlap); \
	DECLARE_FUNCTION(execHandlePlayerOverlap); \
	DECLARE_FUNCTION(execClearControlPoints); \
	DECLARE_FUNCTION(execAddControlPoint); \
	DECLARE_FUNCTION(execSetTrailEndpoints); \
	DECLARE_FUNCTION(execSetActivityScale); \
	DECLARE_FUNCTION(execSetTrailVisibility); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execGenerateTrail); \
	DECLARE_FUNCTION(execGetTrailType); \
	DECLARE_FUNCTION(execSetTrailType);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGTrail_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_27_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGTrail(); \
	friend struct Z_Construct_UClass_AAURACRONPCGTrail_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGTrail_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGTrail, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGTrail_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGTrail)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_27_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGTrail(AAURACRONPCGTrail&&) = delete; \
	AAURACRONPCGTrail(const AAURACRONPCGTrail&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGTrail); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGTrail); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGTrail) \
	NO_API virtual ~AAURACRONPCGTrail();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_24_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_27_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_27_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_27_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h_27_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGTrail;

// ********** End Class AAURACRONPCGTrail **********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGTrail_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
