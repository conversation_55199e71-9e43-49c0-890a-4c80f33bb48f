// AURACRON.Build.cs
// Sistema de Sígilos AURACRON - Configuração de Build UE 5.6
// Dependências verificadas para GameplayAbilities, UMG, Niagara e GameplayTags

using UnrealBuildTool;

public class AURACRON : ModuleRules
{
    public AURACRON(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        
        // Setup Iris Support for Networking (UE 5.6) - ROBUSTO E COMPLETO
        SetupIrisSupport(Target, true);  // FUNCIONANDO: Implementação robusta com Iris

        // Push Model já está habilitado no AURACRON.Target.cs (PRODUCTION READY)
        
        // Use Engine's default WITH_PUSH_MODEL setting (não redefinir para evitar warnings)
        
        // Core Dependencies - UE 5.6 Base
        PublicDependencyModuleNames.AddRange(new string[]
        {
            // Core Dependencies PRIMEIRO
            "Core",
            "CoreUObject",
            "Engine",
            "InputCore",
            "EnhancedInput",
            "GameplayAbilities",
            "GameplayTags",
            "GameplayTasks",
            "UMG",
            "Slate",
            "SlateCore",
            "Niagara",
            "NiagaraCore",

            // PCG Dependencies - UE 5.6 PCG System
            "PCG",                    // Plugin PCG principal

            // Networking Dependencies (Required for FFastArraySerializer) - SOLUÇÃO ROBUSTA UE 5.6
            "NetCore",                // Para FFastArraySerializer - PRODUCTION READY
            "NetCommon"               // Dependência do NetCore
        });

        // SOLUÇÃO DEFINITIVA ENCONTRADA! Configuração EXATA baseada no Engine.Build.cs
        // Adicionar NetCore em Private E configurar PrivateIncludePaths como o Engine faz
        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "NetCore",                // SOLUÇÃO: NetCore em Private também para forçar linking
            "TraceLog",               // Dependência do NetCore
            "IrisCore"                // IrisCore precisa do NetCore para UEPushModelPrivate
        });

        // SOLUÇÃO CRÍTICA! Adicionar PrivateIncludePaths do NetCore como o Engine faz
        PrivateIncludePaths.AddRange(new string[]
        {
            System.IO.Path.Combine(GetModuleDirectory("NetCore"), "Private")
        });

        // SOLUÇÃO DEFINITIVA! Forçar linking da biblioteca NetCore específica
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            string NetCoreLibPath = @"C:\Program Files\Epic Games\UE_5.6\Engine\Intermediate\Build\Win64\x64\UnrealEditor\Development\NetCore\UnrealEditor-NetCore.lib";
            if (System.IO.File.Exists(NetCoreLibPath))
            {
                PublicAdditionalLibraries.Add(NetCoreLibPath);
                System.Console.WriteLine("AURACRON: Forçando linking da biblioteca NetCore: " + NetCoreLibPath);
            }
        }

        // Adicionar PrivateIncludePaths para NetCore como o Engine faz - PRODUCTION READY
        PrivateIncludePaths.AddRange(new string[]
        {
            System.IO.Path.Combine(GetModuleDirectory("NetCore"), "Private")
        });

        // Adicionar NetCore como PublicIncludePathModuleNames como o Engine faz - ROBUSTO
        PublicIncludePathModuleNames.AddRange(new string[]
        {
            "NetCore"
        });

        // Manter Iris habilitado para UE 5.6 (necessário para UEPushModelPrivate)
        // PublicDefinitions.Add("UE_NET_IRIS_ENABLED=0"); // Removido - conflitava com SetupIrisSupport

        // Configurações específicas para UE 5.6 - PRODUCTION READY
        // Deixar o Editor usar suas configurações padrão (UE_WITH_IRIS=1, WITH_PUSH_MODEL=1)

        // Forçar configurações de link para resolver UEPushModelPrivate
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        
        // Sigil System Dependencies - UE 5.6 APIs Verificadas
        PrivateDependencyModuleNames.AddRange(new string[]
        {
            // Gameplay Abilities System (GAS)
            "GameplayAbilities",      // Engine/Plugins/Runtime/GameplayAbilities
            "GameplayTags",           // Engine/Source/Runtime/GameplayTags
            "GameplayTasks",          // Engine/Source/Runtime/GameplayTasks
            
            // UI System (UMG)
            "UMG",                    // Engine/Source/Runtime/UMG
            "Slate",                  // Engine/Source/Runtime/Slate
            "SlateCore",              // Engine/Source/Runtime/SlateCore
            
            // VFX System (Niagara)
            "Niagara",                // Engine/Plugins/FX/Niagara
            "NiagaraCore",            // Engine/Plugins/FX/Niagara
            "NiagaraShader",          // Engine/Plugins/FX/Niagara
            
            // Networking & Replication
            "ReplicationGraph",       // Engine/Source/Runtime/ReplicationGraph
            
            // Additional Systems
            "DeveloperSettings",      // Engine/Source/Developer/Settings
            "ToolMenus",              // Engine/Source/Developer/ToolMenus
            "ApplicationCore",        // Engine/Source/Runtime/ApplicationCore
            "RenderCore",             // Engine/Source/Runtime/RenderCore
            "RHI"                     // Engine/Source/Runtime/RHI
        });
        
        // Include paths para compilação otimizada
        PublicIncludePaths.AddRange(new string[]
        {
            "AURACRON/Public",
            "AURACRON/Public/Sigils",
            "AURACRON/Public/UI",
            "AURACRON/Public/Effects",
            "AURACRON/Public/Data"
        });
        
        // Configurações de compilação para MOBA
        bEnableExceptions = false;
        bUseRTTI = false;
        
        // Otimizações para multiplayer
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_SHIPPING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_SHIPPING=0");
            PublicDefinitions.Add("AURACRON_DEBUG=1");
        }
        
        // Definições específicas do sistema de sígilos
        PublicDefinitions.AddRange(new string[]
        {
            "AURACRON_MAX_PLAYERS=10",
            "AURACRON_FUSION_TIME=360",
            "AURACRON_REFORGE_COOLDOWN=120",
            "AURACRON_MAX_SIGIL_SLOTS=6"
        });

        // SOLUÇÃO DEFINITIVA ENCONTRADA! Configuração robusta para UE 5.6 - PRODUCTION READY
        // Configurar Iris corretamente para resolver UEPushModelPrivate::MarkPropertyDirty
        SetupIrisSupport(Target);

        // SOLUÇÃO DEFINITIVA! Definições EXATAS baseadas no IrisCore.Build.cs
        if (Target.bUseIris == true)
        {
            PublicDefinitions.Add("UE_WITH_IRIS=1");
        }
        else
        {
            PublicDefinitions.Add("UE_WITH_IRIS=0");
        }

        // Definições adicionais para garantir linking correto
        PublicDefinitions.AddRange(new string[]
        {
            "WITH_PUSH_MODEL=1",                        // Habilitar Push Model
            "UE_NET_HAS_IRIS_FASTARRAY_BINDING=1",      // Habilitar binding FFastArray com Iris
            "IRIS_PROFILING_ENABLED=0"                  // Desabilitar profiling para performance
        });
    }
}