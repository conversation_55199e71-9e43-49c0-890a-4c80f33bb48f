// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGEnvironment.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGEnvironment() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironment();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FBreathingForestData();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGSettings_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FBreathingForestData **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FBreathingForestData;
class UScriptStruct* FBreathingForestData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FBreathingForestData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FBreathingForestData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FBreathingForestData, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("BreathingForestData"));
	}
	return Z_Registration_Info_UScriptStruct_FBreathingForestData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FBreathingForestData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura para armazenar informa\xc3\xa7\xc3\xb5""es de uma floresta respirante\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura para armazenar informa\xc3\xa7\xc3\xb5""es de uma floresta respirante" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TreePositions_MetaData[] = {
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TreePositions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TreePositions;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FBreathingForestData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBreathingForestData, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBreathingForestData, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_TreePositions_Inner = { "TreePositions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_TreePositions = { "TreePositions", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBreathingForestData, TreePositions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TreePositions_MetaData), NewProp_TreePositions_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FBreathingForestData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_TreePositions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewProp_TreePositions,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBreathingForestData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FBreathingForestData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"BreathingForestData",
	Z_Construct_UScriptStruct_FBreathingForestData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBreathingForestData_Statics::PropPointers),
	sizeof(FBreathingForestData),
	alignof(FBreathingForestData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBreathingForestData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FBreathingForestData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FBreathingForestData()
{
	if (!Z_Registration_Info_UScriptStruct_FBreathingForestData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FBreathingForestData.InnerSingleton, Z_Construct_UScriptStruct_FBreathingForestData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FBreathingForestData.InnerSingleton;
}
// ********** End ScriptStruct FBreathingForestData ************************************************

// ********** Begin Class AAURACRONPCGEnvironment Function GenerateEnvironment *********************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_GenerateEnvironment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Gerar o ambiente procedural\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar o ambiente procedural" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_GenerateEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "GenerateEnvironment", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GenerateEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_GenerateEnvironment_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_GenerateEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_GenerateEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execGenerateEnvironment)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateEnvironment();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function GenerateEnvironment ***********************

// ********** Begin Class AAURACRONPCGEnvironment Function GetEnvironmentType **********************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics
{
	struct AURACRONPCGEnvironment_eventGetEnvironmentType_Parms
	{
		EAURACRONEnvironmentType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Obter o tipo de ambiente\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter o tipo de ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventGetEnvironmentType_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2415364844
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "GetEnvironmentType", Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::AURACRONPCGEnvironment_eventGetEnvironmentType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::AURACRONPCGEnvironment_eventGetEnvironmentType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execGetEnvironmentType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONEnvironmentType*)Z_Param__Result=P_THIS->GetEnvironmentType();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function GetEnvironmentType ************************

// ********** Begin Class AAURACRONPCGEnvironment Function SetActivityScale ************************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics
{
	struct AURACRONPCGEnvironment_eventSetActivityScale_Parms
	{
		float Scale;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir a escala de atividade do ambiente (0.0 = preview, 1.0 = totalmente ativo)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir a escala de atividade do ambiente (0.0 = preview, 1.0 = totalmente ativo)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Scale;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::NewProp_Scale = { "Scale", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventSetActivityScale_Parms, Scale), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::NewProp_Scale,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "SetActivityScale", Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::AURACRONPCGEnvironment_eventSetActivityScale_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::AURACRONPCGEnvironment_eventSetActivityScale_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execSetActivityScale)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Scale);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetActivityScale(Z_Param_Scale);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function SetActivityScale **************************

// ********** Begin Class AAURACRONPCGEnvironment Function SetEnvironmentType **********************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics
{
	struct AURACRONPCGEnvironment_eventSetEnvironmentType_Parms
	{
		EAURACRONEnvironmentType NewType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configurar o tipo de ambiente\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configurar o tipo de ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::NewProp_NewType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::NewProp_NewType = { "NewType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventSetEnvironmentType_Parms, NewType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2415364844
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::NewProp_NewType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::NewProp_NewType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "SetEnvironmentType", Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::AURACRONPCGEnvironment_eventSetEnvironmentType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::AURACRONPCGEnvironment_eventSetEnvironmentType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execSetEnvironmentType)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_NewType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetEnvironmentType(EAURACRONEnvironmentType(Z_Param_NewType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function SetEnvironmentType ************************

// ********** Begin Class AAURACRONPCGEnvironment Function SetEnvironmentVisibility ****************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics
{
	struct AURACRONPCGEnvironment_eventSetEnvironmentVisibility_Parms
	{
		bool bVisible;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Definir a visibilidade do ambiente\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Definir a visibilidade do ambiente" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bVisible;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::NewProp_bVisible_SetBit(void* Obj)
{
	((AURACRONPCGEnvironment_eventSetEnvironmentVisibility_Parms*)Obj)->bVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::NewProp_bVisible = { "bVisible", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGEnvironment_eventSetEnvironmentVisibility_Parms), &Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::NewProp_bVisible_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::NewProp_bVisible,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "SetEnvironmentVisibility", Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::AURACRONPCGEnvironment_eventSetEnvironmentVisibility_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::AURACRONPCGEnvironment_eventSetEnvironmentVisibility_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execSetEnvironmentVisibility)
{
	P_GET_UBOOL(Z_Param_bVisible);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetEnvironmentVisibility(Z_Param_bVisible);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function SetEnvironmentVisibility ******************

// ********** Begin Class AAURACRONPCGEnvironment Function UpdateForMapPhase ***********************
struct Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics
{
	struct AURACRONPCGEnvironment_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Atualizar o ambiente com base na fase do mapa\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar o ambiente com base na fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGEnvironment_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 3530596558
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGEnvironment, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::AURACRONPCGEnvironment_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::AURACRONPCGEnvironment_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGEnvironment::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGEnvironment Function UpdateForMapPhase *************************

// ********** Begin Class AAURACRONPCGEnvironment **************************************************
void AAURACRONPCGEnvironment::StaticRegisterNativesAAURACRONPCGEnvironment()
{
	UClass* Class = AAURACRONPCGEnvironment::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GenerateEnvironment", &AAURACRONPCGEnvironment::execGenerateEnvironment },
		{ "GetEnvironmentType", &AAURACRONPCGEnvironment::execGetEnvironmentType },
		{ "SetActivityScale", &AAURACRONPCGEnvironment::execSetActivityScale },
		{ "SetEnvironmentType", &AAURACRONPCGEnvironment::execSetEnvironmentType },
		{ "SetEnvironmentVisibility", &AAURACRONPCGEnvironment::execSetEnvironmentVisibility },
		{ "UpdateForMapPhase", &AAURACRONPCGEnvironment::execUpdateForMapPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGEnvironment;
UClass* AAURACRONPCGEnvironment::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGEnvironment;
	if (!Z_Registration_Info_UClass_AAURACRONPCGEnvironment.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGEnvironment"),
			Z_Registration_Info_UClass_AAURACRONPCGEnvironment.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGEnvironment,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGEnvironment.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister()
{
	return AAURACRONPCGEnvironment::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGEnvironment_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Ator para gerenciar um ambiente procedural espec\xc3\xad""fico no AURACRON\n * Cada ambiente (Radiant Plains, Zephyr Firmament, Purgatory Realm) ter\xc3\xa1 sua pr\xc3\xb3pria inst\xc3\xa2ncia\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGEnvironment.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ator para gerenciar um ambiente procedural espec\xc3\xad""fico no AURACRON\nCada ambiente (Radiant Plains, Zephyr Firmament, Purgatory Realm) ter\xc3\xa1 sua pr\xc3\xb3pria inst\xc3\xa2ncia" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGComponent_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente PCG principal para gera\xc3\xa7\xc3\xa3o do ambiente\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente PCG principal para gera\xc3\xa7\xc3\xa3o do ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentSettings_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Configura\xc3\xa7\xc3\xb5""es PCG para este ambiente\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es PCG para este ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasCrystallinePlateaus_MetaData[] = {
		{ "Category", "AURACRON|PCG|RadiantPlains" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Caracter\xc3\xadsticas espec\xc3\xad""ficas do ambiente\n// Radiant Plains\n" },
#endif
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Caracter\xc3\xadsticas espec\xc3\xad""ficas do ambiente\nRadiant Plains" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasLivingCanyons_MetaData[] = {
		{ "Category", "AURACRON|PCG|RadiantPlains" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasBreathingForests_MetaData[] = {
		{ "Category", "AURACRON|PCG|RadiantPlains" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasTectonicBridges_MetaData[] = {
		{ "Category", "AURACRON|PCG|RadiantPlains" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::RadiantPlains" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasOrbitalArchipelagos_MetaData[] = {
		{ "Category", "AURACRON|PCG|ZephyrFirmament" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Zephyr Firmament\n" },
#endif
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Zephyr Firmament" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasAuroraBridges_MetaData[] = {
		{ "Category", "AURACRON|PCG|ZephyrFirmament" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasCloudFortresses_MetaData[] = {
		{ "Category", "AURACRON|PCG|ZephyrFirmament" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasStellarGardens_MetaData[] = {
		{ "Category", "AURACRON|PCG|ZephyrFirmament" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::ZephyrFirmament" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasSpectralPlains_MetaData[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Purgatory Realm\n" },
#endif
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Purgatory Realm" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasRiversOfSouls_MetaData[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasFragmentedStructures_MetaData[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasTemporalDistortionZones_MetaData[] = {
		{ "Category", "AURACRON|PCG|PurgatoryRealm" },
		{ "EditCondition", "EnvironmentType == EAURACRONEnvironmentType::PurgatoryRealm" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentType_MetaData[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tipo de ambiente\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo de ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivityScale_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Escala de atividade atual (0.0 = preview, 1.0 = totalmente ativo)\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Escala de atividade atual (0.0 = preview, 1.0 = totalmente ativo)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GeneratedActors_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Arrays para armazenar atores gerados\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Arrays para armazenar atores gerados" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BreathingForests_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Dados espec\xc3\xad""ficos para elementos din\xc3\xa2micos\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGEnvironment.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dados espec\xc3\xad""ficos para elementos din\xc3\xa2micos" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnvironmentSettings;
	static void NewProp_bHasCrystallinePlateaus_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasCrystallinePlateaus;
	static void NewProp_bHasLivingCanyons_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasLivingCanyons;
	static void NewProp_bHasBreathingForests_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasBreathingForests;
	static void NewProp_bHasTectonicBridges_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasTectonicBridges;
	static void NewProp_bHasOrbitalArchipelagos_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasOrbitalArchipelagos;
	static void NewProp_bHasAuroraBridges_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasAuroraBridges;
	static void NewProp_bHasCloudFortresses_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasCloudFortresses;
	static void NewProp_bHasStellarGardens_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasStellarGardens;
	static void NewProp_bHasSpectralPlains_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasSpectralPlains;
	static void NewProp_bHasRiversOfSouls_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasRiversOfSouls;
	static void NewProp_bHasFragmentedStructures_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasFragmentedStructures;
	static void NewProp_bHasTemporalDistortionZones_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasTemporalDistortionZones;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ActivityScale;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GeneratedActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_GeneratedActors;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BreathingForests_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BreathingForests;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_GenerateEnvironment, "GenerateEnvironment" }, // 2057581934
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_GetEnvironmentType, "GetEnvironmentType" }, // 1660008428
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_SetActivityScale, "SetActivityScale" }, // 2604318470
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentType, "SetEnvironmentType" }, // 2958619241
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_SetEnvironmentVisibility, "SetEnvironmentVisibility" }, // 3432250449
		{ &Z_Construct_UFunction_AAURACRONPCGEnvironment_UpdateForMapPhase, "UpdateForMapPhase" }, // 2488210218
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGEnvironment>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_PCGComponent = { "PCGComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, PCGComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGComponent_MetaData), NewProp_PCGComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_EnvironmentSettings = { "EnvironmentSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, EnvironmentSettings), Z_Construct_UClass_UPCGSettings_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentSettings_MetaData), NewProp_EnvironmentSettings_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCrystallinePlateaus_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasCrystallinePlateaus = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCrystallinePlateaus = { "bHasCrystallinePlateaus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCrystallinePlateaus_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasCrystallinePlateaus_MetaData), NewProp_bHasCrystallinePlateaus_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasLivingCanyons_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasLivingCanyons = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasLivingCanyons = { "bHasLivingCanyons", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasLivingCanyons_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasLivingCanyons_MetaData), NewProp_bHasLivingCanyons_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasBreathingForests_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasBreathingForests = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasBreathingForests = { "bHasBreathingForests", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasBreathingForests_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasBreathingForests_MetaData), NewProp_bHasBreathingForests_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTectonicBridges_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasTectonicBridges = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTectonicBridges = { "bHasTectonicBridges", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTectonicBridges_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasTectonicBridges_MetaData), NewProp_bHasTectonicBridges_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasOrbitalArchipelagos_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasOrbitalArchipelagos = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasOrbitalArchipelagos = { "bHasOrbitalArchipelagos", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasOrbitalArchipelagos_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasOrbitalArchipelagos_MetaData), NewProp_bHasOrbitalArchipelagos_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasAuroraBridges_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasAuroraBridges = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasAuroraBridges = { "bHasAuroraBridges", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasAuroraBridges_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasAuroraBridges_MetaData), NewProp_bHasAuroraBridges_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCloudFortresses_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasCloudFortresses = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCloudFortresses = { "bHasCloudFortresses", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCloudFortresses_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasCloudFortresses_MetaData), NewProp_bHasCloudFortresses_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasStellarGardens_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasStellarGardens = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasStellarGardens = { "bHasStellarGardens", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasStellarGardens_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasStellarGardens_MetaData), NewProp_bHasStellarGardens_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasSpectralPlains_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasSpectralPlains = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasSpectralPlains = { "bHasSpectralPlains", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasSpectralPlains_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasSpectralPlains_MetaData), NewProp_bHasSpectralPlains_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasRiversOfSouls_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasRiversOfSouls = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasRiversOfSouls = { "bHasRiversOfSouls", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasRiversOfSouls_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasRiversOfSouls_MetaData), NewProp_bHasRiversOfSouls_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasFragmentedStructures_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasFragmentedStructures = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasFragmentedStructures = { "bHasFragmentedStructures", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasFragmentedStructures_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasFragmentedStructures_MetaData), NewProp_bHasFragmentedStructures_MetaData) };
void Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTemporalDistortionZones_SetBit(void* Obj)
{
	((AAURACRONPCGEnvironment*)Obj)->bHasTemporalDistortionZones = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTemporalDistortionZones = { "bHasTemporalDistortionZones", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGEnvironment), &Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTemporalDistortionZones_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasTemporalDistortionZones_MetaData), NewProp_bHasTemporalDistortionZones_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0040000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentType_MetaData), NewProp_EnvironmentType_MetaData) }; // 2415364844
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_ActivityScale = { "ActivityScale", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, ActivityScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivityScale_MetaData), NewProp_ActivityScale_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_GeneratedActors_Inner = { "GeneratedActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_GeneratedActors = { "GeneratedActors", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, GeneratedActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GeneratedActors_MetaData), NewProp_GeneratedActors_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_BreathingForests_Inner = { "BreathingForests", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FBreathingForestData, METADATA_PARAMS(0, nullptr) }; // 59522974
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_BreathingForests = { "BreathingForests", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGEnvironment, BreathingForests), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BreathingForests_MetaData), NewProp_BreathingForests_MetaData) }; // 59522974
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_PCGComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_EnvironmentSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCrystallinePlateaus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasLivingCanyons,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasBreathingForests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTectonicBridges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasOrbitalArchipelagos,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasAuroraBridges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasCloudFortresses,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasStellarGardens,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasSpectralPlains,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasRiversOfSouls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasFragmentedStructures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_bHasTemporalDistortionZones,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_EnvironmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_ActivityScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_GeneratedActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_GeneratedActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_BreathingForests_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::NewProp_BreathingForests,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::ClassParams = {
	&AAURACRONPCGEnvironment::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGEnvironment()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGEnvironment.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGEnvironment.OuterSingleton, Z_Construct_UClass_AAURACRONPCGEnvironment_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGEnvironment.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGEnvironment);
AAURACRONPCGEnvironment::~AAURACRONPCGEnvironment() {}
// ********** End Class AAURACRONPCGEnvironment ****************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FBreathingForestData::StaticStruct, Z_Construct_UScriptStruct_FBreathingForestData_Statics::NewStructOps, TEXT("BreathingForestData"), &Z_Registration_Info_UScriptStruct_FBreathingForestData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FBreathingForestData), 59522974U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGEnvironment, AAURACRONPCGEnvironment::StaticClass, TEXT("AAURACRONPCGEnvironment"), &Z_Registration_Info_UClass_AAURACRONPCGEnvironment, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGEnvironment), 2437732428U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h__Script_AURACRON_1163618332(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
