// AURACRONPCGSanctuaryIsland.h
// Definição da classe ASanctuaryIsland para o sistema Prismal Flow

#pragma once

#include "CoreMinimal.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "PCG/AURACRONPCGIsland.h"
#include "AURACRONPCGSanctuaryIsland.generated.h"

/**
 * Implementação específica da Sanctuary Island
 * Ilha de refúgio e recuperação com fontes de cura, proteção e amplificadores de visão
 * Conforme documentação: Fontes de cura, escudos temporários, amplificadores de visão
 * Valor Estratégico: Zonas seguras para reagrupamento e cura
 */
UCLASS()
class AURACRON_API ASanctuaryIsland : public APrismalFlowIsland
{
    GENERATED_BODY()
    
public:
    ASanctuaryIsland();
    
    virtual void Tick(float DeltaTime) override;
    virtual void ApplyIslandEffect(AActor* OverlappingActor) override;
    
    // Implementa a funcionalidade de "zona segura para reagrupamento e cura"
    // conforme especificado na documentação
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Sanctuary Island")
    bool IsSecureZoneActive() const;
    
    /** Verifica se esta ilha está posicionada em uma seção calma do fluxo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|SanctuaryIsland")
    bool IsInCalmFlowSection() const;
    
    /** Define se esta ilha está em uma seção calma do fluxo */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|SanctuaryIsland")
    void SetInCalmFlowSection(bool bInCalmSection);
    
    // Concede efeito de cura ao jogador
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Sanctuary Island")
    void GrantHealingEffect(AActor* TargetActor);
    
    // Concede efeito de proteção ao jogador
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Sanctuary Island")
    void GrantProtectionEffect(AActor* TargetActor);
    
    // Concede efeito de amplificação de visão ao jogador
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Sanctuary Island")
    void GrantVisionAmplificationEffect(AActor* TargetActor);
    
protected:
    // Remove os efeitos de cura, proteção e amplificação de visão
    UFUNCTION()
    void RemoveIslandEffects(AActor* TargetActor);
    
    // Fonte de cura central
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Sanctuary Island")
    UStaticMeshComponent* HealingFountain;
    
    // Efeito visual da fonte de cura
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Sanctuary Island")
    UNiagaraComponent* HealingEffect;
    
    // Barreira protetora
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Sanctuary Island")
    UStaticMeshComponent* ProtectiveBarrier;
    
    // Efeito visual da barreira
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Sanctuary Island")
    UNiagaraComponent* BarrierEffect;
    
    // Árvore antiga
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Sanctuary Island")
    UStaticMeshComponent* AncientTree;
    
    // Amplificador de visão
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Sanctuary Island")
    UStaticMeshComponent* VisionAmplifier;
    
    // Efeito visual do amplificador de visão
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Sanctuary Island")
    UNiagaraComponent* VisionAmplifierEffect;
    
    // Nós de recursos
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Sanctuary Island")
    TArray<UStaticMeshComponent*> ResourceNodes;
    
    // Intensidade do efeito de cura
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Sanctuary Island")
    float HealingIntensity;
    
    // Duração do efeito de cura
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Sanctuary Island")
    float HealingDuration;
    
    // Intensidade do efeito de proteção
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Sanctuary Island")
    float ProtectionIntensity;
    
    // Duração do efeito de proteção
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Sanctuary Island")
    float ProtectionDuration;
    
    // Efeito visual de cura
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Sanctuary Island")
    UNiagaraSystem* HealingVisualEffect;
    
    // Efeito visual de proteção
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Sanctuary Island")
    UNiagaraSystem* ProtectionVisualEffect;
    
    // Efeito de gameplay para cura
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Sanctuary Island")
    TSubclassOf<UGameplayEffect> HealingGameplayEffect;
    
    // Efeito de gameplay para proteção
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Sanctuary Island")
    TSubclassOf<UGameplayEffect> ProtectionGameplayEffect;
    
    // Intensidade do efeito de amplificação de visão
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Sanctuary Island")
    float VisionAmplificationIntensity;
    
    // Duração do efeito de amplificação de visão
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Sanctuary Island")
    float VisionAmplificationDuration;
    
    // Efeito visual de amplificação de visão
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Sanctuary Island")
    UNiagaraSystem* VisionAmplificationVisualEffect;
    
    // Efeito de gameplay para amplificação de visão
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Sanctuary Island")
    TSubclassOf<UGameplayEffect> VisionAmplificationGameplayEffect;
    
    // Mapa de efeitos ativos por ator
    UPROPERTY()
    TMap<AActor*, TArray<FActiveGameplayEffectHandle>> ActiveEffects;
    
    // Tempo acumulado para efeitos visuais
    UPROPERTY()
    float AccumulatedTime;
    
    // Indica se a zona segura está ativa
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Sanctuary Island")
    bool bSecureZoneActive;
    
    // Raio da zona segura
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Sanctuary Island")
    float SecureZoneRadius;
    
    /** Indica se esta ilha está em uma seção calma do fluxo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|SanctuaryIsland")
    bool bInCalmFlowSection = false;
    
    virtual void UpdateIslandVisuals() override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
    
    // Ativa/desativa a zona segura
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Sanctuary Island")
    void SetSecureZoneActive(bool bActive);
    
    ///** Verifica se um ator está dentro da zona segura */
    UFUNCTION(BlueprintPure, Category = "AURACRON|SanctuaryIsland")
    bool IsActorInSecureZone(AActor* Actor) const;
    
    /** Aplica todos os efeitos benéficos da Ilha Santuário a um ator */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|SanctuaryIsland")
    void ApplyAllSanctuaryEffects(AActor* Actor);
    
    /** Atualiza a ilha com base na fase atual do mapa */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|SanctuaryIsland")
    void UpdateBasedOnMapPhase(EAURACRONMapPhase CurrentPhase);
};