// AURACRONPCGSanctuaryIsland.cpp
// Implementação da classe ASanctuaryIsland para o sistema Prismal Flow

#include "PCG/AURACRONPCGSanctuaryIsland.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/SphereComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "GameFramework/Character.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/StaticMeshActor.h"
#include "Net/UnrealNetwork.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemInterface.h"
#include "GameplayEffect.h"

ASanctuaryIsland::ASanctuaryIsland()
{
    // Configuração padrão
    PrimaryActorTick.bCanEverTick = true;
    
    // Inicializar propriedades
    HealingPower = 2.0f;
    ProtectionDuration = 30.0f;
    
    // Configurar componentes específicos da Sanctuary Island
    
    // Fonte de cura central
    HealingFountain = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("HealingFountain"));
    HealingFountain->SetupAttachment(RootComponent);
    HealingFountain->SetRelativeLocation(FVector(0.0f, 0.0f, 150.0f));
    HealingFountain->SetRelativeScale3D(FVector(1.5f, 1.5f, 2.0f));
    HealingFountain->SetCollisionProfileName(TEXT("BlockAll"));
    
    // Efeito de cura da fonte
    HealingEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("HealingEffect"));
    HealingEffect->SetupAttachment(HealingFountain);
    HealingEffect->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));
    
    // Árvore antiga
    AncientTree = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("AncientTree"));
    AncientTree->SetupAttachment(RootComponent);
    AncientTree->SetRelativeLocation(FVector(150.0f, 0.0f, 50.0f));
    AncientTree->SetRelativeScale3D(FVector(2.0f, 2.0f, 4.0f));
    AncientTree->SetCollisionProfileName(TEXT("BlockAll"));
    
    // Barreira protetora
    ProtectiveBarrier = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("ProtectiveBarrier"));
    ProtectiveBarrier->SetupAttachment(RootComponent);
    ProtectiveBarrier->SetRelativeLocation(FVector(0.0f, 0.0f, 0.0f));
    ProtectiveBarrier->SetRelativeScale3D(FVector(10.0f, 10.0f, 5.0f));
    ProtectiveBarrier->SetCollisionProfileName(TEXT("OverlapAll"));
    
    // Efeito da barreira protetora
    BarrierEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("BarrierEffect"));
    BarrierEffect->SetupAttachment(ProtectiveBarrier);
    
    // Definir o tipo de ilha como Sanctuary
    IslandType = EPrismalFlowIslandType::Sanctuary;
}

void ASanctuaryIsland::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Implementar efeitos visuais da ilha santuário
    if (bIsActive)
    {
        // Pulsar efeito de cura
        float Time = GetGameTimeSinceCreation();
        float PulseValue = 0.5f + 0.5f * FMath::Sin(Time * 1.0f);
        
        if (HealingEffect)
        {
            HealingEffect->SetFloatParameter(FName("Intensity"), PulseValue * 2.0f);
        }
        
        // Rotação suave da barreira protetora
        if (BarrierEffect)
        {
            BarrierEffect->SetFloatParameter(FName("RotationSpeed"), 0.2f);
            BarrierEffect->SetFloatParameter(FName("Opacity"), 0.3f + 0.2f * PulseValue);
        }
        
        // Verificar personagens dentro da área de cura
        if (GetWorld()->GetTimeSeconds() - LastHealTime > 1.0f) // Curar a cada segundo
        {
            LastHealTime = GetWorld()->GetTimeSeconds();
            HealCharactersInRange();
        }
    }
}

void ASanctuaryIsland::ApplyIslandEffect(AActor* OverlappingActor)
{
    // Verificar se a ilha está ativa
    if (!bIsActive || !OverlappingActor)
    {
        return;
    }
    
    // Verificar se o ator é um personagem jogável
    ACharacter* Character = Cast<ACharacter>(OverlappingActor);
    if (!Character)
    {
        return;
    }
    
    // Aplicar efeito visual de feedback
    if (HealingEffect)
    {
        HealingEffect->SetFloatParameter(FName("EffectIntensity"), 3.0f); // Intensificar efeito
        
        // Retornar à intensidade normal após um curto período
        FTimerHandle TimerHandle;
        GetWorldTimerManager().SetTimer(TimerHandle, [this]()
        {
            if (HealingEffect)
            {
                HealingEffect->SetFloatParameter(FName("EffectIntensity"), 1.0f);
            }
        }, 0.5f, false);
    }
    
    // Aplicar cura imediata
    ApplyHealing(OverlappingActor);
    
    // Conceder proteção temporária
    GrantProtection(OverlappingActor);
}

void ASanctuaryIsland::ApplyHealing(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        return;
    }
    
    // Aplicar efeito de cura
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // Criar GameplayEffect específico para cura
    UGameplayEffect* HealingEffect = NewObject<UGameplayEffect>(this, FName("GE_SanctuaryIslandHealing"));
    HealingEffect->DurationPolicy = EGameplayEffectDurationType::Instant;
    
    // Modificador para cura instantânea (+75 HP)
    FGameplayModifierInfo HealModifier;
    HealModifier.ModifierMagnitude = FScalableFloat(75.0f);
    HealModifier.ModifierOp = EGameplayModOp::Additive;
    HealModifier.Attribute = FGameplayAttribute(); // Atributo de HP
    HealingEffect->Modifiers.Add(HealModifier);
    
    // Modificador para regeneração contínua (+5 HP/s por 10s)
    FGameplayModifierInfo RegenModifier;
    RegenModifier.ModifierMagnitude = FScalableFloat(5.0f);
    RegenModifier.ModifierOp = EGameplayModOp::Additive;
    RegenModifier.Attribute = FGameplayAttribute(); // Atributo de regeneração
    HealingEffect->Modifiers.Add(RegenModifier);
    
    // Aplicar o efeito de cura
    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(HealingEffect->GetClass(), 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        
        if (ActiveEffect.IsValid())
        {
            UE_LOG(LogTemp, Log, TEXT("Sanctuary Island: Cura aplicada com sucesso em %s (+75 HP + 5 HP/s)"), *TargetActor->GetName());
        }
    }
    
    // Criar feedback visual de cura
    UNiagaraSystem* HealingVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_SanctuaryIslandHealing"));
    if (HealingVFX)
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            HealingVFX,
            TargetActor->GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(1.0f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }
}

void ASanctuaryIsland::GrantProtection(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        return;
    }
    
    // Aplicar efeito de proteção
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // Criar GameplayEffect específico para proteção
    UGameplayEffect* ProtectionEffect = NewObject<UGameplayEffect>(this, FName("GE_SanctuaryIslandProtection"));
    ProtectionEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
    ProtectionEffect->DurationMagnitude = FScalableFloat(ProtectionDuration);
    
    // Modificador para resistência a dano (+40%)
    FGameplayModifierInfo DamageResistanceModifier;
    DamageResistanceModifier.ModifierMagnitude = FScalableFloat(0.6f); // 40% de redução = 60% do dano
    DamageResistanceModifier.ModifierOp = EGameplayModOp::Multiplicative;
    DamageResistanceModifier.Attribute = FGameplayAttribute(); // Atributo de resistência a dano
    ProtectionEffect->Modifiers.Add(DamageResistanceModifier);
    
    // Modificador para imunidade a efeitos negativos
    FGameplayModifierInfo StatusImmunityModifier;
    StatusImmunityModifier.ModifierMagnitude = FScalableFloat(1.0f);
    StatusImmunityModifier.ModifierOp = EGameplayModOp::Override;
    StatusImmunityModifier.Attribute = FGameplayAttribute(); // Atributo de imunidade a status
    ProtectionEffect->Modifiers.Add(StatusImmunityModifier);
    
    // Aplicar o efeito de proteção
    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(ProtectionEffect->GetClass(), 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        
        if (ActiveEffect.IsValid())
        {
            UE_LOG(LogTemp, Log, TEXT("Sanctuary Island: Proteção concedida para %s por %.1f segundos (-40% dano, imunidade a status)"), *TargetActor->GetName(), ProtectionDuration);
        }
    }
    
    // Criar feedback visual de proteção
    UNiagaraSystem* ProtectionVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_SanctuaryIslandProtection"));
    if (ProtectionVFX)
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            ProtectionVFX,
            TargetActor->GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(1.2f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }
}

void ASanctuaryIsland::HealCharactersInRange()
{
    // Obter todos os personagens na área de cura
    TArray<AActor*> OverlappingActors;
    if (InteractionArea)
    {
        InteractionArea->GetOverlappingActors(OverlappingActors, ACharacter::StaticClass());
    }
    
    // Aplicar cura a cada personagem na área
    for (AActor* Actor : OverlappingActors)
    {
        ApplyHealing(Actor);
    }
}