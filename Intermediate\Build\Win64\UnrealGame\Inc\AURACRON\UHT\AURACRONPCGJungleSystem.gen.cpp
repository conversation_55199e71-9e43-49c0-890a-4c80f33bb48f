// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGJungleSystem.h"
#include "Engine/TimerHandle.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGJungleSystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGJungleSystem();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGJungleSystem_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONJungleCampInfo();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMeshComponentArray();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAURACRONMeshComponentArray ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray;
class UScriptStruct* FAURACRONMeshComponentArray::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONMeshComponentArray, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONMeshComponentArray"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Estrutura wrapper para array de componentes de mesh (resolve problema UHT com TMap<Enum, TArray<Type>>)\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Estrutura wrapper para array de componentes de mesh (resolve problema UHT com TMap<Enum, TArray<Type>>)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponents_MetaData[] = {
		{ "Category", "AURACRONMeshComponentArray" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_MeshComponents;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONMeshComponentArray>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::NewProp_MeshComponents_Inner = { "MeshComponents", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::NewProp_MeshComponents = { "MeshComponents", nullptr, (EPropertyFlags)0x001000800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONMeshComponentArray, MeshComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponents_MetaData), NewProp_MeshComponents_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::NewProp_MeshComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::NewProp_MeshComponents,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONMeshComponentArray",
	Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::PropPointers),
	sizeof(FAURACRONMeshComponentArray),
	alignof(FAURACRONMeshComponentArray),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000205),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONMeshComponentArray()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONMeshComponentArray *****************************************

// ********** Begin Enum EAURACRONJungleCampType ***************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONJungleCampType;
static UEnum* EAURACRONJungleCampType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONJungleCampType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONJungleCampType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONJungleCampType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONJungleCampType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONJungleCampType>()
{
	return EAURACRONJungleCampType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ChaosEssence.Comment", "// Blue Buff equivalent (mana regen + CDR)\n" },
		{ "ChaosEssence.DisplayName", "Chaos Essence" },
		{ "ChaosEssence.Name", "EAURACRONJungleCampType::ChaosEssence" },
		{ "ChaosEssence.ToolTip", "Blue Buff equivalent (mana regen + CDR)" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Tipos de jungle camps baseados no LoL\n */" },
#endif
		{ "FluxCrawler.Comment", "// Raptors equivalent\n" },
		{ "FluxCrawler.DisplayName", "Flux Crawler" },
		{ "FluxCrawler.Name", "EAURACRONJungleCampType::FluxCrawler" },
		{ "FluxCrawler.ToolTip", "Raptors equivalent" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
		{ "PrismalToad.Comment", "// Krugs equivalent\n" },
		{ "PrismalToad.DisplayName", "Prismal Toad" },
		{ "PrismalToad.Name", "EAURACRONJungleCampType::PrismalToad" },
		{ "PrismalToad.ToolTip", "Krugs equivalent" },
		{ "RadiantEssence.Comment", "// BUFF CAMPS (equivalentes ao Blue/Red Buff)\n" },
		{ "RadiantEssence.DisplayName", "Radiant Essence" },
		{ "RadiantEssence.Name", "EAURACRONJungleCampType::RadiantEssence" },
		{ "RadiantEssence.ToolTip", "BUFF CAMPS (equivalentes ao Blue/Red Buff)" },
		{ "SpectralPack.Comment", "// Gromp equivalent  \n" },
		{ "SpectralPack.DisplayName", "Spectral Pack" },
		{ "SpectralPack.Name", "EAURACRONJungleCampType::SpectralPack" },
		{ "SpectralPack.ToolTip", "Gromp equivalent" },
		{ "StoneGuardians.Comment", "// CAMPS NORMAIS (baseados no LoL)\n" },
		{ "StoneGuardians.DisplayName", "Stone Guardians" },
		{ "StoneGuardians.Name", "EAURACRONJungleCampType::StoneGuardians" },
		{ "StoneGuardians.ToolTip", "CAMPS NORMAIS (baseados no LoL)" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipos de jungle camps baseados no LoL" },
#endif
		{ "WindSpirits.Comment", "// Wolves equivalent\n" },
		{ "WindSpirits.DisplayName", "Wind Spirits" },
		{ "WindSpirits.Name", "EAURACRONJungleCampType::WindSpirits" },
		{ "WindSpirits.ToolTip", "Wolves equivalent" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONJungleCampType::RadiantEssence", (int64)EAURACRONJungleCampType::RadiantEssence },
		{ "EAURACRONJungleCampType::ChaosEssence", (int64)EAURACRONJungleCampType::ChaosEssence },
		{ "EAURACRONJungleCampType::StoneGuardians", (int64)EAURACRONJungleCampType::StoneGuardians },
		{ "EAURACRONJungleCampType::PrismalToad", (int64)EAURACRONJungleCampType::PrismalToad },
		{ "EAURACRONJungleCampType::SpectralPack", (int64)EAURACRONJungleCampType::SpectralPack },
		{ "EAURACRONJungleCampType::WindSpirits", (int64)EAURACRONJungleCampType::WindSpirits },
		{ "EAURACRONJungleCampType::FluxCrawler", (int64)EAURACRONJungleCampType::FluxCrawler },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONJungleCampType",
	"EAURACRONJungleCampType",
	Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONJungleCampType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONJungleCampType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONJungleCampType.InnerSingleton;
}
// ********** End Enum EAURACRONJungleCampType *****************************************************

// ********** Begin ScriptStruct FAURACRONJungleCampInfo *******************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo;
class UScriptStruct* FAURACRONJungleCampInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONJungleCampInfo, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONJungleCampInfo"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Informa\xc3\xa7\xc3\xb5""es de um jungle camp\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es de um jungle camp" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampType_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tipo do camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tipo do camp" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PositionsByEnvironment_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Posi\xc3\xa7\xc3\xa3o do camp para cada ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Posi\xc3\xa7\xc3\xa3o do camp para cada ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampRadius_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio do camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio do camp" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RespawnTime_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo de respawn em segundos */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo de respawn em segundos" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsBuffCamp_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se \xc3\xa9 um buff camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se \xc3\xa9 um buff camp" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DifficultyLevel_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** N\xc3\xadvel de dificuldade */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "N\xc3\xadvel de dificuldade" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rewards_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Recompensas do camp */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Recompensas do camp" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se o camp est\xc3\xa1 ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se o camp est\xc3\xa1 ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeUntilRespawn_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tempo at\xc3\xa9 pr\xc3\xb3ximo respawn */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo at\xc3\xa9 pr\xc3\xb3ximo respawn" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MapSide_MetaData[] = {
		{ "Category", "AURACRONJungleCampInfo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lado do mapa (0 = Team 1, 1 = Team 2, 2 = Neutro) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lado do mapa (0 = Team 1, 1 = Team 2, 2 = Neutro)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CampType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CampType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PositionsByEnvironment_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PositionsByEnvironment_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PositionsByEnvironment_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PositionsByEnvironment;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CampRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RespawnTime;
	static void NewProp_bIsBuffCamp_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsBuffCamp;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DifficultyLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Rewards_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Rewards_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Rewards;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeUntilRespawn;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MapSide;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONJungleCampInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CampType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CampType = { "CampType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, CampType), Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampType_MetaData), NewProp_CampType_MetaData) }; // 2623526025
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment_ValueProp = { "PositionsByEnvironment", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp = { "PositionsByEnvironment_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2415364844
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment = { "PositionsByEnvironment", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, PositionsByEnvironment), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PositionsByEnvironment_MetaData), NewProp_PositionsByEnvironment_MetaData) }; // 2415364844
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CampRadius = { "CampRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, CampRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampRadius_MetaData), NewProp_CampRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_RespawnTime = { "RespawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, RespawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RespawnTime_MetaData), NewProp_RespawnTime_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsBuffCamp_SetBit(void* Obj)
{
	((FAURACRONJungleCampInfo*)Obj)->bIsBuffCamp = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsBuffCamp = { "bIsBuffCamp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONJungleCampInfo), &Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsBuffCamp_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsBuffCamp_MetaData), NewProp_bIsBuffCamp_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_DifficultyLevel = { "DifficultyLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, DifficultyLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DifficultyLevel_MetaData), NewProp_DifficultyLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_Rewards_ValueProp = { "Rewards", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_Rewards_Key_KeyProp = { "Rewards_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_Rewards = { "Rewards", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, Rewards), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rewards_MetaData), NewProp_Rewards_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAURACRONJungleCampInfo*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONJungleCampInfo), &Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_TimeUntilRespawn = { "TimeUntilRespawn", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, TimeUntilRespawn), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeUntilRespawn_MetaData), NewProp_TimeUntilRespawn_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_MapSide = { "MapSide", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONJungleCampInfo, MapSide), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MapSide_MetaData), NewProp_MapSide_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CampType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CampType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_PositionsByEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_CampRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_RespawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsBuffCamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_DifficultyLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_Rewards_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_Rewards_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_Rewards,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_TimeUntilRespawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewProp_MapSide,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONJungleCampInfo",
	Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::PropPointers),
	sizeof(FAURACRONJungleCampInfo),
	alignof(FAURACRONJungleCampInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONJungleCampInfo()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONJungleCampInfo *********************************************

// ********** Begin Class AAURACRONPCGJungleSystem Function ClearCamp ******************************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics
{
	struct AURACRONPCGJungleSystem_eventClearCamp_Parms
	{
		int32 CampIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Limpar camp (quando morto) */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Limpar camp (quando morto)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CampIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::NewProp_CampIndex = { "CampIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventClearCamp_Parms, CampIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::NewProp_CampIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "ClearCamp", Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::AURACRONPCGJungleSystem_eventClearCamp_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::AURACRONPCGJungleSystem_eventClearCamp_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execClearCamp)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_CampIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearCamp(Z_Param_CampIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function ClearCamp ********************************

// ********** Begin Class AAURACRONPCGJungleSystem Function GenerateCampsForEnvironment ************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics
{
	struct AURACRONPCGJungleSystem_eventGenerateCampsForEnvironment_Parms
	{
		EAURACRONEnvironmentType Environment;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar camps para ambiente espec\xc3\xad""fico */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar camps para ambiente espec\xc3\xad""fico" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Environment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Environment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::NewProp_Environment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::NewProp_Environment = { "Environment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGenerateCampsForEnvironment_Parms, Environment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2415364844
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::NewProp_Environment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::NewProp_Environment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "GenerateCampsForEnvironment", Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::AURACRONPCGJungleSystem_eventGenerateCampsForEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::AURACRONPCGJungleSystem_eventGenerateCampsForEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execGenerateCampsForEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_Environment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateCampsForEnvironment(EAURACRONEnvironmentType(Z_Param_Environment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function GenerateCampsForEnvironment **************

// ********** Begin Class AAURACRONPCGJungleSystem Function GenerateJungleCamps ********************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateJungleCamps_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gerar todos os jungle camps */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gerar todos os jungle camps" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateJungleCamps_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "GenerateJungleCamps", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateJungleCamps_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateJungleCamps_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateJungleCamps()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateJungleCamps_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execGenerateJungleCamps)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateJungleCamps();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function GenerateJungleCamps **********************

// ********** Begin Class AAURACRONPCGJungleSystem Function GetAllCamps ****************************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics
{
	struct AURACRONPCGJungleSystem_eventGetAllCamps_Parms
	{
		TArray<FAURACRONJungleCampInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter informa\xc3\xa7\xc3\xb5""es de todos os camps */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter informa\xc3\xa7\xc3\xb5""es de todos os camps" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONJungleCampInfo, METADATA_PARAMS(0, nullptr) }; // 888297853
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetAllCamps_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 888297853
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "GetAllCamps", Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::AURACRONPCGJungleSystem_eventGetAllCamps_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::AURACRONPCGJungleSystem_eventGetAllCamps_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execGetAllCamps)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONJungleCampInfo>*)Z_Param__Result=P_THIS->GetAllCamps();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function GetAllCamps ******************************

// ********** Begin Class AAURACRONPCGJungleSystem Function GetCampsBySide *************************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics
{
	struct AURACRONPCGJungleSystem_eventGetCampsBySide_Parms
	{
		int32 MapSide;
		TArray<FAURACRONJungleCampInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter camps por lado do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter camps por lado do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_MapSide;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::NewProp_MapSide = { "MapSide", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetCampsBySide_Parms, MapSide), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONJungleCampInfo, METADATA_PARAMS(0, nullptr) }; // 888297853
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetCampsBySide_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 888297853
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::NewProp_MapSide,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "GetCampsBySide", Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::AURACRONPCGJungleSystem_eventGetCampsBySide_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::AURACRONPCGJungleSystem_eventGetCampsBySide_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execGetCampsBySide)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_MapSide);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONJungleCampInfo>*)Z_Param__Result=P_THIS->GetCampsBySide(Z_Param_MapSide);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function GetCampsBySide ***************************

// ********** Begin Class AAURACRONPCGJungleSystem Function GetCampsByType *************************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics
{
	struct AURACRONPCGJungleSystem_eventGetCampsByType_Parms
	{
		EAURACRONJungleCampType CampType;
		TArray<FAURACRONJungleCampInfo> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Obter camps por tipo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Obter camps por tipo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CampType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CampType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_CampType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_CampType = { "CampType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetCampsByType_Parms, CampType), Z_Construct_UEnum_AURACRON_EAURACRONJungleCampType, METADATA_PARAMS(0, nullptr) }; // 2623526025
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONJungleCampInfo, METADATA_PARAMS(0, nullptr) }; // 888297853
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventGetCampsByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 888297853
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_CampType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_CampType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "GetCampsByType", Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::AURACRONPCGJungleSystem_eventGetCampsByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::AURACRONPCGJungleSystem_eventGetCampsByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execGetCampsByType)
{
	P_GET_ENUM(EAURACRONJungleCampType,Z_Param_CampType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAURACRONJungleCampInfo>*)Z_Param__Result=P_THIS->GetCampsByType(EAURACRONJungleCampType(Z_Param_CampType));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function GetCampsByType ***************************

// ********** Begin Class AAURACRONPCGJungleSystem Function IsCampAvailable ************************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics
{
	struct AURACRONPCGJungleSystem_eventIsCampAvailable_Parms
	{
		int32 CampIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Verificar se camp est\xc3\xa1 dispon\xc3\xadvel */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Verificar se camp est\xc3\xa1 dispon\xc3\xadvel" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CampIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::NewProp_CampIndex = { "CampIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventIsCampAvailable_Parms, CampIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AURACRONPCGJungleSystem_eventIsCampAvailable_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AURACRONPCGJungleSystem_eventIsCampAvailable_Parms), &Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::NewProp_CampIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "IsCampAvailable", Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::AURACRONPCGJungleSystem_eventIsCampAvailable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::AURACRONPCGJungleSystem_eventIsCampAvailable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execIsCampAvailable)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_CampIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsCampAvailable(Z_Param_CampIndex);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function IsCampAvailable **************************

// ********** Begin Class AAURACRONPCGJungleSystem Function UpdateForEnvironment *******************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics
{
	struct AURACRONPCGJungleSystem_eventUpdateForEnvironment_Parms
	{
		EAURACRONEnvironmentType NewEnvironment;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar para novo ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar para novo ambiente" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewEnvironment;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment = { "NewEnvironment", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventUpdateForEnvironment_Parms, NewEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2415364844
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::NewProp_NewEnvironment,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "UpdateForEnvironment", Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::AURACRONPCGJungleSystem_eventUpdateForEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::AURACRONPCGJungleSystem_eventUpdateForEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execUpdateForEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_NewEnvironment);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForEnvironment(EAURACRONEnvironmentType(Z_Param_NewEnvironment));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function UpdateForEnvironment *********************

// ********** Begin Class AAURACRONPCGJungleSystem Function UpdateForMapPhase **********************
struct Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics
{
	struct AURACRONPCGJungleSystem_eventUpdateForMapPhase_Parms
	{
		EAURACRONMapPhase MapPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar para nova fase do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar para nova fase do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_MapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_MapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::NewProp_MapPhase = { "MapPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGJungleSystem_eventUpdateForMapPhase_Parms, MapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 657470012
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::NewProp_MapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::NewProp_MapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGJungleSystem, nullptr, "UpdateForMapPhase", Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::AURACRONPCGJungleSystem_eventUpdateForMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::AURACRONPCGJungleSystem_eventUpdateForMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGJungleSystem::execUpdateForMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_MapPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateForMapPhase(EAURACRONMapPhase(Z_Param_MapPhase));
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGJungleSystem Function UpdateForMapPhase ************************

// ********** Begin Class AAURACRONPCGJungleSystem *************************************************
void AAURACRONPCGJungleSystem::StaticRegisterNativesAAURACRONPCGJungleSystem()
{
	UClass* Class = AAURACRONPCGJungleSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearCamp", &AAURACRONPCGJungleSystem::execClearCamp },
		{ "GenerateCampsForEnvironment", &AAURACRONPCGJungleSystem::execGenerateCampsForEnvironment },
		{ "GenerateJungleCamps", &AAURACRONPCGJungleSystem::execGenerateJungleCamps },
		{ "GetAllCamps", &AAURACRONPCGJungleSystem::execGetAllCamps },
		{ "GetCampsBySide", &AAURACRONPCGJungleSystem::execGetCampsBySide },
		{ "GetCampsByType", &AAURACRONPCGJungleSystem::execGetCampsByType },
		{ "IsCampAvailable", &AAURACRONPCGJungleSystem::execIsCampAvailable },
		{ "UpdateForEnvironment", &AAURACRONPCGJungleSystem::execUpdateForEnvironment },
		{ "UpdateForMapPhase", &AAURACRONPCGJungleSystem::execUpdateForMapPhase },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGJungleSystem;
UClass* AAURACRONPCGJungleSystem::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGJungleSystem;
	if (!Z_Registration_Info_UClass_AAURACRONPCGJungleSystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGJungleSystem"),
			Z_Registration_Info_UClass_AAURACRONPCGJungleSystem.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGJungleSystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGJungleSystem.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGJungleSystem_NoRegister()
{
	return AAURACRONPCGJungleSystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Sistema de jungle para AURACRON baseado no layout do LoL\n * 12 camps sim\xc3\xa9tricos distribu\xc3\xad""dos entre as lanes\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGJungleSystem.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sistema de jungle para AURACRON baseado no layout do LoL\n12 camps sim\xc3\xa9tricos distribu\xc3\xad""dos entre as lanes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JungleCamps_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Informa\xc3\xa7\xc3\xb5""es de todos os jungle camps */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Informa\xc3\xa7\xc3\xb5""es de todos os jungle camps" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampMeshesByEnvironment_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes visuais dos camps por ambiente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes visuais dos camps por ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampCollisionComponents_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Componentes de colis\xc3\xa3o dos camps */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes de colis\xc3\xa3o dos camps" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoGenerate_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve gerar automaticamente no BeginPlay */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve gerar automaticamente no BeginPlay" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentEnvironment_MetaData[] = {
		{ "Category", "AURACRON|JungleSystem" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Ambiente atualmente ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Ambiente atualmente ativo" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampRespawnTimers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timers de respawn dos camps */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timers de respawn dos camps" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fase atual do mapa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGJungleSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_JungleCamps_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_JungleCamps;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CampMeshesByEnvironment_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CampMeshesByEnvironment_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CampMeshesByEnvironment_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CampMeshesByEnvironment;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CampCollisionComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CampCollisionComponents;
	static void NewProp_bAutoGenerate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoGenerate;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentEnvironment_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentEnvironment;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CampRespawnTimers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CampRespawnTimers;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_ClearCamp, "ClearCamp" }, // 1168696491
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateCampsForEnvironment, "GenerateCampsForEnvironment" }, // 945747384
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_GenerateJungleCamps, "GenerateJungleCamps" }, // 3582977273
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetAllCamps, "GetAllCamps" }, // 3070923686
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsBySide, "GetCampsBySide" }, // 1609808761
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_GetCampsByType, "GetCampsByType" }, // 2051654297
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_IsCampAvailable, "IsCampAvailable" }, // 833303014
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForEnvironment, "UpdateForEnvironment" }, // 3345707086
		{ &Z_Construct_UFunction_AAURACRONPCGJungleSystem_UpdateForMapPhase, "UpdateForMapPhase" }, // 3912663478
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGJungleSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_JungleCamps_Inner = { "JungleCamps", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONJungleCampInfo, METADATA_PARAMS(0, nullptr) }; // 888297853
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_JungleCamps = { "JungleCamps", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, JungleCamps), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JungleCamps_MetaData), NewProp_JungleCamps_MetaData) }; // 888297853
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment_ValueProp = { "CampMeshesByEnvironment", nullptr, (EPropertyFlags)0x0000008000020001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONMeshComponentArray, METADATA_PARAMS(0, nullptr) }; // 3350711660
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment_Key_KeyProp = { "CampMeshesByEnvironment_Key", nullptr, (EPropertyFlags)0x0000008000020001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2415364844
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment = { "CampMeshesByEnvironment", nullptr, (EPropertyFlags)0x0020088000020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, CampMeshesByEnvironment), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampMeshesByEnvironment_MetaData), NewProp_CampMeshesByEnvironment_MetaData) }; // 2415364844 3350711660
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampCollisionComponents_Inner = { "CampCollisionComponents", nullptr, (EPropertyFlags)0x00000000000a0008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampCollisionComponents = { "CampCollisionComponents", nullptr, (EPropertyFlags)0x002008800002001d, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, CampCollisionComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampCollisionComponents_MetaData), NewProp_CampCollisionComponents_MetaData) };
void Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bAutoGenerate_SetBit(void* Obj)
{
	((AAURACRONPCGJungleSystem*)Obj)->bAutoGenerate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bAutoGenerate = { "bAutoGenerate", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGJungleSystem), &Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bAutoGenerate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoGenerate_MetaData), NewProp_bAutoGenerate_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentEnvironment_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentEnvironment = { "CurrentEnvironment", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, CurrentEnvironment), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentEnvironment_MetaData), NewProp_CurrentEnvironment_MetaData) }; // 2415364844
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampRespawnTimers_Inner = { "CampRespawnTimers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(0, nullptr) }; // 3834150579
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampRespawnTimers = { "CampRespawnTimers", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, CampRespawnTimers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampRespawnTimers_MetaData), NewProp_CampRespawnTimers_MetaData) }; // 3834150579
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGJungleSystem, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 657470012
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_JungleCamps_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_JungleCamps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampMeshesByEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampCollisionComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampCollisionComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_bAutoGenerate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentEnvironment_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentEnvironment,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampRespawnTimers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CampRespawnTimers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::NewProp_CurrentMapPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::ClassParams = {
	&AAURACRONPCGJungleSystem::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGJungleSystem()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGJungleSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGJungleSystem.OuterSingleton, Z_Construct_UClass_AAURACRONPCGJungleSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGJungleSystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGJungleSystem);
AAURACRONPCGJungleSystem::~AAURACRONPCGJungleSystem() {}
// ********** End Class AAURACRONPCGJungleSystem ***************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAURACRONJungleCampType_StaticEnum, TEXT("EAURACRONJungleCampType"), &Z_Registration_Info_UEnum_EAURACRONJungleCampType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2623526025U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONMeshComponentArray::StaticStruct, Z_Construct_UScriptStruct_FAURACRONMeshComponentArray_Statics::NewStructOps, TEXT("AURACRONMeshComponentArray"), &Z_Registration_Info_UScriptStruct_FAURACRONMeshComponentArray, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONMeshComponentArray), 3350711660U) },
		{ FAURACRONJungleCampInfo::StaticStruct, Z_Construct_UScriptStruct_FAURACRONJungleCampInfo_Statics::NewStructOps, TEXT("AURACRONJungleCampInfo"), &Z_Registration_Info_UScriptStruct_FAURACRONJungleCampInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONJungleCampInfo), 888297853U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGJungleSystem, AAURACRONPCGJungleSystem::StaticClass, TEXT("AAURACRONPCGJungleSystem"), &Z_Registration_Info_UClass_AAURACRONPCGJungleSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGJungleSystem), 2257394813U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_3126968344(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGJungleSystem_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
