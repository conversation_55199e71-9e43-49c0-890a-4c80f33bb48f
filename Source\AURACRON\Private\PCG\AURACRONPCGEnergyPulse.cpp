// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#include "PCG/AURACRONPCGEnergyPulse.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/PointLightComponent.h"
#include "Components/AudioComponent.h"
#include "Components/SphereComponent.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "GameFramework/PlayerController.h"
#include "Engine/World.h"

// Sets default values
AAURACRONPCGEnergyPulse::AAURACRONPCGEnergyPulse()
{
    // Set this actor to call Tick() every frame
    PrimaryActorTick.bCanEverTick = true;

    // Criar componente raiz
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

    // Criar componente de partículas Niagara
    PulseEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("PulseEffect"));
    PulseEffect->SetupAttachment(RootComponent);

    // Criar componente de luz
    PulseLight = CreateDefaultSubobject<UPointLightComponent>(TEXT("PulseLight"));
    PulseLight->SetupAttachment(RootComponent);
    PulseLight->SetLightColor(FLinearColor::White);
    PulseLight->SetIntensity(5000.0f);
    PulseLight->SetAttenuationRadius(2000.0f);
    PulseLight->SetCastShadows(false);

    // Criar componente de áudio
    PulseSound = CreateDefaultSubobject<UAudioComponent>(TEXT("PulseSound"));
    PulseSound->SetupAttachment(RootComponent);
    PulseSound->bAutoActivate = false;

    // Criar componente de colisão esférica
    PulseSphere = CreateDefaultSubobject<USphereComponent>(TEXT("PulseSphere"));
    PulseSphere->SetupAttachment(RootComponent);
    PulseSphere->SetSphereRadius(100.0f); // Raio inicial pequeno
    PulseSphere->SetCollisionProfileName(TEXT("OverlapAllDynamic"));
    PulseSphere->OnComponentBeginOverlap.AddDynamic(this, &AAURACRONPCGEnergyPulse::OnPlayerEnterPulseRadius);
    
    // Valores padrão
    PulseRadius = 10000.0f;
    PulseDuration = 5.0f;
    PulseIntensity = 1.0f;
    PulseColor = FLinearColor(0.0f, 0.8f, 1.0f, 1.0f); // Azul ciano
    ExpansionSpeed = 1.0f;
    QualityScale = 1.0f;
    CurrentMapPhase = EAURACRONMapPhase::Awakening;
    ElapsedTime = 0.0f;
    bPulseActive = false;
    CurrentRadius = 0.0f;
    BaseDamage = 10.0f;
    EnergyType = EAURACRONEnergyType::Golden; // Tipo de energia padrão
}

// Called when the game starts or when spawned
void AAURACRONPCGEnergyPulse::BeginPlay()
{
    Super::BeginPlay();
    
    // Desativar efeitos inicialmente
    PulseEffect->Deactivate();
    PulseLight->SetVisibility(false);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnergyPulse: Inicializado"));
}

// Called every frame
void AAURACRONPCGEnergyPulse::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Processar pulso ativo
    if (bPulseActive)
    {
        // Atualizar tempo decorrido
        ElapsedTime += DeltaTime * PulseSpeed;
        
        // Verificar se o pulso terminou
        if (ElapsedTime >= PulseDuration)
        {
            // Desativar pulso
            bPulseActive = false;
            PulseEffect->Deactivate();
            PulseLight->SetVisibility(false);
            
            UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnergyPulse: Pulso concluído"));
            return;
        }
        
        // Atualizar efeitos visuais
        UpdateVisualEffects();
        
        // Aplicar efeitos aos jogadores e ambiente
        ApplyEffectsToPlayers();
        ApplyEffectsToEnvironment();
    }
}

void AAURACRONPCGEnergyPulse::TriggerPulse(float Duration, float Intensity)
{
    // Configurar parâmetros do pulso
    PulseDuration = Duration > 0.0f ? Duration : PulseDuration;
    PulseIntensity = Intensity;
    
    // Reiniciar tempo e ativar pulso
    ElapsedTime = 0.0f;
    bPulseActive = true;
    CurrentRadius = 0.0f;
    
    // Ativar efeitos visuais
    PulseEffect->Activate(true);
    PulseLight->SetVisibility(true);
    PulseSound->Play();
    
    // Atualizar efeitos visuais iniciais
    UpdateVisualEffects();
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnergyPulse: Pulso iniciado com intensidade %.2f e duração %.2f"), 
           Intensity, PulseDuration);
}

void AAURACRONPCGEnergyPulse::CreateGoldenEnergyPulse(float Duration, float Intensity)
{
    // Configurar tipo de energia
    EnergyType = EAURACRONEnergyType::Golden;
    
    // Configurar cor dourada para portais radiantes
    PulseColor = FLinearColor(1.0f, 0.84f, 0.0f, 1.0f); // Dourado
    
    // Configurar parâmetros específicos para energia dourada
    PulseSpeed = 1.2f;
    
    // Configurar luz para energia dourada
    if (PulseLight)
    {
        PulseLight->SetLightColor(PulseColor);
        PulseLight->SetIntensity(6000.0f);
    }
    
    // Disparar pulso
    TriggerPulse(Duration, Intensity);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnergyPulse: Pulso de energia dourada criado"));
}

void AAURACRONPCGEnergyPulse::CreateSilverEnergyPulse(float Duration, float Intensity)
{
    // Configurar tipo de energia
    EnergyType = EAURACRONEnergyType::Silver;
    
    // Configurar cor prateada para portais zephyr
    PulseColor = FLinearColor(0.75f, 0.75f, 0.8f, 1.0f); // Prateado
    
    // Configurar parâmetros específicos para energia prateada
    PulseSpeed = 1.5f;
    
    // Configurar luz para energia prateada
    if (PulseLight)
    {
        PulseLight->SetLightColor(PulseColor);
        PulseLight->SetIntensity(5000.0f);
    }
    
    // Disparar pulso
    TriggerPulse(Duration, Intensity);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnergyPulse: Pulso de energia prateada criado"));
}

void AAURACRONPCGEnergyPulse::CreateVioletEnergyPulse(float Duration, float Intensity)
{
    // Configurar tipo de energia
    EnergyType = EAURACRONEnergyType::Violet;
    
    // Configurar cor violeta para portais umbrais
    PulseColor = FLinearColor(0.5f, 0.0f, 1.0f, 1.0f); // Violeta
    
    // Configurar parâmetros específicos para energia violeta
    PulseSpeed = 1.8f;
    
    // Configurar luz para energia violeta
    if (PulseLight)
    {
        PulseLight->SetLightColor(PulseColor);
        PulseLight->SetIntensity(7000.0f);
    }
    
    // Disparar pulso
    TriggerPulse(Duration, Intensity);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnergyPulse: Pulso de energia violeta criado"));
}

void AAURACRONPCGEnergyPulse::CreateEnergyPulseForPortalType(EAURACRONPortalType PortalType, float Duration, float Intensity)
{
    // Criar pulso baseado no tipo de portal
    switch (PortalType)
    {
    case EAURACRONPortalType::RadiantPlains:
        CreateGoldenEnergyPulse(Duration, Intensity);
        break;
        
    case EAURACRONPortalType::ZephyrFirmament:
        CreateSilverEnergyPulse(Duration, Intensity);
        break;
        
    case EAURACRONPortalType::PurgatoryRealm:
        CreateVioletEnergyPulse(Duration, Intensity);
        break;
        
    default:
        // Usar pulso padrão
        TriggerPulse(Duration, Intensity);
        break;
    }
}

void AAURACRONPCGEnergyPulse::SetQualityScale(float NewQualityScale)
{
    QualityScale = FMath::Clamp(NewQualityScale, 0.1f, 1.0f);
    
    // Ajustar qualidade dos efeitos visuais
    if (PulseEffect)
    {
        // Ajustar densidade de partículas baseado na escala de qualidade
        PulseEffect->SetFloatParameter(FName("ParticleDensity"), QualityScale);
        
        // Ajustar qualidade de iluminação
        PulseLight->SetIntensity(5000.0f * QualityScale);
    }
    
    UE_LOG(LogTemp, Verbose, TEXT("AAURACRONPCGEnergyPulse: Escala de qualidade ajustada para %.2f"), QualityScale);
}

void AAURACRONPCGEnergyPulse::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    CurrentMapPhase = MapPhase;
    
    // Ajustar parâmetros baseados na fase do mapa
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            PulseColor = FLinearColor(0.0f, 0.8f, 1.0f, 1.0f); // Azul ciano
            PulseDuration = 5.0f;
            PulseIntensity = 0.5f;
            break;
            
        case EAURACRONMapPhase::Expansion:
            PulseColor = FLinearColor(0.0f, 1.0f, 0.5f, 1.0f); // Verde esmeralda
            PulseDuration = 4.5f;
            PulseIntensity = 0.75f;
            break;
            
        case EAURACRONMapPhase::Convergence:
            PulseColor = FLinearColor(1.0f, 0.5f, 0.0f, 1.0f); // Laranja
            PulseDuration = 4.0f;
            PulseIntensity = 1.0f;
            break;
            
        case EAURACRONMapPhase::Resolution:
            PulseColor = FLinearColor(1.0f, 0.0f, 0.5f, 1.0f); // Magenta
            PulseDuration = 3.5f;
            PulseIntensity = 1.5f;
            break;
            
        default:
            break;
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnergyPulse: Atualizado para fase %d"), (int32)CurrentMapPhase);
}

void AAURACRONPCGEnergyPulse::UpdateVisualEffects()
{
    // Calcular raio atual do pulso
    CurrentRadius = CalculateCurrentRadius();
    
    // Calcular progresso normalizado (0.0 a 1.0)
    float NormalizedProgress = ElapsedTime / PulseDuration;
    
    // Calcular alpha para fade in/out
    float Alpha = 1.0f;
    if (NormalizedProgress < 0.2f) // Fade in
    {
        Alpha = NormalizedProgress / 0.2f;
    }
    else if (NormalizedProgress > 0.8f) // Fade out
    {
        Alpha = (1.0f - NormalizedProgress) / 0.2f;
    }
    
    // Atualizar parâmetros do sistema de partículas
    if (PulseEffect)
    {
        PulseEffect->SetFloatParameter(FName("Radius"), CurrentRadius);
        PulseEffect->SetFloatParameter(FName("Intensity"), PulseIntensity * Alpha);
        PulseEffect->SetColorParameter(FName("Color"), PulseColor);
    }
    
    // Atualizar luz
    if (PulseLight)
    {
        PulseLight->SetLightColor(PulseColor);
        PulseLight->SetIntensity(5000.0f * PulseIntensity * Alpha * QualityScale);
        PulseLight->SetAttenuationRadius(CurrentRadius * 0.5f);
    }
    
    // Atualizar esfera de colisão
    if (PulseSphere)
    {
        PulseSphere->SetSphereRadius(CurrentRadius, true);
    }
}

void AAURACRONPCGEnergyPulse::ApplyPulseEffects()
{
    // Verificar posições dos jogadores
    CheckPlayerPositions();
    
    // Aplicar efeitos ao ambiente dentro do raio
    // Implementação específica depende do design do jogo
    
    // Exemplo: Afetar materiais dinâmicos no ambiente
    if (CurrentMapPhase == EAURACRONMapPhase::Resolution)
    {
        // Obter todos os atores de ambiente afetáveis
        TArray<AActor*> EnvironmentActors;
        UGameplayStatics::GetAllActorsWithTag(GetWorld(), FName("AffectedByEnergyPulse"), EnvironmentActors);
        
        for (AActor* EnvActor : EnvironmentActors)
        {
            if (!EnvActor)
            {
                continue;
            }
            
            // Calcular distância do ator ao centro do pulso
            float Distance = FVector::Dist(EnvActor->GetActorLocation(), GetActorLocation());
            
            // Verificar se o ator está dentro do raio do pulso
            if (Distance <= CurrentRadius)
            {
                // Calcular intensidade baseada na distância
                float DistanceFactor = 1.0f - (Distance / CurrentRadius);
                float EffectIntensity = PulseIntensity * DistanceFactor;
                
                // Aplicar efeito ao ator de ambiente
                // Exemplo: Notificar o ator sobre o pulso
                EnvActor->ReceiveNotifyEvent(FName("OnEnergyPulse"), this, EffectIntensity);
            }
        }
    }
}

void AAURACRONPCGEnergyPulse::CheckPlayerPositions()
{
    // Obter todos os jogadores
    TArray<AActor*> Players;
    UGameplayStatics::GetAllActorsOfClass(GetWorld(), ACharacter::StaticClass(), Players);
    
    // Aplicar efeitos aos jogadores dentro do raio
    for (AActor* Player : Players)
    {
        if (!Player)
        {
            continue;
        }
        
        // Calcular distância do jogador ao centro do pulso
        float Distance = FVector::Dist(Player->GetActorLocation(), GetActorLocation());
        
        // Verificar se o jogador está dentro do raio do pulso
        if (Distance <= CurrentRadius)
        {
            // Calcular intensidade baseada na distância (mais forte no centro do pulso)
            float DistanceFactor = 1.0f - (Distance / CurrentRadius);
            float EffectIntensity = PulseIntensity * DistanceFactor;
            
            // Aplicar efeito ao jogador
            float DamageAmount = BaseDamage * EffectIntensity;
            ApplyDamageToPlayer(Player, DamageAmount);
            
            // Exemplo: Aplicar impulso na direção oposta ao centro do pulso
            if (CurrentMapPhase == EAURACRONMapPhase::Resolution && EffectIntensity > 0.5f)
            {
                FVector Direction = (Player->GetActorLocation() - GetActorLocation()).GetSafeNormal();
                float ImpulseStrength = 500.0f * EffectIntensity;
                
                // Aplicar impulso ao personagem
                ACharacter* Character = Cast<ACharacter>(Player);
                if (Character && Character->GetCharacterMovement())
                {
                    Character->GetCharacterMovement()->AddImpulse(Direction * ImpulseStrength);
                }
            }
        }
    }
}

float AAURACRONPCGEnergyPulse::CalculateCurrentRadius() const
{
    // Calcular progresso normalizado (0.0 a 1.0)
    float NormalizedProgress = ElapsedTime / PulseDuration;
    
    // Função de expansão não-linear (mais rápida no início, mais lenta no final)
    float ExpansionFactor = FMath::Sqrt(NormalizedProgress);
    
    // Calcular raio atual
    return PulseRadius * ExpansionFactor;
}

float AAURACRONPCGEnergyPulse::CalculateDamageMultiplier(float Distance)
{
    // Quanto mais próximo do centro, maior o dano
    float DistanceFactor = 1.0f - (Distance / CurrentRadius);
    
    // Aplicar curva não-linear para aumentar dano no centro
    return FMath::Pow(DistanceFactor, 2.0f);
}

void AAURACRONPCGEnergyPulse::ApplyDamageToPlayer(AActor* Player, float DamageAmount)
{
    // Aplicar dano ao jogador usando o sistema de dano do Unreal
    if (Player && DamageAmount > 0.0f)
    {
        // Usar o sistema de dano do Unreal
        UGameplayStatics::ApplyDamage(
            Player,                  // Ator alvo
            DamageAmount,            // Quantidade de dano
            nullptr,                 // Instigador (opcional)
            this,                    // Causador do dano
            nullptr                  // Tipo de dano (opcional)
        );
    }
}

void AAURACRONPCGEnergyPulse::OnPlayerEnterPulseRadius(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, 
                                UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, 
                                bool bFromSweep, const FHitResult& SweepResult)
{
    // Verificar se o ator que entrou é um jogador
    ACharacter* Character = Cast<ACharacter>(OtherActor);
    if (Character && bPulseActive)
    {
        // Calcular distância do jogador ao centro do pulso
        float Distance = FVector::Dist(Character->GetActorLocation(), GetActorLocation());
        
        // Calcular multiplicador de dano baseado na distância
        float DamageMultiplier = CalculateDamageMultiplier(Distance);
        
        // Aplicar dano ao jogador
        float DamageAmount = BaseDamage * DamageMultiplier * PulseIntensity;
        ApplyDamageToPlayer(Character, DamageAmount);
        
        // Efeito visual de feedback
        if (PulseEffect)
        {
            // Criar efeito de impacto no jogador
            UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                PulseEffect->GetAsset(),
                Character->GetActorLocation(),
                FRotator::ZeroRotator,
                FVector(0.5f),
                true,
                true,
                ENCPoolMethod::AutoRelease
            );
        }
    }
}