// Copyright Aura Cronos Studios, Inc. All Rights Reserved.

#include "PCG/AURACRONPCGChaosIslandManager.h"
#include "PCG/AURACRONPCGChaosIsland.h"
#include "PCG/AURACRONPCGChaosPortal.h"
#include "PCG/AURACRONPCGPrismalFlow.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Components/StaticMeshComponent.h"
#include "UObject/ConstructorHelpers.h"

AAURACRONPCGChaosIslandManager::AAURACRONPCGChaosIslandManager()
{
    PrimaryActorTick.bCanEverTick = true;
    
    // Configurações padrão conforme GDD
    MaxChaosIslands = 4; // Número balanceado para o mapa
    MinDistanceBetweenIslands = 2000.0f; // Distância mínima em unidades UE
    
    // Definir classes padrão
    ChaosIslandClass = AChaosIsland::StaticClass();
    ChaosPortalClass = AAURACRONPCGChaosPortal::StaticClass();
    
    PrismalFlow = nullptr;
}

void AAURACRONPCGChaosIslandManager::BeginPlay()
{
    Super::BeginPlay();
    
    // Se não foi inicializado manualmente, tentar encontrar o PrismalFlow na cena
    if (!PrismalFlow)
    {
        PrismalFlow = Cast<AAURACRONPCGPrismalFlow>(UGameplayStatics::GetActorOfClass(GetWorld(), AAURACRONPCGPrismalFlow::StaticClass()));
        if (PrismalFlow)
        {
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: PrismalFlow encontrado automaticamente"));
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager: PrismalFlow não encontrado na cena"));
        }
    }
}

void AAURACRONPCGChaosIslandManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Atualizar comportamento das ilhas caos se necessário
    for (AChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            // Lógica de atualização das ilhas pode ser implementada aqui
        }
    }
}

void AAURACRONPCGChaosIslandManager::Initialize(AAURACRONPCGPrismalFlow* InPrismalFlow)
{
    if (!InPrismalFlow)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager::Initialize - PrismalFlow inválido"));
        return;
    }
    
    PrismalFlow = InPrismalFlow;
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Inicializado com PrismalFlow"));
}

void AAURACRONPCGChaosIslandManager::GenerateChaosIslands()
{
    if (!PrismalFlow)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGChaosIslandManager::GenerateChaosIslands - PrismalFlow não definido"));
        return;
    }
    
    // Limpar ilhas existentes
    for (AChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            Island->Destroy();
        }
    }
    ChaosIslands.Empty();
    
    // Limpar portais existentes
    for (AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            Portal->Destroy();
        }
    }
    ChaosPortals.Empty();
    
    // Encontrar pontos de interseção do fluxo
    TArray<FVector> IntersectionPoints = FindAllFlowIntersections();
    
    if (IntersectionPoints.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager::GenerateChaosIslands - Nenhum ponto de interseção encontrado"));
        return;
    }
    
    // Limitar ao número máximo de ilhas
    int32 IslandsToCreate = FMath::Min(IntersectionPoints.Num(), MaxChaosIslands);
    
    // Gerar ilhas nos pontos de interseção
    for (int32 i = 0; i < IslandsToCreate; i++)
    {
        FVector SpawnLocation = IntersectionPoints[i];
        
        // Verificar se a posição está válida e não muito próxima de outras ilhas
        bool bValidPosition = true;
        for (const AChaosIsland* ExistingIsland : ChaosIslands)
        {
            if (ExistingIsland && FVector::Dist(SpawnLocation, ExistingIsland->GetActorLocation()) < MinDistanceBetweenIslands)
            {
                bValidPosition = false;
                break;
            }
        }
        
        if (!bValidPosition)
        {
            continue;
        }
        
        // Spawnar ilha caos
        FActorSpawnParameters SpawnParams;
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
        
        AChaosIsland* NewIsland = GetWorld()->SpawnActor<AChaosIsland>(ChaosIslandClass, SpawnLocation, FRotator::ZeroRotator, SpawnParams);
        if (NewIsland)
        {
            ChaosIslands.Add(NewIsland);
            
            // Configurar a ilha conforme especificações do GDD
            NewIsland->SetIslandType(EAURACRONIslandType::Chaos);
            
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Ilha Caos criada em %s"), *SpawnLocation.ToString());
            
            // Criar portal caos associado
            AAURACRONPCGChaosPortal* NewPortal = GetWorld()->SpawnActor<AAURACRONPCGChaosPortal>(ChaosPortalClass, SpawnLocation + FVector(0, 0, 200), FRotator::ZeroRotator, SpawnParams);
            if (NewPortal)
            {
                ChaosPortals.Add(NewPortal);
                UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager: Portal Caos criado para ilha em %s"), *SpawnLocation.ToString());
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager::GenerateChaosIslands - Geradas %d ilhas caos"), ChaosIslands.Num());
}

void AAURACRONPCGChaosIslandManager::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    // Atualizar comportamento das ilhas baseado na fase do mapa
    for (AChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            // Implementar lógica específica por fase
            switch (MapPhase)
            {
                case EAURACRONMapPhase::Awakening:
                    // Fase inicial - ilhas menos ativas
                    Island->SetActivityLevel(0.3f);
                    break;
                    
                case EAURACRONMapPhase::Convergence:
                    // Fase de convergência - atividade moderada
                    Island->SetActivityLevel(0.6f);
                    break;
                    
                case EAURACRONMapPhase::Intensification:
                    // Fase de intensificação - alta atividade
                    Island->SetActivityLevel(0.9f);
                    break;
                    
                case EAURACRONMapPhase::PrismalFlow:
                    // Fase do fluxo prismal - máxima atividade
                    Island->SetActivityLevel(1.0f);
                    break;
                    
                case EAURACRONMapPhase::Resolution:
                    // Fase de resolução - atividade reduzida
                    Island->SetActivityLevel(0.4f);
                    break;
            }
        }
    }
    
    // Atualizar portais também
    for (AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            Portal->UpdateForMapPhase(MapPhase);
        }
    }
}

bool AAURACRONPCGChaosIslandManager::IsPointAtFlowIntersection(const FVector& Point, float Tolerance) const
{
    if (!PrismalFlow)
    {
        return false;
    }
    
    // Verificar se o ponto está próximo de uma interseção do fluxo
    TArray<FVector> IntersectionPoints = FindAllFlowIntersections();
    
    for (const FVector& IntersectionPoint : IntersectionPoints)
    {
        if (FVector::Dist(Point, IntersectionPoint) <= Tolerance)
        {
            return true;
        }
    }
    
    return false;
}

TArray<FVector> AAURACRONPCGChaosIslandManager::FindAllFlowIntersections() const
{
    TArray<FVector> IntersectionPoints;
    
    if (!PrismalFlow)
    {
        return IntersectionPoints;
    }
    
    // Obter pontos de controle do fluxo prismal
    TArray<FVector> FlowControlPoints = PrismalFlow->GetFlowControlPoints();
    
    if (FlowControlPoints.Num() < 3)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGChaosIslandManager::FindAllFlowIntersections - Pontos de controle insuficientes"));
        return IntersectionPoints;
    }
    
    // Algoritmo para encontrar interseções entre segmentos do fluxo
    for (int32 i = 0; i < FlowControlPoints.Num() - 1; i++)
    {
        for (int32 j = i + 2; j < FlowControlPoints.Num() - 1; j++)
        {
            // Evitar segmentos adjacentes
            if (FMath::Abs(i - j) <= 1)
            {
                continue;
            }
            
            FVector P1 = FlowControlPoints[i];
            FVector P2 = FlowControlPoints[i + 1];
            FVector P3 = FlowControlPoints[j];
            FVector P4 = FlowControlPoints[j + 1];
            
            // Calcular interseção entre os segmentos P1-P2 e P3-P4
            FVector IntersectionPoint;
            if (CalculateLineIntersection(P1, P2, P3, P4, IntersectionPoint))
            {
                IntersectionPoints.Add(IntersectionPoint);
            }
        }
    }
    
    // Adicionar pontos de convergência principais (centro do mapa, etc.)
    if (FlowControlPoints.Num() > 0)
    {
        FVector CenterPoint = FVector::ZeroVector;
        for (const FVector& Point : FlowControlPoints)
        {
            CenterPoint += Point;
        }
        CenterPoint /= FlowControlPoints.Num();
        IntersectionPoints.Add(CenterPoint);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager::FindAllFlowIntersections - Encontrados %d pontos de interseção"), IntersectionPoints.Num());
    
    return IntersectionPoints;
}

void AAURACRONPCGChaosIslandManager::SetAllChaosIslandsActive(bool bActive)
{
    for (AChaosIsland* Island : ChaosIslands)
    {
        if (Island && IsValid(Island))
        {
            Island->SetActorHiddenInGame(!bActive);
            Island->SetActorEnableCollision(bActive);
            Island->SetActorTickEnabled(bActive);
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager::SetAllChaosIslandsActive - Ilhas %s"), bActive ? TEXT("ativadas") : TEXT("desativadas"));
}

void AAURACRONPCGChaosIslandManager::SetAllChaosPortalsActive(bool bActive, float Duration, float Intensity)
{
    for (AAURACRONPCGChaosPortal* Portal : ChaosPortals)
    {
        if (Portal && IsValid(Portal))
        {
            if (bActive)
            {
                Portal->ActivatePortal(Duration, Intensity);
            }
            else
            {
                Portal->DeactivatePortal();
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGChaosIslandManager::SetAllChaosPortalsActive - Portais %s"), bActive ? TEXT("ativados") : TEXT("desativados"));
}

bool AAURACRONPCGChaosIslandManager::CalculateLineIntersection(const FVector& P1, const FVector& P2, const FVector& P3, const FVector& P4, FVector& OutIntersection) const
{
    // Calcular interseção entre duas linhas em 2D (ignorando Z)
    float X1 = P1.X, Y1 = P1.Y;
    float X2 = P2.X, Y2 = P2.Y;
    float X3 = P3.X, Y3 = P3.Y;
    float X4 = P4.X, Y4 = P4.Y;
    
    float Denominator = (X1 - X2) * (Y3 - Y4) - (Y1 - Y2) * (X3 - X4);
    
    if (FMath::IsNearlyZero(Denominator))
    {
        // Linhas paralelas
        return false;
    }
    
    float T = ((X1 - X3) * (Y3 - Y4) - (Y1 - Y3) * (X3 - X4)) / Denominator;
    float U = -((X1 - X2) * (Y1 - Y3) - (Y1 - Y2) * (X1 - X3)) / Denominator;
    
    // Verificar se a interseção está dentro dos segmentos
    if (T >= 0.0f && T <= 1.0f && U >= 0.0f && U <= 1.0f)
    {
        OutIntersection.X = X1 + T * (X2 - X1);
        OutIntersection.Y = Y1 + T * (Y2 - Y1);
        OutIntersection.Z = (P1.Z + P2.Z + P3.Z + P4.Z) / 4.0f; // Média das alturas
        
        return true;
    }
    
    return false;
}