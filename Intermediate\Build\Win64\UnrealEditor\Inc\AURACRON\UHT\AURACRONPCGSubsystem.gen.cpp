// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGSubsystem.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGSubsystem() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_UAURACRONPCGSubsystem();
AURACRON_API UClass* Z_Construct_UClass_UAURACRONPCGSubsystem_NoRegister();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONIslandType();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase();
AURACRON_API UEnum* Z_Construct_UEnum_AURACRON_EAURACRONTrailType();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
PCG_API UClass* Z_Construct_UClass_APCGVolume_NoRegister();
PCG_API UClass* Z_Construct_UClass_UPCGComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin Enum EAURACRONEnvironmentType **************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONEnvironmentType;
static UEnum* EAURACRONEnvironmentType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONEnvironmentType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONEnvironmentType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONEnvironmentType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONEnvironmentType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONEnvironmentType>()
{
	return EAURACRONEnvironmentType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAURACRONEnvironmentType::None" },
		{ "PurgatoryRealm.DisplayName", "Purgatory Realm" },
		{ "PurgatoryRealm.Name", "EAURACRONEnvironmentType::PurgatoryRealm" },
		{ "RadiantPlains.DisplayName", "Radiant Plains" },
		{ "RadiantPlains.Name", "EAURACRONEnvironmentType::RadiantPlains" },
		{ "ZephyrFirmament.DisplayName", "Zephyr Firmament" },
		{ "ZephyrFirmament.Name", "EAURACRONEnvironmentType::ZephyrFirmament" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONEnvironmentType::None", (int64)EAURACRONEnvironmentType::None },
		{ "EAURACRONEnvironmentType::RadiantPlains", (int64)EAURACRONEnvironmentType::RadiantPlains },
		{ "EAURACRONEnvironmentType::ZephyrFirmament", (int64)EAURACRONEnvironmentType::ZephyrFirmament },
		{ "EAURACRONEnvironmentType::PurgatoryRealm", (int64)EAURACRONEnvironmentType::PurgatoryRealm },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONEnvironmentType",
	"EAURACRONEnvironmentType",
	Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONEnvironmentType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONEnvironmentType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONEnvironmentType.InnerSingleton;
}
// ********** End Enum EAURACRONEnvironmentType ****************************************************

// ********** Begin Enum EAURACRONTrailType ********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONTrailType;
static UEnum* EAURACRONTrailType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONTrailType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONTrailType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONTrailType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONTrailType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONTrailType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONTrailType>()
{
	return EAURACRONTrailType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Axis.DisplayName", "Axis Trail" },
		{ "Axis.Name", "EAURACRONTrailType::Axis" },
		{ "BlueprintType", "true" },
		{ "Lunar.DisplayName", "Lunar Trail" },
		{ "Lunar.Name", "EAURACRONTrailType::Lunar" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAURACRONTrailType::None" },
		{ "Solar.DisplayName", "Solar Trail" },
		{ "Solar.Name", "EAURACRONTrailType::Solar" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONTrailType::None", (int64)EAURACRONTrailType::None },
		{ "EAURACRONTrailType::Solar", (int64)EAURACRONTrailType::Solar },
		{ "EAURACRONTrailType::Axis", (int64)EAURACRONTrailType::Axis },
		{ "EAURACRONTrailType::Lunar", (int64)EAURACRONTrailType::Lunar },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONTrailType",
	"EAURACRONTrailType",
	Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONTrailType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONTrailType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONTrailType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONTrailType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONTrailType.InnerSingleton;
}
// ********** End Enum EAURACRONTrailType **********************************************************

// ********** Begin Enum EAURACRONIslandType *******************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONIslandType;
static UEnum* EAURACRONIslandType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONIslandType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONIslandType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONIslandType, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONIslandType"));
	}
	return Z_Registration_Info_UEnum_EAURACRONIslandType.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONIslandType>()
{
	return EAURACRONIslandType_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONIslandType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Arsenal.DisplayName", "Arsenal Island" },
		{ "Arsenal.Name", "EAURACRONIslandType::Arsenal" },
		{ "BlueprintType", "true" },
		{ "Chaos.DisplayName", "Chaos Island" },
		{ "Chaos.Name", "EAURACRONIslandType::Chaos" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
		{ "Nexus.DisplayName", "Nexus Island" },
		{ "Nexus.Name", "EAURACRONIslandType::Nexus" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAURACRONIslandType::None" },
		{ "Sanctuary.DisplayName", "Sanctuary Island" },
		{ "Sanctuary.Name", "EAURACRONIslandType::Sanctuary" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONIslandType::None", (int64)EAURACRONIslandType::None },
		{ "EAURACRONIslandType::Nexus", (int64)EAURACRONIslandType::Nexus },
		{ "EAURACRONIslandType::Sanctuary", (int64)EAURACRONIslandType::Sanctuary },
		{ "EAURACRONIslandType::Arsenal", (int64)EAURACRONIslandType::Arsenal },
		{ "EAURACRONIslandType::Chaos", (int64)EAURACRONIslandType::Chaos },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONIslandType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONIslandType",
	"EAURACRONIslandType",
	Z_Construct_UEnum_AURACRON_EAURACRONIslandType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONIslandType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONIslandType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONIslandType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONIslandType()
{
	if (!Z_Registration_Info_UEnum_EAURACRONIslandType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONIslandType.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONIslandType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONIslandType.InnerSingleton;
}
// ********** End Enum EAURACRONIslandType *********************************************************

// ********** Begin Enum EAURACRONMapPhase *********************************************************
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAURACRONMapPhase;
static UEnum* EAURACRONMapPhase_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAURACRONMapPhase.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAURACRONMapPhase.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("EAURACRONMapPhase"));
	}
	return Z_Registration_Info_UEnum_EAURACRONMapPhase.OuterSingleton;
}
template<> AURACRON_API UEnum* StaticEnum<EAURACRONMapPhase>()
{
	return EAURACRONMapPhase_StaticEnum();
}
struct Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Awakening.DisplayName", "Awakening Phase" },
		{ "Awakening.Name", "EAURACRONMapPhase::Awakening" },
		{ "BlueprintType", "true" },
		{ "Convergence.DisplayName", "Convergence Phase" },
		{ "Convergence.Name", "EAURACRONMapPhase::Convergence" },
		{ "Intensification.DisplayName", "Intensification Phase" },
		{ "Intensification.Name", "EAURACRONMapPhase::Intensification" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
		{ "Resolution.DisplayName", "Resolution Phase" },
		{ "Resolution.Name", "EAURACRONMapPhase::Resolution" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAURACRONMapPhase::Awakening", (int64)EAURACRONMapPhase::Awakening },
		{ "EAURACRONMapPhase::Convergence", (int64)EAURACRONMapPhase::Convergence },
		{ "EAURACRONMapPhase::Intensification", (int64)EAURACRONMapPhase::Intensification },
		{ "EAURACRONMapPhase::Resolution", (int64)EAURACRONMapPhase::Resolution },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	"EAURACRONMapPhase",
	"EAURACRONMapPhase",
	Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::Enum_MetaDataParams), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_AURACRON_EAURACRONMapPhase()
{
	if (!Z_Registration_Info_UEnum_EAURACRONMapPhase.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAURACRONMapPhase.InnerSingleton, Z_Construct_UEnum_AURACRON_EAURACRONMapPhase_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAURACRONMapPhase.InnerSingleton;
}
// ********** End Enum EAURACRONMapPhase ***********************************************************

// ********** Begin Class UAURACRONPCGSubsystem Function AdvanceToNextPhase ************************
struct Z_Construct_UFunction_UAURACRONPCGSubsystem_AdvanceToNextPhase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es para gerenciar as fases do mapa\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es para gerenciar as fases do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGSubsystem_AdvanceToNextPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGSubsystem, nullptr, "AdvanceToNextPhase", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_AdvanceToNextPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGSubsystem_AdvanceToNextPhase_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_UAURACRONPCGSubsystem_AdvanceToNextPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGSubsystem_AdvanceToNextPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGSubsystem::execAdvanceToNextPhase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AdvanceToNextPhase();
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGSubsystem Function AdvanceToNextPhase **************************

// ********** Begin Class UAURACRONPCGSubsystem Function GenerateEnvironment ***********************
struct Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics
{
	struct AURACRONPCGSubsystem_eventGenerateEnvironment_Parms
	{
		EAURACRONEnvironmentType EnvironmentType;
		FVector Center;
		float Radius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es para gerenciar os ambientes\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es para gerenciar os ambientes" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::NewProp_EnvironmentType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::NewProp_EnvironmentType = { "EnvironmentType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventGenerateEnvironment_Parms, EnvironmentType), Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2415364844
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventGenerateEnvironment_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventGenerateEnvironment_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::NewProp_EnvironmentType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::NewProp_EnvironmentType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::NewProp_Radius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGSubsystem, nullptr, "GenerateEnvironment", Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::AURACRONPCGSubsystem_eventGenerateEnvironment_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::AURACRONPCGSubsystem_eventGenerateEnvironment_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGSubsystem::execGenerateEnvironment)
{
	P_GET_ENUM(EAURACRONEnvironmentType,Z_Param_EnvironmentType);
	P_GET_STRUCT(FVector,Z_Param_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateEnvironment(EAURACRONEnvironmentType(Z_Param_EnvironmentType),Z_Param_Center,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGSubsystem Function GenerateEnvironment *************************

// ********** Begin Class UAURACRONPCGSubsystem Function GenerateIsland ****************************
struct Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics
{
	struct AURACRONPCGSubsystem_eventGenerateIsland_Parms
	{
		EAURACRONIslandType IslandType;
		FVector Location;
		float Radius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es para gerenciar as ilhas\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es para gerenciar as ilhas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_IslandType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_IslandType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::NewProp_IslandType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::NewProp_IslandType = { "IslandType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventGenerateIsland_Parms, IslandType), Z_Construct_UEnum_AURACRON_EAURACRONIslandType, METADATA_PARAMS(0, nullptr) }; // 3671229991
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventGenerateIsland_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventGenerateIsland_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::NewProp_IslandType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::NewProp_IslandType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::NewProp_Radius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGSubsystem, nullptr, "GenerateIsland", Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::AURACRONPCGSubsystem_eventGenerateIsland_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::AURACRONPCGSubsystem_eventGenerateIsland_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGSubsystem::execGenerateIsland)
{
	P_GET_ENUM(EAURACRONIslandType,Z_Param_IslandType);
	P_GET_STRUCT(FVector,Z_Param_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateIsland(EAURACRONIslandType(Z_Param_IslandType),Z_Param_Location,Z_Param_Radius);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGSubsystem Function GenerateIsland ******************************

// ********** Begin Class UAURACRONPCGSubsystem Function GenerateMap *******************************
struct Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics
{
	struct AURACRONPCGSubsystem_eventGenerateMap_Parms
	{
		FVector MapCenter;
		float MapRadius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es para iniciar a gera\xc3\xa7\xc3\xa3o do mapa\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es para iniciar a gera\xc3\xa7\xc3\xa3o do mapa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MapCenter;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MapRadius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics::NewProp_MapCenter = { "MapCenter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventGenerateMap_Parms, MapCenter), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics::NewProp_MapRadius = { "MapRadius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventGenerateMap_Parms, MapRadius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics::NewProp_MapCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics::NewProp_MapRadius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGSubsystem, nullptr, "GenerateMap", Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics::AURACRONPCGSubsystem_eventGenerateMap_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics::AURACRONPCGSubsystem_eventGenerateMap_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGSubsystem::execGenerateMap)
{
	P_GET_STRUCT(FVector,Z_Param_MapCenter);
	P_GET_PROPERTY(FFloatProperty,Z_Param_MapRadius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateMap(Z_Param_MapCenter,Z_Param_MapRadius);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGSubsystem Function GenerateMap *********************************

// ********** Begin Class UAURACRONPCGSubsystem Function GeneratePrismalFlow ***********************
struct Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics
{
	struct AURACRONPCGSubsystem_eventGeneratePrismalFlow_Parms
	{
		TArray<FVector> FlowControlPoints;
		float Width;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es para gerenciar o Prismal Flow\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es para gerenciar o Prismal Flow" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FlowControlPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FlowControlPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Width;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::NewProp_FlowControlPoints_Inner = { "FlowControlPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::NewProp_FlowControlPoints = { "FlowControlPoints", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventGeneratePrismalFlow_Parms, FlowControlPoints), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventGeneratePrismalFlow_Parms, Width), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::NewProp_FlowControlPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::NewProp_FlowControlPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::NewProp_Width,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGSubsystem, nullptr, "GeneratePrismalFlow", Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::AURACRONPCGSubsystem_eventGeneratePrismalFlow_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::AURACRONPCGSubsystem_eventGeneratePrismalFlow_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGSubsystem::execGeneratePrismalFlow)
{
	P_GET_TARRAY(FVector,Z_Param_FlowControlPoints);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Width);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GeneratePrismalFlow(Z_Param_FlowControlPoints,Z_Param_Width);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGSubsystem Function GeneratePrismalFlow *************************

// ********** Begin Class UAURACRONPCGSubsystem Function GenerateTrail *****************************
struct Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics
{
	struct AURACRONPCGSubsystem_eventGenerateTrail_Parms
	{
		EAURACRONTrailType TrailType;
		TArray<FVector> ControlPoints;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fun\xc3\xa7\xc3\xb5""es para gerenciar as trilhas\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fun\xc3\xa7\xc3\xb5""es para gerenciar as trilhas" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TrailType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TrailType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ControlPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ControlPoints;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::NewProp_TrailType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::NewProp_TrailType = { "TrailType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventGenerateTrail_Parms, TrailType), Z_Construct_UEnum_AURACRON_EAURACRONTrailType, METADATA_PARAMS(0, nullptr) }; // 321700334
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::NewProp_ControlPoints_Inner = { "ControlPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::NewProp_ControlPoints = { "ControlPoints", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventGenerateTrail_Parms, ControlPoints), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::NewProp_TrailType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::NewProp_TrailType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::NewProp_ControlPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::NewProp_ControlPoints,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGSubsystem, nullptr, "GenerateTrail", Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::AURACRONPCGSubsystem_eventGenerateTrail_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::AURACRONPCGSubsystem_eventGenerateTrail_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGSubsystem::execGenerateTrail)
{
	P_GET_ENUM(EAURACRONTrailType,Z_Param_TrailType);
	P_GET_TARRAY(FVector,Z_Param_ControlPoints);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GenerateTrail(EAURACRONTrailType(Z_Param_TrailType),Z_Param_ControlPoints);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGSubsystem Function GenerateTrail *******************************

// ********** Begin Class UAURACRONPCGSubsystem Function GetCurrentMapPhase ************************
struct Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics
{
	struct AURACRONPCGSubsystem_eventGetCurrentMapPhase_Parms
	{
		EAURACRONMapPhase ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventGetCurrentMapPhase_Parms, ReturnValue), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 3530596558
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGSubsystem, nullptr, "GetCurrentMapPhase", Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics::AURACRONPCGSubsystem_eventGetCurrentMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics::AURACRONPCGSubsystem_eventGetCurrentMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGSubsystem::execGetCurrentMapPhase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EAURACRONMapPhase*)Z_Param__Result=P_THIS->GetCurrentMapPhase();
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGSubsystem Function GetCurrentMapPhase **************************

// ********** Begin Class UAURACRONPCGSubsystem Function SetMapPhase *******************************
struct Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics
{
	struct AURACRONPCGSubsystem_eventSetMapPhase_Parms
	{
		EAURACRONMapPhase NewPhase;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPhase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics::NewProp_NewPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics::NewProp_NewPhase = { "NewPhase", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventSetMapPhase_Parms, NewPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(0, nullptr) }; // 3530596558
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics::NewProp_NewPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics::NewProp_NewPhase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGSubsystem, nullptr, "SetMapPhase", Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics::AURACRONPCGSubsystem_eventSetMapPhase_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics::AURACRONPCGSubsystem_eventSetMapPhase_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGSubsystem::execSetMapPhase)
{
	P_GET_ENUM(EAURACRONMapPhase,Z_Param_NewPhase);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetMapPhase(EAURACRONMapPhase(Z_Param_NewPhase));
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGSubsystem Function SetMapPhase *********************************

// ********** Begin Class UAURACRONPCGSubsystem Function UpdatePrismalFlow *************************
struct Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow_Statics
{
	struct AURACRONPCGSubsystem_eventUpdatePrismalFlow_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventUpdatePrismalFlow_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGSubsystem, nullptr, "UpdatePrismalFlow", Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow_Statics::AURACRONPCGSubsystem_eventUpdatePrismalFlow_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow_Statics::AURACRONPCGSubsystem_eventUpdatePrismalFlow_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGSubsystem::execUpdatePrismalFlow)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePrismalFlow(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGSubsystem Function UpdatePrismalFlow ***************************

// ********** Begin Class UAURACRONPCGSubsystem Function UpdateTrailPositions **********************
struct Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions_Statics
{
	struct AURACRONPCGSubsystem_eventUpdateTrailPositions_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|PCG" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGSubsystem_eventUpdateTrailPositions_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_UAURACRONPCGSubsystem, nullptr, "UpdateTrailPositions", Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions_Statics::AURACRONPCGSubsystem_eventUpdateTrailPositions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions_Statics::AURACRONPCGSubsystem_eventUpdateTrailPositions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAURACRONPCGSubsystem::execUpdateTrailPositions)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateTrailPositions(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// ********** End Class UAURACRONPCGSubsystem Function UpdateTrailPositions ************************

// ********** Begin Class UAURACRONPCGSubsystem ****************************************************
void UAURACRONPCGSubsystem::StaticRegisterNativesUAURACRONPCGSubsystem()
{
	UClass* Class = UAURACRONPCGSubsystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AdvanceToNextPhase", &UAURACRONPCGSubsystem::execAdvanceToNextPhase },
		{ "GenerateEnvironment", &UAURACRONPCGSubsystem::execGenerateEnvironment },
		{ "GenerateIsland", &UAURACRONPCGSubsystem::execGenerateIsland },
		{ "GenerateMap", &UAURACRONPCGSubsystem::execGenerateMap },
		{ "GeneratePrismalFlow", &UAURACRONPCGSubsystem::execGeneratePrismalFlow },
		{ "GenerateTrail", &UAURACRONPCGSubsystem::execGenerateTrail },
		{ "GetCurrentMapPhase", &UAURACRONPCGSubsystem::execGetCurrentMapPhase },
		{ "SetMapPhase", &UAURACRONPCGSubsystem::execSetMapPhase },
		{ "UpdatePrismalFlow", &UAURACRONPCGSubsystem::execUpdatePrismalFlow },
		{ "UpdateTrailPositions", &UAURACRONPCGSubsystem::execUpdateTrailPositions },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_UAURACRONPCGSubsystem;
UClass* UAURACRONPCGSubsystem::GetPrivateStaticClass()
{
	using TClass = UAURACRONPCGSubsystem;
	if (!Z_Registration_Info_UClass_UAURACRONPCGSubsystem.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGSubsystem"),
			Z_Registration_Info_UClass_UAURACRONPCGSubsystem.InnerSingleton,
			StaticRegisterNativesUAURACRONPCGSubsystem,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UAURACRONPCGSubsystem.InnerSingleton;
}
UClass* Z_Construct_UClass_UAURACRONPCGSubsystem_NoRegister()
{
	return UAURACRONPCGSubsystem::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UAURACRONPCGSubsystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Subsistema para gerenciar a gera\xc3\xa7\xc3\xa3o procedural do mapa AURACRON\n * Respons\xc3\xa1vel por coordenar os diferentes ambientes, trilhas e o Prismal Flow\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGSubsystem.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Subsistema para gerenciar a gera\xc3\xa7\xc3\xa3o procedural do mapa AURACRON\nRespons\xc3\xa1vel por coordenar os diferentes ambientes, trilhas e o Prismal Flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentComponents_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes PCG para cada ambiente\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes PCG para cada ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrailComponents_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componentes PCG para as trilhas\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componentes PCG para as trilhas" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrismalFlowComponent_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Componente PCG para o Prismal Flow\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Componente PCG para o Prismal Flow" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnvironmentVolumes_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Volumes PCG para cada ambiente\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Volumes PCG para cada ambiente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentMapPhase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Fase atual do mapa\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fase atual do mapa" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElapsedTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tempo decorrido desde o in\xc3\xad""cio da partida\n" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGSubsystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tempo decorrido desde o in\xc3\xad""cio da partida" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnvironmentComponents_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentComponents_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentComponents_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EnvironmentComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TrailComponents_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TrailComponents_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TrailComponents_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TrailComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PrismalFlowComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnvironmentVolumes_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_EnvironmentVolumes_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_EnvironmentVolumes_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EnvironmentVolumes;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentMapPhase_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentMapPhase;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ElapsedTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAURACRONPCGSubsystem_AdvanceToNextPhase, "AdvanceToNextPhase" }, // 228602820
		{ &Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateEnvironment, "GenerateEnvironment" }, // 3883077637
		{ &Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateIsland, "GenerateIsland" }, // 2737116059
		{ &Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateMap, "GenerateMap" }, // 3609451361
		{ &Z_Construct_UFunction_UAURACRONPCGSubsystem_GeneratePrismalFlow, "GeneratePrismalFlow" }, // 2568990220
		{ &Z_Construct_UFunction_UAURACRONPCGSubsystem_GenerateTrail, "GenerateTrail" }, // 3459507082
		{ &Z_Construct_UFunction_UAURACRONPCGSubsystem_GetCurrentMapPhase, "GetCurrentMapPhase" }, // 1507888807
		{ &Z_Construct_UFunction_UAURACRONPCGSubsystem_SetMapPhase, "SetMapPhase" }, // 3906394845
		{ &Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdatePrismalFlow, "UpdatePrismalFlow" }, // 2940085772
		{ &Z_Construct_UFunction_UAURACRONPCGSubsystem_UpdateTrailPositions, "UpdateTrailPositions" }, // 593110658
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAURACRONPCGSubsystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentComponents_ValueProp = { "EnvironmentComponents", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentComponents_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentComponents_Key_KeyProp = { "EnvironmentComponents_Key", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2415364844
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentComponents = { "EnvironmentComponents", nullptr, (EPropertyFlags)0x0040008000000008, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONPCGSubsystem, EnvironmentComponents), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentComponents_MetaData), NewProp_EnvironmentComponents_MetaData) }; // 2415364844
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_TrailComponents_ValueProp = { "TrailComponents", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_TrailComponents_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_TrailComponents_Key_KeyProp = { "TrailComponents_Key", nullptr, (EPropertyFlags)0x0000000000080008, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONTrailType, METADATA_PARAMS(0, nullptr) }; // 321700334
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_TrailComponents = { "TrailComponents", nullptr, (EPropertyFlags)0x0040008000000008, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONPCGSubsystem, TrailComponents), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrailComponents_MetaData), NewProp_TrailComponents_MetaData) }; // 321700334
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_PrismalFlowComponent = { "PrismalFlowComponent", nullptr, (EPropertyFlags)0x0040000000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONPCGSubsystem, PrismalFlowComponent), Z_Construct_UClass_UPCGComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrismalFlowComponent_MetaData), NewProp_PrismalFlowComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentVolumes_ValueProp = { "EnvironmentVolumes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_APCGVolume_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentVolumes_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentVolumes_Key_KeyProp = { "EnvironmentVolumes_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_AURACRON_EAURACRONEnvironmentType, METADATA_PARAMS(0, nullptr) }; // 2415364844
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentVolumes = { "EnvironmentVolumes", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONPCGSubsystem, EnvironmentVolumes), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnvironmentVolumes_MetaData), NewProp_EnvironmentVolumes_MetaData) }; // 2415364844
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_CurrentMapPhase_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_CurrentMapPhase = { "CurrentMapPhase", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONPCGSubsystem, CurrentMapPhase), Z_Construct_UEnum_AURACRON_EAURACRONMapPhase, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentMapPhase_MetaData), NewProp_CurrentMapPhase_MetaData) }; // 3530596558
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_ElapsedTime = { "ElapsedTime", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAURACRONPCGSubsystem, ElapsedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElapsedTime_MetaData), NewProp_ElapsedTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentComponents_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentComponents_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentComponents_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_TrailComponents_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_TrailComponents_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_TrailComponents_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_TrailComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_PrismalFlowComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentVolumes_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentVolumes_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentVolumes_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_EnvironmentVolumes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_CurrentMapPhase_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_CurrentMapPhase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::NewProp_ElapsedTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::ClassParams = {
	&UAURACRONPCGSubsystem::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::Class_MetaDataParams), Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAURACRONPCGSubsystem()
{
	if (!Z_Registration_Info_UClass_UAURACRONPCGSubsystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAURACRONPCGSubsystem.OuterSingleton, Z_Construct_UClass_UAURACRONPCGSubsystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAURACRONPCGSubsystem.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAURACRONPCGSubsystem);
UAURACRONPCGSubsystem::~UAURACRONPCGSubsystem() {}
// ********** End Class UAURACRONPCGSubsystem ******************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h__Script_AURACRON_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAURACRONEnvironmentType_StaticEnum, TEXT("EAURACRONEnvironmentType"), &Z_Registration_Info_UEnum_EAURACRONEnvironmentType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2415364844U) },
		{ EAURACRONTrailType_StaticEnum, TEXT("EAURACRONTrailType"), &Z_Registration_Info_UEnum_EAURACRONTrailType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 321700334U) },
		{ EAURACRONIslandType_StaticEnum, TEXT("EAURACRONIslandType"), &Z_Registration_Info_UEnum_EAURACRONIslandType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3671229991U) },
		{ EAURACRONMapPhase_StaticEnum, TEXT("EAURACRONMapPhase"), &Z_Registration_Info_UEnum_EAURACRONMapPhase, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3530596558U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAURACRONPCGSubsystem, UAURACRONPCGSubsystem::StaticClass, TEXT("UAURACRONPCGSubsystem"), &Z_Registration_Info_UClass_UAURACRONPCGSubsystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAURACRONPCGSubsystem), 3612350256U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h__Script_AURACRON_1511492888(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h__Script_AURACRON_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h__Script_AURACRON_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGSubsystem_h__Script_AURACRON_Statics::EnumInfo));
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
