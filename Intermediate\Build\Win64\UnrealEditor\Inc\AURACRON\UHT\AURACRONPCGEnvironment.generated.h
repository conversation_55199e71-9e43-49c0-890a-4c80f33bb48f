// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PCG/AURACRONPCGEnvironment.h"

#ifdef AURACRON_AURACRONPCGEnvironment_generated_h
#error "AURACRONPCGEnvironment.generated.h already included, missing '#pragma once' in AURACRONPCGEnvironment.h"
#endif
#define AURACRON_AURACRONPCGEnvironment_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

enum class EAURACRONEnvironmentType : uint8;
enum class EAURACRONMapPhase : uint8;

// ********** Begin ScriptStruct FBreathingForestData **********************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_23_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBreathingForestData_Statics; \
	AURACRON_API static class UScriptStruct* StaticStruct();


struct FBreathingForestData;
// ********** End ScriptStruct FBreathingForestData ************************************************

// ********** Begin Class AAURACRONPCGEnvironment **************************************************
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_55_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetActivityScale); \
	DECLARE_FUNCTION(execSetEnvironmentVisibility); \
	DECLARE_FUNCTION(execUpdateForMapPhase); \
	DECLARE_FUNCTION(execGenerateEnvironment); \
	DECLARE_FUNCTION(execGetEnvironmentType); \
	DECLARE_FUNCTION(execSetEnvironmentType);


AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister();

#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_55_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAURACRONPCGEnvironment(); \
	friend struct Z_Construct_UClass_AAURACRONPCGEnvironment_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister(); \
public: \
	DECLARE_CLASS2(AAURACRONPCGEnvironment, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/AURACRON"), Z_Construct_UClass_AAURACRONPCGEnvironment_NoRegister) \
	DECLARE_SERIALIZER(AAURACRONPCGEnvironment)


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_55_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	AAURACRONPCGEnvironment(AAURACRONPCGEnvironment&&) = delete; \
	AAURACRONPCGEnvironment(const AAURACRONPCGEnvironment&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAURACRONPCGEnvironment); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAURACRONPCGEnvironment); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAURACRONPCGEnvironment) \
	NO_API virtual ~AAURACRONPCGEnvironment();


#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_52_PROLOG
#define FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_55_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_55_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_55_INCLASS_NO_PURE_DECLS \
	FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h_55_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class AAURACRONPCGEnvironment;

// ********** End Class AAURACRONPCGEnvironment ****************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGEnvironment_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
