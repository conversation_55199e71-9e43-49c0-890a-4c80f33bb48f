// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "PCG/AURACRONPCGWorldPartitionIntegration.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeAURACRONPCGWorldPartitionIntegration() {}

// ********** Begin Cross Module References ********************************************************
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration();
AURACRON_API UClass* Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_NoRegister();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry();
AURACRON_API UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FGuid();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorldPartition_NoRegister();
UPackage* Z_Construct_UPackage__Script_AURACRON();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAURACRONPCGStreamingConfig ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig;
class UScriptStruct* FAURACRONPCGStreamingConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONPCGStreamingConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Configura\xc3\xa7\xc3\xb5""es de streaming para elementos PCG\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es de streaming para elementos PCG" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingDistance_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia de carregamento */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia de carregamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnloadingDistance_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia de descarregamento */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia de descarregamento" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingPriority_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
		{ "ClampMax", "100" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prioridade de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prioridade de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseAsyncStreaming_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve usar streaming ass\xc3\xadncrono */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve usar streaming ass\xc3\xadncrono" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridSize_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tamanho do grid de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tamanho do grid de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingDistance_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
		{ "ClampMin", "1000.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dist\xc3\xa2ncia de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dist\xc3\xa2ncia de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bStartActive_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve come\xc3\xa7""ar ativo */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve come\xc3\xa7""ar ativo" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadingDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UnloadingDistance;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StreamingPriority;
	static void NewProp_bUseAsyncStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseAsyncStreaming;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GridSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingDistance;
	static void NewProp_bStartActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bStartActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONPCGStreamingConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_LoadingDistance = { "LoadingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingConfig, LoadingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingDistance_MetaData), NewProp_LoadingDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_UnloadingDistance = { "UnloadingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingConfig, UnloadingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnloadingDistance_MetaData), NewProp_UnloadingDistance_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_StreamingPriority = { "StreamingPriority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingConfig, StreamingPriority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingPriority_MetaData), NewProp_StreamingPriority_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bUseAsyncStreaming_SetBit(void* Obj)
{
	((FAURACRONPCGStreamingConfig*)Obj)->bUseAsyncStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bUseAsyncStreaming = { "bUseAsyncStreaming", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGStreamingConfig), &Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bUseAsyncStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseAsyncStreaming_MetaData), NewProp_bUseAsyncStreaming_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_GridSize = { "GridSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingConfig, GridSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridSize_MetaData), NewProp_GridSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_StreamingDistance = { "StreamingDistance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingConfig, StreamingDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingDistance_MetaData), NewProp_StreamingDistance_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bStartActive_SetBit(void* Obj)
{
	((FAURACRONPCGStreamingConfig*)Obj)->bStartActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bStartActive = { "bStartActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGStreamingConfig), &Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bStartActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bStartActive_MetaData), NewProp_bStartActive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_LoadingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_UnloadingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_StreamingPriority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bUseAsyncStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_GridSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_StreamingDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewProp_bStartActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONPCGStreamingConfig",
	Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::PropPointers),
	sizeof(FAURACRONPCGStreamingConfig),
	alignof(FAURACRONPCGStreamingConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONPCGStreamingConfig *****************************************

// ********** Begin ScriptStruct FAURACRONPCGStreamingEntry ****************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry;
class UScriptStruct* FAURACRONPCGStreamingEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONPCGStreamingEntry"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Entrada de streaming para elemento PCG\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Entrada de streaming para elemento PCG" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PCGElement_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingEntry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elemento PCG */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elemento PCG" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingConfig_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingEntry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xa3o de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xa3o de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCurrentlyStreamed_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingEntry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se est\xc3\xa1 atualmente sendo streamed */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se est\xc3\xa1 atualmente sendo streamed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingEntry" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** \xc3\x9altimo tempo de atualiza\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "\xc3\x9altimo tempo de atualiza\xc3\xa7\xc3\xa3o" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PCGElement;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingConfig;
	static void NewProp_bIsCurrentlyStreamed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCurrentlyStreamed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONPCGStreamingEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_PCGElement = { "PCGElement", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingEntry, PCGElement), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PCGElement_MetaData), NewProp_PCGElement_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_StreamingConfig = { "StreamingConfig", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingEntry, StreamingConfig), Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingConfig_MetaData), NewProp_StreamingConfig_MetaData) }; // 4039972323
void Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_bIsCurrentlyStreamed_SetBit(void* Obj)
{
	((FAURACRONPCGStreamingEntry*)Obj)->bIsCurrentlyStreamed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_bIsCurrentlyStreamed = { "bIsCurrentlyStreamed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGStreamingEntry), &Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_bIsCurrentlyStreamed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCurrentlyStreamed_MetaData), NewProp_bIsCurrentlyStreamed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingEntry, LastUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_PCGElement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_StreamingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_bIsCurrentlyStreamed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewProp_LastUpdateTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONPCGStreamingEntry",
	Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::PropPointers),
	sizeof(FAURACRONPCGStreamingEntry),
	alignof(FAURACRONPCGStreamingEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONPCGStreamingEntry ******************************************

// ********** Begin ScriptStruct FAURACRONPCGStreamingRegion ***************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion;
class UScriptStruct* FAURACRONPCGStreamingRegion::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion, (UObject*)Z_Construct_UPackage__Script_AURACRON(), TEXT("AURACRONPCGStreamingRegion"));
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Regi\xc3\xa3o de streaming\n */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regi\xc3\xa3o de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegionName_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingRegion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Nome da regi\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Nome da regi\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingRegion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Centro da regi\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Centro da regi\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Radius_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingRegion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio da regi\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio da regi\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "AURACRONPCGStreamingRegion" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se a regi\xc3\xa3o est\xc3\xa1 ativa */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se a regi\xc3\xa3o est\xc3\xa1 ativa" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_RegionName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAURACRONPCGStreamingRegion>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_RegionName = { "RegionName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingRegion, RegionName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegionName_MetaData), NewProp_RegionName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingRegion, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAURACRONPCGStreamingRegion, Radius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Radius_MetaData), NewProp_Radius_MetaData) };
void Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAURACRONPCGStreamingRegion*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAURACRONPCGStreamingRegion), &Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_RegionName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_Radius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewProp_bIsActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
	nullptr,
	&NewStructOps,
	"AURACRONPCGStreamingRegion",
	Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::PropPointers),
	sizeof(FAURACRONPCGStreamingRegion),
	alignof(FAURACRONPCGStreamingRegion),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion()
{
	if (!Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion.InnerSingleton, Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion.InnerSingleton;
}
// ********** End ScriptStruct FAURACRONPCGStreamingRegion *****************************************

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration Function InitializeWorldPartitionIntegration 
struct Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_InitializeWorldPartitionIntegration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Inicializar integra\xc3\xa7\xc3\xa3o com World Partition */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inicializar integra\xc3\xa7\xc3\xa3o com World Partition" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_InitializeWorldPartitionIntegration_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, nullptr, "InitializeWorldPartitionIntegration", nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_InitializeWorldPartitionIntegration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_InitializeWorldPartitionIntegration_Statics::Function_MetaDataParams)},  };
UFunction* Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_InitializeWorldPartitionIntegration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_InitializeWorldPartitionIntegration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGWorldPartitionIntegration::execInitializeWorldPartitionIntegration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeWorldPartitionIntegration();
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGWorldPartitionIntegration Function InitializeWorldPartitionIntegration 

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration Function RegisterPCGElementForStreaming 
struct Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics
{
	struct AURACRONPCGWorldPartitionIntegration_eventRegisterPCGElementForStreaming_Parms
	{
		AActor* Element;
		FAURACRONPCGStreamingConfig Config;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Registrar elemento PCG para streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Registrar elemento PCG para streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Element;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::NewProp_Element = { "Element", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGWorldPartitionIntegration_eventRegisterPCGElementForStreaming_Parms, Element), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGWorldPartitionIntegration_eventRegisterPCGElementForStreaming_Parms, Config), Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 4039972323
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::NewProp_Element,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::NewProp_Config,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, nullptr, "RegisterPCGElementForStreaming", Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::AURACRONPCGWorldPartitionIntegration_eventRegisterPCGElementForStreaming_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::AURACRONPCGWorldPartitionIntegration_eventRegisterPCGElementForStreaming_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGWorldPartitionIntegration::execRegisterPCGElementForStreaming)
{
	P_GET_OBJECT(AActor,Z_Param_Element);
	P_GET_STRUCT_REF(FAURACRONPCGStreamingConfig,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterPCGElementForStreaming(Z_Param_Element,Z_Param_Out_Config);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGWorldPartitionIntegration Function RegisterPCGElementForStreaming 

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration Function UpdateStreamingForPlayerLocation 
struct Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics
{
	struct AURACRONPCGWorldPartitionIntegration_eventUpdateStreamingForPlayerLocation_Parms
	{
		FVector PlayerLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AURACRON|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Atualizar streaming baseado na posi\xc3\xa7\xc3\xa3o do jogador */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Atualizar streaming baseado na posi\xc3\xa7\xc3\xa3o do jogador" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::NewProp_PlayerLocation = { "PlayerLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AURACRONPCGWorldPartitionIntegration_eventUpdateStreamingForPlayerLocation_Parms, PlayerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerLocation_MetaData), NewProp_PlayerLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::NewProp_PlayerLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::FuncParams = { { (UObject*(*)())Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, nullptr, "UpdateStreamingForPlayerLocation", Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::AURACRONPCGWorldPartitionIntegration_eventUpdateStreamingForPlayerLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::Function_MetaDataParams)},  };
static_assert(sizeof(Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::AURACRONPCGWorldPartitionIntegration_eventUpdateStreamingForPlayerLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAURACRONPCGWorldPartitionIntegration::execUpdateStreamingForPlayerLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_PlayerLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateStreamingForPlayerLocation(Z_Param_Out_PlayerLocation);
	P_NATIVE_END;
}
// ********** End Class AAURACRONPCGWorldPartitionIntegration Function UpdateStreamingForPlayerLocation 

// ********** Begin Class AAURACRONPCGWorldPartitionIntegration ************************************
void AAURACRONPCGWorldPartitionIntegration::StaticRegisterNativesAAURACRONPCGWorldPartitionIntegration()
{
	UClass* Class = AAURACRONPCGWorldPartitionIntegration::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "InitializeWorldPartitionIntegration", &AAURACRONPCGWorldPartitionIntegration::execInitializeWorldPartitionIntegration },
		{ "RegisterPCGElementForStreaming", &AAURACRONPCGWorldPartitionIntegration::execRegisterPCGElementForStreaming },
		{ "UpdateStreamingForPlayerLocation", &AAURACRONPCGWorldPartitionIntegration::execUpdateStreamingForPlayerLocation },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
FClassRegistrationInfo Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration;
UClass* AAURACRONPCGWorldPartitionIntegration::GetPrivateStaticClass()
{
	using TClass = AAURACRONPCGWorldPartitionIntegration;
	if (!Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("AURACRONPCGWorldPartitionIntegration"),
			Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration.InnerSingleton,
			StaticRegisterNativesAAURACRONPCGWorldPartitionIntegration,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration.InnerSingleton;
}
UClass* Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_NoRegister()
{
	return AAURACRONPCGWorldPartitionIntegration::GetPrivateStaticClass();
}
struct Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Integra\xc3\xa7\xc3\xa3o do sistema PCG com World Partition do UE 5.6\n * Gerencia streaming eficiente de conte\xc3\xba""do procedural\n */" },
#endif
		{ "IncludePath", "PCG/AURACRONPCGWorldPartitionIntegration.h" },
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integra\xc3\xa7\xc3\xa3o do sistema PCG com World Partition do UE 5.6\nGerencia streaming eficiente de conte\xc3\xba""do procedural" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultStreamingConfig_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configura\xc3\xa7\xc3\xb5""es padr\xc3\xa3o de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configura\xc3\xa7\xc3\xb5""es padr\xc3\xa3o de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingRadius_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Raio de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Raio de streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpdateInterval_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Intervalo de atualiza\xc3\xa7\xc3\xa3o */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Intervalo de atualiza\xc3\xa7\xc3\xa3o" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoUpdateStreaming_MetaData[] = {
		{ "Category", "AURACRON|WorldPartition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Se deve atualizar streaming automaticamente */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Se deve atualizar streaming automaticamente" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldPartition_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Refer\xc3\xaancia ao World Partition */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Refer\xc3\xaancia ao World Partition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingElements_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elementos registrados para streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elementos registrados para streaming" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegisteredElements_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Elementos registrados com ID */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Elementos registrados com ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingRegions_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Regi\xc3\xb5""es de streaming */" },
#endif
		{ "ModuleRelativePath", "Public/PCG/AURACRONPCGWorldPartitionIntegration.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regi\xc3\xb5""es de streaming" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultStreamingConfig;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_UpdateInterval;
	static void NewProp_bAutoUpdateStreaming_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoUpdateStreaming;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldPartition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingElements_ValueProp;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_StreamingElements_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_StreamingElements;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegisteredElements_ValueProp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RegisteredElements_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RegisteredElements;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StreamingRegions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_StreamingRegions;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_InitializeWorldPartitionIntegration, "InitializeWorldPartitionIntegration" }, // 1205790007
		{ &Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_RegisterPCGElementForStreaming, "RegisterPCGElementForStreaming" }, // 404053965
		{ &Z_Construct_UFunction_AAURACRONPCGWorldPartitionIntegration_UpdateStreamingForPlayerLocation, "UpdateStreamingForPlayerLocation" }, // 1349655335
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAURACRONPCGWorldPartitionIntegration>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_DefaultStreamingConfig = { "DefaultStreamingConfig", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, DefaultStreamingConfig), Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultStreamingConfig_MetaData), NewProp_DefaultStreamingConfig_MetaData) }; // 4039972323
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingRadius = { "StreamingRadius", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, StreamingRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingRadius_MetaData), NewProp_StreamingRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_UpdateInterval = { "UpdateInterval", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, UpdateInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpdateInterval_MetaData), NewProp_UpdateInterval_MetaData) };
void Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bAutoUpdateStreaming_SetBit(void* Obj)
{
	((AAURACRONPCGWorldPartitionIntegration*)Obj)->bAutoUpdateStreaming = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bAutoUpdateStreaming = { "bAutoUpdateStreaming", nullptr, (EPropertyFlags)0x0020080000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAURACRONPCGWorldPartitionIntegration), &Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bAutoUpdateStreaming_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoUpdateStreaming_MetaData), NewProp_bAutoUpdateStreaming_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_WorldPartition = { "WorldPartition", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, WorldPartition), Z_Construct_UClass_UWorldPartition_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldPartition_MetaData), NewProp_WorldPartition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingElements_ValueProp = { "StreamingElements", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig, METADATA_PARAMS(0, nullptr) }; // 4039972323
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingElements_Key_KeyProp = { "StreamingElements_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingElements = { "StreamingElements", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, StreamingElements), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingElements_MetaData), NewProp_StreamingElements_MetaData) }; // 4039972323
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_RegisteredElements_ValueProp = { "RegisteredElements", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry, METADATA_PARAMS(0, nullptr) }; // 2653175999
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_RegisteredElements_Key_KeyProp = { "RegisteredElements_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_RegisteredElements = { "RegisteredElements", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, RegisteredElements), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegisteredElements_MetaData), NewProp_RegisteredElements_MetaData) }; // 2653175999
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingRegions_Inner = { "StreamingRegions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion, METADATA_PARAMS(0, nullptr) }; // 4185464151
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingRegions = { "StreamingRegions", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAURACRONPCGWorldPartitionIntegration, StreamingRegions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingRegions_MetaData), NewProp_StreamingRegions_MetaData) }; // 4185464151
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_DefaultStreamingConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_UpdateInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_bAutoUpdateStreaming,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_WorldPartition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingElements_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingElements_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_RegisteredElements_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_RegisteredElements_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_RegisteredElements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingRegions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::NewProp_StreamingRegions,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_AURACRON,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::ClassParams = {
	&AAURACRONPCGWorldPartitionIntegration::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::PropPointers),
	0,
	0x009001A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::Class_MetaDataParams), Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration()
{
	if (!Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration.OuterSingleton, Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration.OuterSingleton;
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAURACRONPCGWorldPartitionIntegration);
AAURACRONPCGWorldPartitionIntegration::~AAURACRONPCGWorldPartitionIntegration() {}
// ********** End Class AAURACRONPCGWorldPartitionIntegration **************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h__Script_AURACRON_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAURACRONPCGStreamingConfig::StaticStruct, Z_Construct_UScriptStruct_FAURACRONPCGStreamingConfig_Statics::NewStructOps, TEXT("AURACRONPCGStreamingConfig"), &Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONPCGStreamingConfig), 4039972323U) },
		{ FAURACRONPCGStreamingEntry::StaticStruct, Z_Construct_UScriptStruct_FAURACRONPCGStreamingEntry_Statics::NewStructOps, TEXT("AURACRONPCGStreamingEntry"), &Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONPCGStreamingEntry), 2653175999U) },
		{ FAURACRONPCGStreamingRegion::StaticStruct, Z_Construct_UScriptStruct_FAURACRONPCGStreamingRegion_Statics::NewStructOps, TEXT("AURACRONPCGStreamingRegion"), &Z_Registration_Info_UScriptStruct_FAURACRONPCGStreamingRegion, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAURACRONPCGStreamingRegion), 4185464151U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAURACRONPCGWorldPartitionIntegration, AAURACRONPCGWorldPartitionIntegration::StaticClass, TEXT("AAURACRONPCGWorldPartitionIntegration"), &Z_Registration_Info_UClass_AAURACRONPCGWorldPartitionIntegration, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAURACRONPCGWorldPartitionIntegration), 2652728564U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h__Script_AURACRON_2147863788(TEXT("/Script/AURACRON"),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h__Script_AURACRON_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h__Script_AURACRON_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h__Script_AURACRON_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_AURACRON_Source_AURACRON_Public_PCG_AURACRONPCGWorldPartitionIntegration_h__Script_AURACRON_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
