// AURACRONPCGPrismalFlow.h
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Classe para gerenciar o Prismal Flow serpentino

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
// Forward declaration para evitar dependência direta
class UPCGComponent;
#include "PCGSettings.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "Components/SplineComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "PCG/AURACRONPCGWorldPartitionIntegration.h"
#include "AURACRONPCGPrismalFlow.generated.h"

class UPCGComponent;
class USplineComponent;
class UNiagaraComponent;

/**
 * Enumeração para definir os tipos de ilhas no Prismal Flow
 * Alinhado com EAURACRONIslandType em AURACRONPCGSubsystem.h
 */
UENUM(BlueprintType)
enum class EPrismalFlowIslandType : uint8
{
    None         UMETA(DisplayName = "None"),               // Nenhum tipo específico
    Nexus        UMETA(DisplayName = "Nexus Island"),       // Ilha central com poderes especiais
    Sanctuary    UMETA(DisplayName = "Sanctuary Island"),   // Ilha de refúgio e recuperação
    Arsenal      UMETA(DisplayName = "Arsenal Island"),     // Ilha com upgrades de armas e buffs temporários
    Chaos        UMETA(DisplayName = "Chaos Island"),       // Ilha com perigos ambientais e recompensas de alto risco
    Battlefield  UMETA(DisplayName = "Battlefield Island"), // Ilha para combates e confrontos
    Amplifier    UMETA(DisplayName = "Amplifier Island"),   // Ilha que amplifica poderes
    Gateway      UMETA(DisplayName = "Gateway Island"),     // Ilha que permite teleporte
    Corrupted    UMETA(DisplayName = "Corrupted Island")    // Ilha corrompida com efeitos negativos
};

/**
 * Estrutura para definir um segmento do Prismal Flow
 */
USTRUCT(BlueprintType)
struct AURACRON_API FPrismalFlowSegment
{
    GENERATED_BODY()

    /** Posição central do segmento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    FVector Position;
    
    /** Largura do flow neste segmento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Width;
    
    /** Intensidade do flow neste segmento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float Intensity;
    
    /** Velocidade do flow neste segmento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float FlowSpeed;
    
    /** Tipo de ambiente que este segmento atravessa */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONEnvironmentType EnvironmentType;
    
    /** Indica se este segmento contém uma ilha */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bHasIsland;
    
    /** Tipo de ilha neste segmento (se houver) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (EditCondition = "bHasIsland"))
    EPrismalFlowIslandType IslandType;
    
    /** Indica se este é um segmento calmo do fluxo (ideal para Ilhas Santuário) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsCalmFlowSection;
    
    /** Indica se este segmento usa padrão predeterminado (Fase 1) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bUsesPredeterminedPattern;
    
    FPrismalFlowSegment()
        : Position(FVector::ZeroVector)
        , Width(3000.0f) // 30 metros padrão
        , Intensity(1.0f)
        , FlowSpeed(1.0f)
        , EnvironmentType(EAURACRONEnvironmentType::Radiant)
        , bHasIsland(false)
        , IslandType(EPrismalFlowIslandType::None)
        , bIsCalmFlowSection(false)
        , bUsesPredeterminedPattern(true) // Por padrão, usa padrão predeterminado na Fase 1
    {
    }
};

/**
 * Classe base para todas as ilhas no Prismal Flow
 */
UCLASS()
class AURACRON_API APrismalFlowIsland : public AActor
{
    GENERATED_BODY()
    
public:
    APrismalFlowIsland();
    
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    
    // Ativa/desativa a ilha
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Island")
    virtual void SetIslandActive(bool bActive);
    
    // Aplica efeito ao jogador quando está na ilha
    UFUNCTION()
    virtual void ApplyIslandEffect(AActor* OverlappingActor);
    
    // Configura o tipo de ilha
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Island")
    void SetIslandType(EPrismalFlowIslandType NewType);
    
    // Retorna o tipo de ilha
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Island")
    EPrismalFlowIslandType GetIslandType() const { return IslandType; }
    
    // Configura a posição da ilha no flow (0-1)
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Island")
    void SetFlowPosition(float InFlowPosition);
    
    // Retorna a posição da ilha no flow (0-1)
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Island")
    float GetFlowPosition() const { return FlowPosition; }
    
protected:
    // Malha da ilha
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Island")
    UStaticMeshComponent* IslandMesh;
    
    // Efeito visual da ilha
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Island")
    UNiagaraComponent* IslandEffect;
    
    // Área de interação
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Island")
    USphereComponent* InteractionArea;
    
    // Tipo de ilha
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Island")
    EPrismalFlowIslandType IslandType;
    
    // Posição no flow (0-1)
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Island")
    float FlowPosition;
    
    // Estado de ativação da ilha
    UPROPERTY(BlueprintReadOnly, Category = "Prismal Flow|Island")
    bool bIsActive;
    
    // Material dinâmico da ilha
    UPROPERTY()
    UMaterialInstanceDynamic* IslandMaterial;
    
    // Atualiza o visual da ilha baseado no tipo
    virtual void UpdateIslandVisuals();
};

// ANexusIsland está definida em AURACRONPCGNexusIsland.h

/**
 * Ator para gerenciar o Prismal Flow serpentino que conecta os três ambientes
 * O flow tem largura variável, curvas matemáticas precisas e ilhas estratégicas
 * Integra com World Partition e Data Layers para streaming eficiente
 */
UCLASS()
class AURACRON_API AAURACRONPCGPrismalFlow : public AActor
{
    GENERATED_BODY()

public:
    AAURACRONPCGPrismalFlow();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    // ========================================
    // FUNÇÕES PÚBLICAS DE CONFIGURAÇÃO
    // ========================================
    
    /** Gerar o Prismal Flow completo */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void GeneratePrismalFlow();
    
    /** Atualizar o flow para uma fase específica do mapa */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);
    
    /** Definir intensidade global do flow */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void SetFlowIntensity(float NewIntensity);
    
    /** Obter posição no flow baseada em parâmetro T (0-1) */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow")
    FVector GetFlowPositionAtT(float T) const;
    
    /** Obter largura do flow em uma posição específica */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow")
    float GetFlowWidthAtT(float T) const;
    
    /** Verificar se uma posição está dentro do flow */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow")
    bool IsPositionInFlow(const FVector& Position, float Tolerance = 100.0f) const;
    
    // Inicializar o sistema de fluxo prismal
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow")
    void InitializePrismalFlow();
    
    // Configurar para Fase 1: Despertar
    UFUNCTION(BlueprintCallable, Category = "PrismalFlow")
    void ConfigureForAwakeningPhase(bool bIsEntryDevice);
    
    // Configurar para Fase 2: Convergência (fortalecimento gradual)
    UFUNCTION(BlueprintCallable, Category = "PrismalFlow")
    void ConfigureForConvergencePhase(bool bIsEntryDevice, bool bIsMidDevice, bool bIsHighDevice);
    
    // Definir padrão predeterminado do fluxo prismal
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PrismalFlow")
    void SetPredeterminedPattern(bool bUsePredeterminedPattern);

    // ========================================
    // FUNÇÕES DE ILHAS ESTRATÉGICAS
    // ========================================
    
    /** Adicionar uma ilha estratégica em uma posição específica do flow */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Islands")
    APrismalFlowIsland* AddIslandAtPosition(float FlowPosition, EPrismalFlowIslandType IslandType);
    
    /** Remover uma ilha específica */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Islands")
    void RemoveIsland(APrismalFlowIsland* Island);
    
    /** Obter todas as ilhas do flow */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Islands")
    TArray<APrismalFlowIsland*> GetAllIslands() const;
    
    /** Obter ilhas de um tipo específico */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Islands")
    TArray<APrismalFlowIsland*> GetIslandsByType(EPrismalFlowIslandType IslandType) const;
    
    // ========================================
    // FUNÇÕES DE INTEGRAÇÃO COM WORLD PARTITION
    // ========================================
    
    /** Configurar streaming para o Prismal Flow */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|WorldPartition")
    void ConfigureWorldPartitionStreaming(const FAURACRONPCGStreamingConfig& StreamingConfig);
    
    /** Associar o flow a uma Data Layer específica */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|DataLayers")
    void AssociateWithDataLayer(const FName& DataLayerName);
    
    /** Verificar se uma posição está dentro do flow */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow")
    bool IsPositionInFlow(const FVector& Position, float Tolerance = 100.0f) const;

protected:
    // ========================================
    // COMPONENTES
    // ========================================
    
    /** Componente PCG principal */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Components")
    TObjectPtr<UPCGComponent> PCGComponent;
    
    /** Spline que define o caminho do flow */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Components")
    TObjectPtr<USplineComponent> FlowSpline;
    
    /** Componente de efeitos visuais principais */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Components")
    TObjectPtr<UNiagaraComponent> MainFlowEffect;

    /** Mesh do flow (alias para compatibilidade) */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Components")
    TObjectPtr<UStaticMeshComponent> FlowMesh;

    /** Efeito do flow (alias para compatibilidade) */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Components")
    TObjectPtr<UNiagaraComponent> FlowEffect;
    
    /** Componente de colisão para o flow */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Prismal Flow|Components")
    TObjectPtr<UBoxComponent> FlowCollision;

    // ========================================
    // PROPRIEDADES CONFIGURÁVEIS
    // ========================================
    
    /** Intensidade global do flow */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Settings", meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float GlobalFlowIntensity;
    
    /** Velocidade base do flow */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Settings", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float BaseFlowSpeed;
    
    /** Equipe controladora atual do flow (0 = neutro, 1 = Equipe A, 2 = Equipe B) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Prismal Flow|Control")
    int32 ControllingTeam;
    
    /** Número de pontos de controle para a curva serpentina */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Settings", meta = (ClampMin = "10", ClampMax = "50"))
    int32 NumControlPoints;
    
    /** Amplitude das curvas serpentinas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Settings", meta = (ClampMin = "500.0", ClampMax = "3000.0"))
    float SerpentineAmplitude;
    
    /** Frequência das curvas serpentinas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Settings", meta = (ClampMin = "1.0", ClampMax = "6.0"))
    float SerpentineFrequency;

    /** Escala de atividade do flow */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Settings", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float ActivityScale;
    
    // ========================================
    // ILHAS ESTRATÉGICAS
    // ========================================
    
    /** Ilhas estratégicas no flow */
    UPROPERTY()
    TArray<APrismalFlowIsland*> Islands;
    
    /** Configurações para geração de ilhas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Islands")
    int32 MaxIslandCount;
    
    /** Distância mínima entre ilhas (em unidades de flow T 0-1) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Islands", meta = (ClampMin = "0.05", ClampMax = "0.5"))
    float MinIslandSpacing;
    
    /** Classes de ilhas para cada tipo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|Islands")
    TMap<EPrismalFlowIslandType, TSubclassOf<APrismalFlowIsland>> IslandClasses;
    
    // ========================================
    // INTEGRAÇÃO COM WORLD PARTITION
    // ========================================
    
    /** Configuração de streaming para World Partition */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|WorldPartition")
    FAURACRONPCGStreamingConfig StreamingConfiguration;
    
    /** Nome da Data Layer associada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prismal Flow|DataLayers")
    FName AssociatedDataLayer;

private:
    // ========================================
    // DADOS INTERNOS
    // ========================================
    
    /** Segmentos do flow gerados */
    UPROPERTY()
    TArray<FPrismalFlowSegment> FlowSegments;
    
    /** Componentes gerados dinamicamente */
    UPROPERTY()
    TArray<UActorComponent*> GeneratedComponents;
    
    /** Fase atual do mapa */
    UPROPERTY()
    EAURACRONMapPhase CurrentMapPhase;
    
    /** Tempo acumulado para animações */
    UPROPERTY()
    float AccumulatedTime;
    
    /** Entradas de streaming para World Partition */
    UPROPERTY()
    TArray<FAURACRONPCGStreamingEntry> StreamingEntries;
    
    /** Material dinâmico para o flow */
    UPROPERTY()
    UMaterialInstanceDynamic* FlowMaterialInstance;

    // ========================================
    // FUNÇÕES INTERNAS
    // ========================================
    
    /** Gerar pontos da curva serpentina */
    TArray<FVector> GenerateSerpentineCurve();
    
    /** Calcular largura do flow baseada na posição */
    float CalculateFlowWidth(float T, EAURACRONEnvironmentType EnvironmentType);
    
    /** Calcular intensidade do flow baseada na posição */
    float CalculateFlowIntensity(float T, EAURACRONMapPhase MapPhase);
    
    /** Gerar segmentos do flow */
    void GenerateFlowSegments();
    
    /** Gerar efeitos visuais */
    void GenerateVisualEffects();
    
    /** Gerar ilhas estratégicas ao longo do flow */
    void GenerateStrategicIslands();
    
    /** Limpar elementos gerados anteriormente */
    void ClearGeneratedElements();
    
    /** Atualizar efeitos dinâmicos */
    void UpdateDynamicEffects(float DeltaTime);
    
    /** Determinar tipo de ambiente baseado na posição */
    EAURACRONEnvironmentType DetermineEnvironmentType(const FVector& Position);
    
    /** Aplicar efeitos específicos do ambiente */
    void ApplyEnvironmentEffects(FPrismalFlowSegment& Segment);
    
    /** Calcular volatilidade do flow baseada na fase */
    float CalculateFlowVolatility(EAURACRONMapPhase MapPhase);
    
    /** Gerar pontos de interseção com trilhas */
    TArray<FVector> GenerateTrailIntersections();
    
    /** Criar efeitos de transição entre ambientes */
    void CreateEnvironmentTransitions();
    
    /** Atualizar parâmetros baseados no tempo */
    void UpdateTimeBasedParameters();
    
    /** Gerar obstáculos e características especiais */
    void GenerateFlowObstacles();
    
    /** Calcular direção do flow em uma posição */
    FVector CalculateFlowDirection(float T) const;
    
    /** Aplicar efeitos de fase do mapa */
    void ApplyMapPhaseEffects();

    /** Obter cor do ambiente */
    FLinearColor GetEnvironmentColor(EAURACRONEnvironmentType EnvironmentType);

    /** Obter raio da ilha baseado no tipo */
    float GetIslandRadius(EPrismalFlowIslandType IslandType);
    
    /** Configurar streaming para World Partition */
    void ConfigureWorldPartitionStreaming();
    
    /** Associar o flow a uma Data Layer */
    void AssociateWithDataLayer(const FName& DataLayerName);
    
    /** Atualizar as ilhas estratégicas para a fase atual do mapa */
    void UpdateIslandsForMapPhase(EAURACRONMapPhase NewPhase);
    
    /** Atualizar os efeitos visuais para a fase atual do mapa */
    void UpdateVisualEffectsForMapPhase(EAURACRONMapPhase NewPhase);
    
    /** Atualizar a configuração de streaming para a fase atual do mapa */
    void UpdateStreamingForMapPhase(EAURACRONMapPhase NewPhase);
    
    /** Criar uma ilha estratégica de um tipo específico */
    APrismalFlowIsland* CreateIsland(EPrismalFlowIslandType IslandType, float FlowPosition);
    
    /** Verificar se uma posição no flow está disponível para uma ilha */
    bool IsFlowPositionAvailableForIsland(float FlowPosition, float MinSpacing = 0.05f) const;

public:
    /** Obter componente PCG para acesso externo */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Components")
    UPCGComponent* GetPCGComponent() const { return PCGComponent; }

    /** Definir escala de atividade */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Settings")
    void SetActivityScale(float NewActivityScale) { ActivityScale = NewActivityScale; }

    /** Replicação de propriedades */
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

    /** Callback quando flow é ativado */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void OnFlowActivated();

    /** Callback quando flow é desativado */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void OnFlowDeactivated();

    /** Atualizar flow para nova fase */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void UpdateFlowPhase(EAURACRONMapPhase NewPhase);

    /** Obter cor do flow para fase */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow")
    FLinearColor GetFlowColorForPhase(EAURACRONMapPhase Phase) const;

    /** Obter cor do flow baseada na equipe controladora */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Control")
    FLinearColor GetFlowColorForTeam() const;
    
    /** Definir equipe controladora do flow */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow|Control")
    void SetControllingTeam(int32 TeamID);
    
    /** Obter equipe controladora atual */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|Control")
    int32 GetControllingTeam() const { return ControllingTeam; }

    /** Gerar caminho do flow */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void GenerateFlowPath();

    /** Aplicar efeitos visuais da fase */
    UFUNCTION(BlueprintCallable, Category = "Prismal Flow")
    void ApplyPhaseVisualEffects(const FLinearColor& PhaseColor, float PhaseIntensity, const FString& EffectName);
    
    /** Obter a configuração de streaming atual */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|WorldPartition")
    FAURACRONPCGStreamingConfig GetStreamingConfiguration() const { return StreamingConfiguration; }
    
    /** Obter a Data Layer associada */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow|DataLayers")
    FName GetAssociatedDataLayer() const { return AssociatedDataLayer; }
    
    /** Obter a fase atual do mapa */
    UFUNCTION(BlueprintPure, Category = "Prismal Flow")
    EAURACRONMapPhase GetCurrentMapPhase() const { return CurrentMapPhase; }
    
    // Funções públicas para configuração de streaming
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Streaming")
    void ConfigureStreamingSettings(const FAURACRONPCGStreamingConfig& StreamingConfig);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Streaming")
    void SetStreamingEnabled(bool bEnabled);

    // Funções para efeitos visuais
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Visual Effects")
    void SetVisualEffectsEnabled(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Visual Effects")
    void UpdateVisualEffectsForPhase(EAURACRONMapPhase Phase);

    // Funções para gerenciamento de ilhas
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Islands")
    void RegisterStrategicIsland(APrismalFlowIsland* Island);

    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Islands")
    void UnregisterStrategicIsland(APrismalFlowIsland* Island);

    // Funções para volatilidade adaptada ao hardware
    UFUNCTION(BlueprintCallable, Category = "AURACRON|PCG|Hardware")
    void UpdateVolatilityForHardware();

    UFUNCTION(BlueprintPure, Category = "AURACRON|PCG|Hardware")
    float GetHardwareVolatilityMultiplier() const;
};
