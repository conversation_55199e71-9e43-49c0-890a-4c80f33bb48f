// AURACRONPCGJungleSystem.h
// Sistema de Jungle para AURACRON baseado no League of Legends - UE 5.6
// 12 camps simétricos com Blue/Red buffs equivalents
// Sistema de IA adaptativa para ajustar dificuldade e comportamento baseado no jogador

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGLaneSystem.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "PCG/AURACRONPCGWorldPartitionIntegration.h"
#include "AURACRONPCGJungleSystem.generated.h"

/**
 * Enumeração para definir os perfis de jogador para IA adaptativa
 */
UENUM(BlueprintType)
enum class EAURACRONJunglePlayerProfile : uint8
{
    Aggressive      UMETA(DisplayName = "Aggressive"),      // Jogador agressivo que invade jungle frequentemente
    Farming         UMETA(DisplayName = "Farming"),         // Jogador que foca em farmar jungle
    Objective       UMETA(DisplayName = "Objective"),       // Jogador que prioriza objetivos
    Supportive      UMETA(DisplayName = "Supportive"),      // Jogador que compartilha recursos
    Balanced        UMETA(DisplayName = "Balanced")         // Jogador com estilo equilibrado
};

/**
 * Enumeração para definir os comportamentos adaptativos dos camps
 */
UENUM(BlueprintType)
enum class EAURACRONJungleAdaptiveBehavior : uint8
{
    Standard        UMETA(DisplayName = "Standard"),        // Comportamento padrão
    Defensive       UMETA(DisplayName = "Defensive"),       // Mais resistente, menos dano
    Aggressive      UMETA(DisplayName = "Aggressive"),      // Mais dano, menos resistente
    Rewarding       UMETA(DisplayName = "Rewarding"),       // Mais recompensas
    Challenging     UMETA(DisplayName = "Challenging")      // Mais difícil, melhores recompensas
};

/**
 * Enumeração para definir as estratégias de jungle previstas
 */
UENUM(BlueprintType)
enum class EAURACRONJungleStrategy : uint8
{
    Balanced        UMETA(DisplayName = "Balanced"),        // Estratégia balanceada
    Aggressive      UMETA(DisplayName = "Aggressive"),      // Foco em invasões e ganks
    Farming         UMETA(DisplayName = "Farming"),         // Foco em farming da jungle
    Objective       UMETA(DisplayName = "Objective")        // Foco em objetivos
};

/**
 * Estrutura para armazenar dados de adaptação da IA
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONJungleAdaptiveData
{
    GENERATED_BODY()
    
    /** Perfil atual do jogador */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONJunglePlayerProfile PlayerProfile;
    
    /** Comportamento adaptativo atual */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONJungleAdaptiveBehavior AdaptiveBehavior;
    
    /** Estratégia prevista baseada na composição da equipe */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONJungleStrategy PredictedStrategy;
    
    /** Nível de dificuldade adaptativa (0.0-2.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.0", ClampMax = "2.0"))
    float AdaptiveDifficulty;
    
    /** Histórico de interações do jogador */
    UPROPERTY()
    TMap<EAURACRONJungleCampType, int32> CampInteractionCount;
    
    /** Tempo médio para limpar camps */
    UPROPERTY()
    TMap<EAURACRONJungleCampType, float> AverageClearTime;
    
    /** Padrão de rota do jogador */
    UPROPERTY()
    TArray<EAURACRONJungleCampType> PreferredClearPath;
    
    /** Contador de invasões de jungle */
    UPROPERTY()
    int32 InvasionCount;
    
    /** Contador de objetivos tomados */
    UPROPERTY()
    int32 ObjectivesSecured;
    
    /** Tempo desde a última adaptação */
    UPROPERTY()
    float TimeSinceLastAdaptation;
    
    FAURACRONJungleAdaptiveData()
        : PlayerProfile(EAURACRONJunglePlayerProfile::Balanced)
        , AdaptiveBehavior(EAURACRONJungleAdaptiveBehavior::Standard)
        , PredictedStrategy(EAURACRONJungleStrategy::Balanced)
        , AdaptiveDifficulty(1.0f)
        , InvasionCount(0)
        , ObjectivesSecured(0)
        , TimeSinceLastAdaptation(0.0f)
    {
    }
};

/**
 * Estrutura wrapper para array de componentes de mesh (resolve problema UHT com TMap<Enum, TArray<Type>>)
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONMeshComponentArray
{
    GENERATED_BODY()

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
    TArray<UStaticMeshComponent*> MeshComponents;

    FAURACRONMeshComponentArray()
    {
    }
};

/**
 * Tipos de jungle camps baseados no LoL
 */
UENUM(BlueprintType)
enum class EAURACRONJungleCampType : uint8
{
    // BUFF CAMPS (equivalentes ao Blue/Red Buff)
    RadiantEssence      UMETA(DisplayName = "Radiant Essence"),     // Blue Buff equivalent (mana regen + CDR)
    ChaosEssence        UMETA(DisplayName = "Chaos Essence"),       // Red Buff equivalent (damage + slow)
    
    // CAMPS NORMAIS (baseados no LoL)
    StoneGuardians      UMETA(DisplayName = "Stone Guardians"),     // Krugs equivalent
    PrismalToad         UMETA(DisplayName = "Prismal Toad"),        // Gromp equivalent  
    SpectralPack        UMETA(DisplayName = "Spectral Pack"),       // Wolves equivalent
    WindSpirits         UMETA(DisplayName = "Wind Spirits"),        // Raptors equivalent
    FluxCrawler         UMETA(DisplayName = "Flux Crawler"),        // Scuttle Crab equivalent
};

/**
 * Informações de um jungle camp
 */
USTRUCT(BlueprintType)
struct AURACRON_API FAURACRONJungleCampInfo
{
    GENERATED_BODY()

    /** Tipo do camp */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    EAURACRONJungleCampType CampType;
    
    /** Posição do camp para cada ambiente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TMap<EAURACRONEnvironmentType, FVector> PositionsByEnvironment;
    
    /** Raio do camp */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float CampRadius;
    
    /** Tempo de respawn em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float RespawnTime;
    
    /** Se é um buff camp */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsBuffCamp;
    
    /** Nível de dificuldade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 DifficultyLevel;
    
    /** Recompensas do camp */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    TMap<FString, float> Rewards;
    
    /** Se o camp está ativo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    bool bIsActive;
    
    /** Tempo até próximo respawn */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    float TimeUntilRespawn;
    
    /** Lado do mapa (0 = Team 1, 1 = Team 2, 2 = Neutro) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite)
    int32 MapSide;
    
    // ========================================
    // PROPRIEDADES DE IA ADAPTATIVA
    // ========================================
    
    /** Comportamento adaptativo atual */
    UPROPERTY(BlueprintReadOnly)
    EAURACRONJungleAdaptiveBehavior CurrentBehavior;
    
    /** Multiplicador de dano adaptativo */
    UPROPERTY(BlueprintReadOnly)
    float AdaptiveDamageMultiplier;
    
    /** Multiplicador de saúde adaptativo */
    UPROPERTY(BlueprintReadOnly)
    float AdaptiveHealthMultiplier;
    
    /** Multiplicador de recompensa adaptativo */
    UPROPERTY(BlueprintReadOnly)
    float AdaptiveRewardMultiplier;
    
    /** Tempo médio para ser derrotado */
    UPROPERTY()
    float AverageDefeatTime;
    
    /** Contagem de derrotas */
    UPROPERTY()
    int32 DefeatCount;
    
    /** Contagem de invasões sofridas */
    UPROPERTY()
    int32 InvasionsSuffered;
    
    /** Prioridade para adaptação */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float AdaptationPriority;
    
    FAURACRONJungleCampInfo()
        : CampType(EAURACRONJungleCampType::SpectralPack)
        , CampRadius(600.0f)
        , RespawnTime(120.0f)
        , bIsBuffCamp(false)
        , DifficultyLevel(1)
        , bIsActive(true)
        , TimeUntilRespawn(0.0f)
        , MapSide(2)
        , CurrentBehavior(EAURACRONJungleAdaptiveBehavior::Standard)
        , AdaptiveDamageMultiplier(1.0f)
        , AdaptiveHealthMultiplier(1.0f)
        , AdaptiveRewardMultiplier(1.0f)
        , AverageDefeatTime(0.0f)
        , DefeatCount(0)
        , InvasionsSuffered(0)
        , AdaptationPriority(0.5f)
    {
    }
};

/**
 * Sistema de jungle para AURACRON baseado no layout do LoL
 * 12 camps simétricos distribuídos entre as lanes
 */
UCLASS()
class AURACRON_API AAURACRONPCGJungleSystem : public AActor
{
    GENERATED_BODY()

public:
    AAURACRONPCGJungleSystem();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

    virtual void BeginPlay() override;

    // ========================================
    // FUNÇÕES PÚBLICAS - GERAÇÃO E GERENCIAMENTO
    // ========================================
    
    /** Gerar todos os jungle camps */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem")
    void GenerateJungleCamps();
    
    /** Gerar camps para ambiente específico */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem")
    void GenerateCampsForEnvironment(EAURACRONEnvironmentType Environment);
    
    /** Obter informações de todos os camps */
    UFUNCTION(BlueprintPure, Category = "AURACRON|JungleSystem")
    TArray<FAURACRONJungleCampInfo> GetAllCamps() const { return JungleCamps; }
    
    /** Obter camps por tipo */
    UFUNCTION(BlueprintPure, Category = "AURACRON|JungleSystem")
    TArray<FAURACRONJungleCampInfo> GetCampsByType(EAURACRONJungleCampType CampType) const;
    
    /** Obter camps por lado do mapa */
    UFUNCTION(BlueprintPure, Category = "AURACRON|JungleSystem")
    TArray<FAURACRONJungleCampInfo> GetCampsBySide(int32 MapSide) const;
    
    /** Limpar camp (quando morto) */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem")
    void ClearCamp(int32 CampIndex);
    
    /** Verificar se camp está disponível */
    UFUNCTION(BlueprintPure, Category = "AURACRON|JungleSystem")
    bool IsCampAvailable(int32 CampIndex) const;
    
    /** Atualizar para novo ambiente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem")
    void UpdateForEnvironment(EAURACRONEnvironmentType NewEnvironment);
    
    /** Atualizar para nova fase do mapa */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem")
    void UpdateForMapPhase(EAURACRONMapPhase MapPhase);
    
    // ========================================
    // FUNÇÕES PÚBLICAS - IA ADAPTATIVA
    // ========================================
    
    /** Obter dados adaptativos atuais */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem|Adaptive")
    FAURACRONJungleAdaptiveData GetAdaptiveData() const;
    
    /** Definir perfil do jogador manualmente */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem|Adaptive")
    void SetPlayerProfile(EAURACRONJunglePlayerProfile NewProfile);
    
    /** Registrar interação com camp */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem|Adaptive")
    void RegisterCampInteraction(int32 CampIndex, float ClearTime);
    
    /** Registrar invasão de jungle */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem|Adaptive")
    void RegisterJungleInvasion(int32 MapSide);
    
    /** Registrar objetivo tomado */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem|Adaptive")
    void RegisterObjectiveSecured();
    
    /** Adaptar comportamento dos camps */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem|Adaptive")
    void AdaptCampBehaviors();
    
    /** Obter multiplicador de dificuldade para camp */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem|Adaptive")
    float GetCampDifficultyMultiplier(int32 CampIndex) const;
    
    /** Obter multiplicador de recompensa para camp */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem|Adaptive")
    float GetCampRewardMultiplier(int32 CampIndex) const;
    
    /** Definir nível de dificuldade adaptativa global */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem|Adaptive")
    void SetAdaptiveDifficulty(float NewDifficulty);
    
    /** Obter comportamento recomendado para camp */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem|Adaptive")
    EAURACRONJungleAdaptiveBehavior GetRecommendedBehaviorForCamp(int32 CampIndex) const;
    
    /** Resetar dados adaptativos */
    UFUNCTION(BlueprintCallable, Category = "AURACRON|JungleSystem|Adaptive")
    void ResetAdaptiveData();

protected:
    // ========================================
    // CONFIGURAÇÕES BÁSICAS
    // ========================================
    
    /** Informações de todos os jungle camps */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|JungleSystem")
    TArray<FAURACRONJungleCampInfo> JungleCamps;
    
    /** Componentes visuais dos camps por ambiente */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|JungleSystem")
    TMap<EAURACRONEnvironmentType, FAURACRONMeshComponentArray> CampMeshesByEnvironment;
    
    /** Componentes de colisão dos camps */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "AURACRON|JungleSystem")
    TArray<USphereComponent*> CampCollisionComponents;
    
    /** Se deve gerar automaticamente no BeginPlay */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|JungleSystem")
    bool bAutoGenerate;
    
    /** Ambiente atualmente ativo */
    UPROPERTY(BlueprintReadOnly, Category = "AURACRON|JungleSystem")
    EAURACRONEnvironmentType CurrentEnvironment;
    
    // ========================================
    // CONFIGURAÇÕES DE IA ADAPTATIVA
    // ========================================
    
    /** Dados adaptativos da jungle */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|JungleSystem|Adaptive")
    FAURACRONJungleAdaptiveData AdaptiveData;
    
    /** Intervalo de adaptação em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|JungleSystem|Adaptive")
    float AdaptationInterval;
    
    /** Ativar IA adaptativa? */
     UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|JungleSystem|Adaptive")
     bool bEnableAdaptiveAI;
     
     /** Sensibilidade da adaptação (0.0-1.0) */
     UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|JungleSystem|Adaptive", meta = (ClampMin = "0.0", ClampMax = "1.0"))
     float AdaptationSensitivity;
     
     /** Curva de dificuldade adaptativa */
     UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|JungleSystem|Adaptive")
     UCurveFloat* AdaptiveDifficultyCurve;
     
     /** Configuração de streaming para World Partition */
     UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|JungleSystem|WorldPartition")
     FAURACRONWorldPartitionStreamingConfig StreamingConfiguration;
     
     /** Data Layer associada */
     UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AURACRON|JungleSystem|WorldPartition")
     FName AssociatedDataLayer;

private:
    // ========================================
    // ESTADO INTERNO
    // ========================================
    
    /** Timers de respawn dos camps */
    UPROPERTY()
    TArray<FTimerHandle> CampRespawnTimers;
    
    /** Fase atual do mapa */
    UPROPERTY()
    EAURACRONMapPhase CurrentMapPhase;

    // ========================================
    // FUNÇÕES INTERNAS - BÁSICAS
    // ========================================
    
    /** Inicializar informações dos camps */
    void InitializeCampInfos();
    
    /** Criar camp específico */
    void CreateCamp(int32 CampIndex, EAURACRONEnvironmentType Environment);
    
    /** Obter configuração padrão por tipo de camp */
    FAURACRONJungleCampInfo GetDefaultCampConfig(EAURACRONJungleCampType CampType);
    
    /** Calcular posição do camp para ambiente */
    FVector CalculateCampPosition(EAURACRONJungleCampType CampType, int32 MapSide, EAURACRONEnvironmentType Environment);
    
    /** Aplicar características do ambiente ao camp */
    void ApplyEnvironmentCharacteristics(int32 CampIndex, EAURACRONEnvironmentType Environment);
    
    /** Iniciar timer de respawn */
    void StartRespawnTimer(int32 CampIndex);
    
    /** Callback de respawn do camp */
    void OnCampRespawn(int32 CampIndex);
    
    /** Atualizar visibilidade dos camps */
    void UpdateCampVisibility();
    
    /** Limpar camps de um ambiente */
    void ClearCampsForEnvironment(EAURACRONEnvironmentType Environment);
    
    /** Validar posição do camp */
    bool IsValidCampPosition(const FVector& Position, float MinDistanceFromLanes = 1000.0f);
    
    /** Obter mesh do camp baseado no tipo e ambiente */
    UStaticMesh* GetCampMesh(EAURACRONJungleCampType CampType, EAURACRONEnvironmentType Environment);
    
    /** Aplicar efeitos da fase do mapa */
    void ApplyMapPhaseEffects();
    
    // ========================================
    // FUNÇÕES INTERNAS - IA ADAPTATIVA
    // ========================================
    
    /** Inicializar sistema de IA adaptativa */
    void InitializeAdaptiveSystem();
    
    /** Analisar padrão de jogo do jogador */
    void AnalyzePlayerPattern();
    
    /** Determinar perfil do jogador baseado em dados */
    EAURACRONJunglePlayerProfile DeterminePlayerProfile();
    
    /** Calcular comportamento adaptativo para camp */
    EAURACRONJungleAdaptiveBehavior CalculateAdaptiveBehavior(int32 CampIndex);
    
    /** Aplicar comportamento adaptativo ao camp */
    void ApplyAdaptiveBehavior(int32 CampIndex, EAURACRONJungleAdaptiveBehavior Behavior);
    
    /** Calcular multiplicadores adaptativos */
    void CalculateAdaptiveMultipliers(int32 CampIndex);
    
    /** Atualizar dados adaptativos */
    void UpdateAdaptiveData(float DeltaTime);
    
    /** Verificar necessidade de adaptação */
    bool ShouldAdapt() const;
    
    /** Registrar rota de limpeza de camps */
    void RegisterClearPath(int32 CampIndex);
    
    /** Calcular prioridade de adaptação para camp */
    float CalculateAdaptationPriority(int32 CampIndex) const;
    
    /** Prever estratégia baseada na composição da equipe */
    void PredictStrategy(const TArray<FString>& TeamComposition);
    
    /** Gerar objetivos dinâmicos baseados na estratégia prevista */
    void GenerateDynamicObjectives();
    
    /** Adaptar spawns baseado em padrões de clear */
    void AdaptSpawnsBasedOnClearPatterns();
    
    /** Prever estratégia baseada na composição da equipe */
    void PredictStrategy(const TArray<FString>& TeamComposition);
    
    /** Gerar objetivos dinâmicos baseados no comportamento do jogador */
    void GenerateDynamicObjectives();
    
    /** Adaptar spawns baseado em padrões de limpeza */
    void AdaptSpawnsBasedOnClearPatterns();
    
    /** Configurar streaming para World Partition */
    void ConfigureWorldPartitionStreaming();
    
    /** Associar com Data Layer */
    void AssociateWithDataLayer();
};
