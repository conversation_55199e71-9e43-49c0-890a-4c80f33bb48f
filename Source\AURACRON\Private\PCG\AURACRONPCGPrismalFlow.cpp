// AURACRONPCGPrismalFlow.cpp
// Sistema de Geração Procedural para AURACRON - UE 5.6
// Implementação do Prismal Flow serpentino

#include "PCG/AURACRONPCGPrismalFlow.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCG/AURACRONPCGPerformanceManager.h"
#include "PCGComponent.h"
#include "PCGSettings.h"
#include "Engine/World.h"
#include "Components/SplineComponent.h"
#include "Components/StaticMeshComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Kismet/GameplayStatics.h"
#include "Net/UnrealNetwork.h"
#include "EngineUtils.h"

AAURACRONPCGPrismalFlow::AAURACRONPCGPrismalFlow()
    : GlobalFlowIntensity(1.0f)
    , BaseFlowSpeed(1.0f)
    , NumControlPoints(20)
    , SerpentineAmplitude(1500.0f) // 15 metros
    , SerpentineFrequency(4.0f)
    , CurrentMapPhase(EAURACRONMapPhase::Awakening)
    , AccumulatedTime(0.0f)
{
    PrimaryActorTick.bCanEverTick = true;
    
    // Configurar replicação para multiplayer
    bReplicates = true;
    SetReplicateMovement(false); // Flow não se move
    
    // Criar o componente raiz (USceneComponent)
    USceneComponent* SceneRoot = CreateDefaultSubobject<USceneComponent>(TEXT("SceneRoot"));
    RootComponent = SceneRoot;

    // Criar componente PCG
    PCGComponent = CreateDefaultSubobject<UPCGComponent>(TEXT("PCGComponent"));
    
    // Criar spline do flow
    FlowSpline = CreateDefaultSubobject<USplineComponent>(TEXT("FlowSpline"));
    FlowSpline->SetupAttachment(RootComponent);
    FlowSpline->SetClosedLoop(false);
    FlowSpline->SetSplinePointType(0, ESplinePointType::CurveClamped);
    
    // Criar componente de efeito principal
    MainFlowEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("MainFlowEffect"));
    MainFlowEffect->SetupAttachment(RootComponent);

    // Criar componentes para compatibilidade
    FlowMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("FlowMesh"));
    FlowMesh->SetupAttachment(RootComponent);

    FlowEffect = MainFlowEffect; // Alias para o componente principal

    // Inicializar propriedades
    ActivityScale = 1.0f;
}

void AAURACRONPCGPrismalFlow::BeginPlay()
{
    Super::BeginPlay();
    
    // Gerar o flow quando o jogo começar (apenas no servidor)
    if (HasAuthority())
    {
        GeneratePrismalFlow();
    }
}

void AAURACRONPCGPrismalFlow::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (HasAuthority())
    {
        AccumulatedTime += DeltaTime;
        UpdateDynamicEffects(DeltaTime);
        UpdateTimeBasedParameters();
    }
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES PÚBLICAS
// ========================================

void AAURACRONPCGPrismalFlow::GeneratePrismalFlow()
{
    if (!HasAuthority())
    {
        return;
    }
    
    // Limpar elementos anteriores
    ClearGeneratedElements();
    
    // Gerar curva serpentina principal
    TArray<FVector> CurvePoints = GenerateSerpentineCurve();
    
    if (CurvePoints.Num() < 2)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGPrismalFlow: Falha ao gerar pontos da curva"));
        return;
    }
    
    // Configurar spline
    FlowSpline->ClearSplinePoints();
    for (int32 i = 0; i < CurvePoints.Num(); ++i)
    {
        FlowSpline->AddSplinePoint(CurvePoints[i], ESplineCoordinateSpace::World);
        FlowSpline->SetSplinePointType(i, ESplinePointType::CurveClamped);
    }
    
    FlowSpline->UpdateSpline();
    
    // Gerar segmentos do flow
    GenerateFlowSegments();
    
    // Gerar efeitos visuais
    GenerateVisualEffects();
    
    // Gerar ilhas estratégicas
    GenerateStrategicIslands();
    
    // Gerar obstáculos e características especiais
    GenerateFlowObstacles();
    
    // Criar transições entre ambientes
    CreateEnvironmentTransitions();
    
    // Aplicar efeitos da fase atual do mapa
    ApplyMapPhaseEffects();
}

void AAURACRONPCGPrismalFlow::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    if (CurrentMapPhase != MapPhase)
    {
        CurrentMapPhase = MapPhase;
        
        // Atualizar intensidade baseada na fase
        switch (MapPhase)
        {
        case EAURACRONMapPhase::Awakening:
            SetFlowIntensity(0.7f);
            break;
            
        case EAURACRONMapPhase::Convergence:
            SetFlowIntensity(1.0f);
            break;
            
        case EAURACRONMapPhase::Intensification:
            SetFlowIntensity(1.5f);
            break;
            
        case EAURACRONMapPhase::Resolution:
            SetFlowIntensity(2.0f); // Máxima volatilidade
            break;
        }
        
        // Aplicar efeitos específicos da fase
        ApplyMapPhaseEffects();
    }
}

void AAURACRONPCGPrismalFlow::SetFlowIntensity(float NewIntensity)
{
    GlobalFlowIntensity = FMath::Clamp(NewIntensity, 0.0f, 2.0f);
    
    // Atualizar todos os segmentos
    for (FPrismalFlowSegment& Segment : FlowSegments)
    {
        Segment.Intensity = GlobalFlowIntensity * CalculateFlowIntensity(0.0f, CurrentMapPhase);
    }
    
    // Atualizar efeito principal
    if (MainFlowEffect)
    {
        MainFlowEffect->SetFloatParameter(FName("GlobalIntensity"), GlobalFlowIntensity);
    }
}

FVector AAURACRONPCGPrismalFlow::GetFlowPositionAtT(float T) const
{
    if (!FlowSpline)
    {
        return FVector::ZeroVector;
    }
    
    T = FMath::Clamp(T, 0.0f, 1.0f);
    float SplineDistance = T * FlowSpline->GetSplineLength();
    
    return FlowSpline->GetLocationAtDistanceAlongSpline(SplineDistance, ESplineCoordinateSpace::World);
}

float AAURACRONPCGPrismalFlow::GetFlowWidthAtT(float T) const
{
    T = FMath::Clamp(T, 0.0f, 1.0f);
    
    // Usar a função matemática para calcular largura
    return UAURACRONMapMeasurements::GetPrismalFlowWidth(T);
}

bool AAURACRONPCGPrismalFlow::IsPositionInFlow(const FVector& Position, float Tolerance) const
{
    if (!FlowSpline || FlowSpline->GetNumberOfSplinePoints() < 2)
    {
        return false;
    }
    
    // Encontrar o ponto mais próximo na spline
    float ClosestInputKey = FlowSpline->FindInputKeyClosestToWorldLocation(Position);
    FVector ClosestPoint = FlowSpline->GetLocationAtSplineInputKey(ClosestInputKey, ESplineCoordinateSpace::World);
    
    float Distance = FVector::Dist(Position, ClosestPoint);
    
    // Calcular largura do flow neste ponto
    float T = ClosestInputKey / (FlowSpline->GetNumberOfSplinePoints() - 1);
    float FlowWidth = GetFlowWidthAtT(T);
    
    return Distance <= (FlowWidth * 0.5f + Tolerance);
}

// ========================================
// IMPLEMENTAÇÃO DAS FUNÇÕES INTERNAS
// ========================================

TArray<FVector> AAURACRONPCGPrismalFlow::GenerateSerpentineCurve()
{
    TArray<FVector> Points;
    
    // Pontos de início e fim baseados no mapa
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    float MapRadius = FAURACRONMapDimensions::MAP_RADIUS_CM;
    
    // Começar em uma borda do mapa
    FVector StartPoint = MapCenter + FVector(-MapRadius * 0.8f, -MapRadius * 0.6f, 0.0f);
    FVector EndPoint = MapCenter + FVector(MapRadius * 0.8f, MapRadius * 0.6f, 0.0f);
    
    // Usar a biblioteca matemática para criar curva serpentina
    FAURACRONSplineCurve SerpentineCurve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
        StartPoint,
        EndPoint,
        NumControlPoints,
        SerpentineAmplitude,
        SerpentineFrequency
    );
    
    // Converter para array de pontos
    for (int32 i = 0; i <= NumControlPoints; ++i)
    {
        float T = static_cast<float>(i) / NumControlPoints;
        FVector Point = UAURACRONPCGMathLibrary::EvaluateSplineCurve(SerpentineCurve, T);
        
        // Ajustar altura baseada na posição para passar pelos três ambientes
        EAURACRONEnvironmentType EnvType = DetermineEnvironmentType(Point);
        FVector EnvCenter = UAURACRONMapMeasurements::GetEnvironmentCenter(static_cast<int32>(EnvType));
        Point.Z = EnvCenter.Z + FMath::Sin(T * PI) * 200.0f; // Variação suave de altura
        
        Points.Add(Point);
    }
    
    return Points;
}

float AAURACRONPCGPrismalFlow::CalculateFlowWidth(float T, EAURACRONEnvironmentType EnvironmentType)
{
    // Largura base do flow
    float BaseWidth = UAURACRONMapMeasurements::GetPrismalFlowWidth(T);
    
    // Modificadores baseados no ambiente
    float EnvironmentModifier = 1.0f;
    switch (EnvironmentType)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        EnvironmentModifier = 1.0f; // Largura normal
        break;
        
    case EAURACRONEnvironmentType::ZephyrFirmament:
        EnvironmentModifier = 0.8f; // Mais estreito no ar
        break;
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        EnvironmentModifier = 1.2f; // Mais largo no reino espectral
        break;
    }
    
    // Modificador baseado na fase do mapa
    float PhaseModifier = CalculateFlowVolatility(CurrentMapPhase);
    
    return BaseWidth * EnvironmentModifier * PhaseModifier;
}

float AAURACRONPCGPrismalFlow::CalculateFlowIntensity(float T, EAURACRONMapPhase MapPhase)
{
    // Intensidade base
    float BaseIntensity = 1.0f;
    
    // Variação ao longo do flow
    float PositionVariation = 0.8f + 0.4f * FMath::Sin(T * 6.0f * PI);
    
    // Modificador baseado na fase
    float PhaseModifier = 1.0f;
    switch (MapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        PhaseModifier = 0.7f;
        break;
        
    case EAURACRONMapPhase::Convergence:
        PhaseModifier = 1.0f;
        break;
        
    case EAURACRONMapPhase::Intensification:
        PhaseModifier = 1.3f;
        break;
        
    case EAURACRONMapPhase::Resolution:
        PhaseModifier = 1.8f;
        break;
    }
    
    return BaseIntensity * PositionVariation * PhaseModifier;
}

void AAURACRONPCGPrismalFlow::GenerateFlowSegments()
{
    FlowSegments.Empty();

    if (!FlowSpline || FlowSpline->GetNumberOfSplinePoints() < 2)
    {
        return;
    }

    int32 NumSegments = NumControlPoints * 2; // Mais segmentos para suavidade

    for (int32 i = 0; i < NumSegments; ++i)
    {
        float T = static_cast<float>(i) / (NumSegments - 1);

        FPrismalFlowSegment Segment;
        Segment.Position = GetFlowPositionAtT(T);
        Segment.EnvironmentType = DetermineEnvironmentType(Segment.Position);
        Segment.Width = CalculateFlowWidth(T, Segment.EnvironmentType);
        Segment.Intensity = CalculateFlowIntensity(T, CurrentMapPhase);
        Segment.FlowSpeed = BaseFlowSpeed * (0.8f + 0.4f * FMath::Sin(T * 4.0f * PI));

        // Aplicar efeitos específicos do ambiente
        ApplyEnvironmentEffects(Segment);

        FlowSegments.Add(Segment);
    }
}

void AAURACRONPCGPrismalFlow::GenerateVisualEffects()
{
    if (!MainFlowEffect)
    {
        return;
    }

    // Configurar efeito principal
    MainFlowEffect->SetFloatParameter(FName("GlobalIntensity"), GlobalFlowIntensity);
    MainFlowEffect->SetFloatParameter(FName("FlowSpeed"), BaseFlowSpeed);
    MainFlowEffect->SetFloatParameter(FName("FlowLength"), FlowSpline->GetSplineLength());

    // Criar efeitos adicionais ao longo do flow
    int32 EffectCount = FlowSegments.Num() / 4; // Um efeito a cada 4 segmentos

    for (int32 i = 0; i < EffectCount; ++i)
    {
        int32 SegmentIndex = i * 4;
        if (SegmentIndex < FlowSegments.Num())
        {
            const FPrismalFlowSegment& Segment = FlowSegments[SegmentIndex];

            // Criar componente de efeito
            UNiagaraComponent* SegmentEffect = NewObject<UNiagaraComponent>(this);
            if (SegmentEffect)
            {
                SegmentEffect->AttachToComponent(RootComponent,
                    FAttachmentTransformRules::KeepWorldTransform);
                SegmentEffect->SetWorldLocation(Segment.Position);

                // Configurar parâmetros baseados no segmento
                SegmentEffect->SetFloatParameter(FName("Intensity"), Segment.Intensity);
                SegmentEffect->SetFloatParameter(FName("Width"), Segment.Width);
                SegmentEffect->SetFloatParameter(FName("FlowSpeed"), Segment.FlowSpeed);

                // Configurar cor baseada no ambiente
                FLinearColor EnvironmentColor = GetEnvironmentColor(Segment.EnvironmentType);
                SegmentEffect->SetColorParameter(FName("FlowColor"), EnvironmentColor);

                GeneratedComponents.Add(SegmentEffect);
            }
        }
    }
}

float AAURACRONPCGPrismalFlow::GetHardwareVolatilityMultiplier() const
{
    // Encontrar o Performance Manager no mundo
    AAURACRONPCGPerformanceManager* PerformanceManager = nullptr;
    
    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAURACRONPCGPerformanceManager> It(World); It; ++It)
        {
            PerformanceManager = *It;
            break;
        }
    }
    
    if (!PerformanceManager)
    {
        // Retornar multiplicador padrão se não encontrar o Performance Manager
        return 1.0f;
    }
    
    // Obter o perfil de dispositivo atual
    EAURACRONDeviceType DeviceType = PerformanceManager->GetCurrentDeviceType();
    FAURACRONDevicePerformanceProfile DeviceProfile = PerformanceManager->GetDeviceProfile(DeviceType);
    
    return DeviceProfile.PrismalFlowVolatilityMultiplier;
}

void AAURACRONPCGPrismalFlow::UpdateVolatilityForHardware()
{
    // Recalcular volatilidade com base no hardware atual
    float NewVolatility = CalculateFlowVolatility(CurrentMapPhase);
    
    // Atualizar todos os segmentos do flow
    for (FPrismalFlowSegment& Segment : FlowSegments)
    {
        Segment.Volatility = NewVolatility;
    }
    
    // Atualizar efeito principal
    if (MainFlowEffect)
    {
        MainFlowEffect->SetFloatParameter(FName("FlowVolatility"), NewVolatility);
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::UpdateVolatilityForHardware - Updated volatility to %.2f"), NewVolatility);
}

void AAURACRONPCGPrismalFlow::GenerateStrategicIslands()
{
    // Usar a função matemática para gerar posições das ilhas
    TArray<FVector> FlowPoints;
    for (const FPrismalFlowSegment& Segment : FlowSegments)
    {
        FlowPoints.Add(Segment.Position);
    }

    // Gerar diferentes tipos de ilhas
    TArray<EAURACRONIslandType> IslandTypes = {
        EAURACRONIslandType::Nexus,
        EAURACRONIslandType::Sanctuary,
        EAURACRONIslandType::Arsenal,
        EAURACRONIslandType::Chaos
    };

    for (EAURACRONIslandType IslandType : IslandTypes)
    {
        TArray<FVector> IslandPositions = UAURACRONMapMeasurements::GetIslandPositions(
            static_cast<int32>(IslandType),
            FlowPoints
        );

        for (const FVector& IslandPos : IslandPositions)
        {
            // Criar componente de ilha
            UStaticMeshComponent* Island = NewObject<UStaticMeshComponent>(this);
            if (Island)
            {
                Island->AttachToComponent(RootComponent,
                    FAttachmentTransformRules::KeepWorldTransform);
                Island->SetWorldLocation(IslandPos);

                // Configurar escala baseada no tipo de ilha
                float IslandRadius = GetIslandRadius(IslandType);
                Island->SetWorldScale3D(FVector(IslandRadius / 100.0f)); // Normalizar para escala

                GeneratedComponents.Add(Island);
            }
        }
    }
}

void AAURACRONPCGPrismalFlow::ClearGeneratedElements()
{
    // Limpar componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (IsValid(Component))
        {
            Component->DestroyComponent();
        }
    }

    GeneratedComponents.Empty();
    FlowSegments.Empty();
}

// Implementação do método SetIslandType para APrismalFlowIsland
void APrismalFlowIsland::SetIslandType(EPrismalFlowIslandType NewType)
{
    // Atualizar o tipo de ilha
    IslandType = NewType;
    
    // Atualizar visuais baseados no novo tipo
    UpdateIslandVisuals();
}

// Implementação do método SetFlowPosition para APrismalFlowIsland
void APrismalFlowIsland::SetFlowPosition(float InFlowPosition)
{
    // Atualizar a posição no flow (0-1)
    FlowPosition = FMath::Clamp(InFlowPosition, 0.0f, 1.0f);
}

// Implementação do método SetIslandActive para APrismalFlowIsland
void APrismalFlowIsland::SetIslandActive(bool bActive)
{
    // Atualizar o estado de ativação da ilha
    bIsActive = bActive;
    
    // Ativar/desativar efeitos visuais
    if (IslandEffect)
    {
        IslandEffect->SetActive(bActive);
    }
    
    // Ativar/desativar área de interação
    if (InteractionArea)
    {
        InteractionArea->SetCollisionEnabled(bActive ? ECollisionEnabled::QueryOnly : ECollisionEnabled::NoCollision);
    }
}

// Implementação do construtor APrismalFlowIsland
APrismalFlowIsland::APrismalFlowIsland()
{
    // Configuração padrão
    PrimaryActorTick.bCanEverTick = true;
    
    // Inicializar propriedades
    IslandType = EPrismalFlowIslandType::None;
    FlowPosition = 0.0f;
    bIsActive = false;
    
    // Criar componentes
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));
    
    // Malha da ilha
    IslandMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("IslandMesh"));
    IslandMesh->SetupAttachment(RootComponent);
    IslandMesh->SetCollisionProfileName(TEXT("BlockAll"));
    
    // Efeito visual da ilha
    IslandEffect = CreateDefaultSubobject<UNiagaraComponent>(TEXT("IslandEffect"));
    IslandEffect->SetupAttachment(RootComponent);
    IslandEffect->SetAutoActivate(false);
    
    // Área de interação
    InteractionArea = CreateDefaultSubobject<USphereComponent>(TEXT("InteractionArea"));
    InteractionArea->SetupAttachment(RootComponent);
    InteractionArea->SetSphereRadius(500.0f);
    InteractionArea->SetCollisionProfileName(TEXT("Trigger"));
    InteractionArea->SetCollisionEnabled(ECollisionEnabled::NoCollision);
}

// Implementação do método UpdateIslandVisuals para APrismalFlowIsland
void APrismalFlowIsland::UpdateIslandVisuals()
{
    // Atualizar visuais baseados no tipo de ilha
    if (IslandMesh)
    {
        // Criar material dinâmico se ainda não existir
        if (!IslandMaterial && IslandMesh->GetMaterial(0))
        {
            IslandMaterial = IslandMesh->CreateAndSetMaterialInstanceDynamic(0);
        }
        
        // Atualizar parâmetros do material baseados no tipo de ilha
        if (IslandMaterial)
        {
            FLinearColor IslandColor;
            
            // Definir cor baseada no tipo de ilha
            switch (IslandType)
            {
            case EPrismalFlowIslandType::Nexus:
                IslandColor = FLinearColor(0.0f, 0.8f, 1.0f); // Azul brilhante
                break;
                
            case EPrismalFlowIslandType::Sanctuary:
                IslandColor = FLinearColor(0.0f, 1.0f, 0.5f); // Verde cura
                break;
                
            case EPrismalFlowIslandType::Arsenal:
                IslandColor = FLinearColor(1.0f, 0.5f, 0.0f); // Laranja poder
                break;
                
            case EPrismalFlowIslandType::Chaos:
                IslandColor = FLinearColor(1.0f, 0.0f, 0.5f); // Roxo caos
                break;
                
            case EPrismalFlowIslandType::Battlefield:
                IslandColor = FLinearColor(1.0f, 0.2f, 0.2f); // Vermelho batalha
                break;
                
            case EPrismalFlowIslandType::Amplifier:
                IslandColor = FLinearColor(0.5f, 0.0f, 1.0f); // Roxo amplificador
                break;
                
            case EPrismalFlowIslandType::Gateway:
                IslandColor = FLinearColor(1.0f, 1.0f, 0.0f); // Amarelo portal
                break;
                
            case EPrismalFlowIslandType::Corrupted:
                IslandColor = FLinearColor(0.2f, 0.0f, 0.3f); // Roxo escuro corrompido
                break;
                
            default:
                IslandColor = FLinearColor(0.5f, 0.5f, 0.5f); // Cinza neutro
                break;
            }
            
            // Aplicar cor ao material
            IslandMaterial->SetVectorParameterValue(FName("IslandColor"), IslandColor);
        }
    }
    
    // Atualizar efeito visual baseado no tipo de ilha
    if (IslandEffect)
    {
        // Configurar parâmetros do efeito baseados no tipo de ilha
        // Implementação específica para cada tipo de ilha
    }
}

// Implementação do método BeginPlay para APrismalFlowIsland
void APrismalFlowIsland::BeginPlay()
{
    Super::BeginPlay();
    
    // Configurar eventos de colisão para a área de interação
    if (InteractionArea)
    {
        InteractionArea->OnComponentBeginOverlap.AddDynamic(this, &APrismalFlowIsland::ApplyIslandEffect);
    }
    
    // Atualizar visuais iniciais
    UpdateIslandVisuals();
}

// Implementação do método Tick para APrismalFlowIsland
void APrismalFlowIsland::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Implementar flutuação suave da ilha
    if (IslandMesh && bIsActive)
    {
        // Calcular movimento de flutuação baseado no tempo
        float Time = GetGameTimeSinceCreation();
        float FloatOffset = FMath::Sin(Time * 0.5f) * 10.0f; // 10 unidades de flutuação
        
        // Aplicar movimento de flutuação
        FVector CurrentLocation = IslandMesh->GetRelativeLocation();
        CurrentLocation.Z = FloatOffset;
        IslandMesh->SetRelativeLocation(CurrentLocation);
        
        // Atualizar efeitos visuais se necessário
        if (IslandEffect)
        {
            IslandEffect->SetFloatParameter(FName("Time"), Time);
        }
    }
}

// Implementação do método ApplyIslandEffect para APrismalFlowIsland
void APrismalFlowIsland::ApplyIslandEffect(AActor* OverlappingActor)
{
    // Verificar se a ilha está ativa
    if (!bIsActive || !OverlappingActor)
    {
        return;
    }
    
    // Implementação base que será sobrescrita por classes derivadas
    // Cada tipo de ilha implementará seus próprios efeitos específicos
    
    // Exemplo de efeito visual de feedback
    if (IslandEffect)
    {
        IslandEffect->SetFloatParameter(FName("EffectIntensity"), 2.0f); // Intensificar efeito
        
        // Retornar à intensidade normal após um curto período
        FTimerHandle TimerHandle;
        GetWorldTimerManager().SetTimer(TimerHandle, [this]()
        {
            if (IslandEffect)
            {
                IslandEffect->SetFloatParameter(FName("EffectIntensity"), 1.0f);
            }
        }, 0.5f, false);
    }
}

void AAURACRONPCGPrismalFlow::UpdateDynamicEffects(float DeltaTime)
{
    // Atualizar efeitos baseados no tempo
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
        {
            // Atualizar parâmetros temporais
            NiagaraComp->SetFloatParameter(FName("Time"), AccumulatedTime);

            // Adicionar variação temporal à intensidade
            float TimeVariation = 1.0f + 0.1f * FMath::Sin(AccumulatedTime * 2.0f);
            float CurrentIntensity = GlobalFlowIntensity * TimeVariation;
            NiagaraComp->SetFloatParameter(FName("DynamicIntensity"), CurrentIntensity);
        }
    }
}

EAURACRONEnvironmentType AAURACRONPCGPrismalFlow::DetermineEnvironmentType(const FVector& Position)
{
    // Determinar ambiente baseado na altura Z
    if (Position.Z > 500.0f)
    {
        return EAURACRONEnvironmentType::ZephyrFirmament;
    }
    else if (Position.Z < -200.0f)
    {
        return EAURACRONEnvironmentType::PurgatoryRealm;
    }
    else
    {
        return EAURACRONEnvironmentType::RadiantPlains;
    }
}

void AAURACRONPCGPrismalFlow::ApplyEnvironmentEffects(FPrismalFlowSegment& Segment)
{
    switch (Segment.EnvironmentType)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        // Efeitos terrestres - flow mais estável
        Segment.Intensity *= 1.0f;
        Segment.FlowSpeed *= 1.0f;
        break;

    case EAURACRONEnvironmentType::ZephyrFirmament:
        // Efeitos celestiais - flow mais rápido e brilhante
        Segment.Intensity *= 1.2f;
        Segment.FlowSpeed *= 1.3f;
        break;

    case EAURACRONEnvironmentType::PurgatoryRealm:
        // Efeitos espectrais - flow mais volátil
        Segment.Intensity *= 0.8f;
        Segment.FlowSpeed *= 0.9f;
        break;
    }
}

float AAURACRONPCGPrismalFlow::CalculateFlowVolatility(EAURACRONMapPhase MapPhase)
{
    // Calcular volatilidade base baseada na fase do mapa
    float BaseVolatility = 1.0f;
    switch (MapPhase)
    {
    case EAURACRONMapPhase::Awakening:
        BaseVolatility = 1.0f; // Estável
        break;

    case EAURACRONMapPhase::Convergence:
        BaseVolatility = 1.1f; // Ligeiramente instável
        break;

    case EAURACRONMapPhase::Intensification:
        BaseVolatility = 1.3f; // Moderadamente volátil
        break;

    case EAURACRONMapPhase::Resolution:
        BaseVolatility = 1.6f; // Altamente volátil
        break;

    default:
        BaseVolatility = 1.0f;
        break;
    }

    // Aplicar multiplicador baseado na capacidade de hardware
    float HardwareMultiplier = GetHardwareVolatilityMultiplier();
    
    return BaseVolatility * HardwareMultiplier;
}

FLinearColor AAURACRONPCGPrismalFlow::GetEnvironmentColor(EAURACRONEnvironmentType EnvironmentType)
{
    switch (EnvironmentType)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        return FLinearColor(0.2f, 0.8f, 0.3f, 1.0f); // Verde cristalino

    case EAURACRONEnvironmentType::ZephyrFirmament:
        return FLinearColor(0.3f, 0.6f, 1.0f, 1.0f); // Azul celestial

    case EAURACRONEnvironmentType::PurgatoryRealm:
        return FLinearColor(0.8f, 0.2f, 0.8f, 1.0f); // Roxo espectral

    default:
        return FLinearColor::White;
    }
}

float AAURACRONPCGPrismalFlow::GetIslandRadius(EAURACRONIslandType IslandType)
{
    switch (IslandType)
    {
    case EAURACRONIslandType::Nexus:
        return FAURACRONMapDimensions::NEXUS_ISLAND_RADIUS_CM;

    case EAURACRONIslandType::Sanctuary:
        return FAURACRONMapDimensions::SANCTUARY_ISLAND_RADIUS_CM;

    case EAURACRONIslandType::Arsenal:
        return FAURACRONMapDimensions::ARSENAL_ISLAND_RADIUS_CM;

    case EAURACRONIslandType::Chaos:
        return FAURACRONMapDimensions::CHAOS_ISLAND_RADIUS_CM;

    default:
        return 100.0f;
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGPrismalFlow::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar propriedades usando APIs modernas do UE 5.6
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, GlobalFlowIntensity);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, BaseFlowSpeed);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, NumControlPoints);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, SerpentineAmplitude);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, SerpentineFrequency);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, CurrentMapPhase);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, ControllingTeam);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, FlowSpline);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, FlowMesh);
    DOREPLIFETIME(AAURACRONPCGPrismalFlow, FlowEffect);
}

void AAURACRONPCGPrismalFlow::OnFlowActivated()
{
    // Callback quando Prismal Flow é ativado usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::OnFlowActivated - Prismal Flow %s activated"), *GetName());

    // Ativar componentes do flow
    if (PCGComponent && IsValid(PCGComponent))
    {
        PCGComponent->SetComponentTickEnabled(true);

        // Regenerar conteúdo PCG se necessário
        if (GlobalFlowIntensity > 0.0f)
        {
            PCGComponent->GenerateLocal(false); // Regeneração não-bloqueante
        }
    }

    // Ativar spline do flow
    if (FlowSpline && IsValid(FlowSpline))
    {
        FlowSpline->SetVisibility(true);
        FlowSpline->SetComponentTickEnabled(true);
    }

    // Ativar mesh do flow
    if (FlowMesh && IsValid(FlowMesh))
    {
        FlowMesh->SetVisibility(true);
        FlowMesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    }

    // Ativar efeitos visuais
    if (FlowEffect && IsValid(FlowEffect))
    {
        FlowEffect->SetVisibility(true);
        FlowEffect->Activate();

        // Configurar parâmetros do efeito baseado na intensidade
        FlowEffect->SetFloatParameter(FName("FlowIntensity"), GlobalFlowIntensity);
        FlowEffect->SetFloatParameter(FName("FlowSpeed"), BaseFlowSpeed);

        // Configurar cor baseada na fase do mapa
        FLinearColor FlowColor = GetFlowColorForPhase(CurrentMapPhase);
        FlowEffect->SetColorParameter(FName("FlowColor"), FlowColor);
    }

    // Ativar componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (Component && IsValid(Component))
        {
            Component->SetComponentTickEnabled(true);

            if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
            {
                MeshComp->SetVisibility(true);
            }
            else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
            {
                NiagaraComp->SetVisibility(true);
                NiagaraComp->Activate();
            }
        }
    }

    // Ativar tick do ator
    SetActorTickEnabled(true);
    SetActorHiddenInGame(false);

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::OnFlowActivated - Prismal Flow activation completed"));
}

void AAURACRONPCGPrismalFlow::OnFlowDeactivated()
{
    // Callback quando Prismal Flow é desativado usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::OnFlowDeactivated - Prismal Flow %s deactivated"), *GetName());

    // Desativar componentes do flow
    if (PCGComponent && IsValid(PCGComponent))
    {
        PCGComponent->SetComponentTickEnabled(false);
    }

    // Desativar spline do flow
    if (FlowSpline && IsValid(FlowSpline))
    {
        FlowSpline->SetVisibility(false);
        FlowSpline->SetComponentTickEnabled(false);
    }

    // Desativar mesh do flow
    if (FlowMesh && IsValid(FlowMesh))
    {
        FlowMesh->SetVisibility(false);
        FlowMesh->SetCollisionEnabled(ECollisionEnabled::NoCollision);
    }

    // Desativar efeitos visuais
    if (FlowEffect && IsValid(FlowEffect))
    {
        FlowEffect->SetVisibility(false);
        FlowEffect->Deactivate();
    }

    // Desativar componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (Component && IsValid(Component))
        {
            Component->SetComponentTickEnabled(false);

            if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
            {
                MeshComp->SetVisibility(false);
            }
            else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
            {
                NiagaraComp->SetVisibility(false);
                NiagaraComp->Deactivate();
            }
        }
    }

    // Desativar tick do ator
    SetActorTickEnabled(false);
    SetActorHiddenInGame(true);

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::OnFlowDeactivated - Prismal Flow deactivation completed"));
}

void AAURACRONPCGPrismalFlow::UpdateFlowPhase(EAURACRONMapPhase NewPhase)
{
    // Atualizar Prismal Flow para nova fase do mapa usando APIs modernas do UE 5.6
    if (CurrentMapPhase == NewPhase)
    {
        return; // Já está na fase correta
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::UpdateFlowPhase - Updating flow %s from phase %d to phase %d"),
           *GetName(), (int32)CurrentMapPhase, (int32)NewPhase);

    EAURACRONMapPhase PreviousPhase = CurrentMapPhase;
    CurrentMapPhase = NewPhase;

    // Aplicar mudanças baseadas na nova fase
    switch (NewPhase)
    {
        case EAURACRONMapPhase::Awakening:
        {
            // Fase inicial: flow suave e calmo
            GlobalFlowIntensity = 0.6f;
            BaseFlowSpeed = 0.8f;
            SerpentineAmplitude = 1200.0f; // Menos serpentino
            SerpentineFrequency = 3.0f;
            break;
        }

        case EAURACRONMapPhase::Convergence:
        {
            // Fase de convergência: flow mais intenso
            GlobalFlowIntensity = 0.8f;
            BaseFlowSpeed = 1.0f;
            SerpentineAmplitude = 1500.0f; // Amplitude normal
            SerpentineFrequency = 4.0f;
            break;
        }

        case EAURACRONMapPhase::Intensification:
        {
            // Fase de intensificação: flow muito ativo
            GlobalFlowIntensity = 1.0f;
            BaseFlowSpeed = 1.3f;
            SerpentineAmplitude = 1800.0f; // Mais serpentino
            SerpentineFrequency = 5.0f;
            break;
        }

        case EAURACRONMapPhase::Resolution:
        {
            // Fase final: flow caótico e poderoso
            GlobalFlowIntensity = 1.5f; // Além do normal
            BaseFlowSpeed = 1.8f;
            SerpentineAmplitude = 2200.0f; // Muito serpentino
            SerpentineFrequency = 6.0f;
            break;
        }
    }

    // Atualizar efeitos visuais baseados na nova fase
    if (FlowEffect && IsValid(FlowEffect))
    {
        FlowEffect->SetFloatParameter(FName("FlowIntensity"), GlobalFlowIntensity);
        FlowEffect->SetFloatParameter(FName("FlowSpeed"), BaseFlowSpeed);
        FlowEffect->SetFloatParameter(FName("SerpentineAmplitude"), SerpentineAmplitude);
        FlowEffect->SetFloatParameter(FName("SerpentineFrequency"), SerpentineFrequency);

        // Configurar cor baseada na fase
        FLinearColor FlowColor = GetFlowColorForPhase(NewPhase);
        FlowEffect->SetColorParameter(FName("FlowColor"), FlowColor);
    }

    // Atualizar componentes gerados baseados na nova fase
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
        {
            NiagaraComp->SetFloatParameter(FName("PhaseIntensity"), GlobalFlowIntensity);
            NiagaraComp->SetFloatParameter(FName("PhaseSpeed"), BaseFlowSpeed);

            FLinearColor PhaseColor = GetFlowColorForPhase(NewPhase);
            NiagaraComp->SetColorParameter(FName("PhaseColor"), PhaseColor);
        }
    }

    // Regenerar spline se necessário
    if (FlowSpline && IsValid(FlowSpline))
    {
        // Regenerar pontos da spline com nova amplitude e frequência
        GenerateFlowPath();
    }

    // Regenerar conteúdo PCG se necessário
    if (PCGComponent && IsValid(PCGComponent) && GlobalFlowIntensity > 0.0f)
    {
        PCGComponent->GenerateLocal(false); // Regeneração não-bloqueante
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::UpdateFlowPhase - Flow phase update completed"));
}

FLinearColor AAURACRONPCGPrismalFlow::GetFlowColorForPhase(EAURACRONMapPhase Phase) const
{
    // Se houver uma equipe controladora, usar a cor da equipe em vez da cor da fase
    if (ControllingTeam > 0)
    {
        return GetFlowColorForTeam();
    }
    
    // Obter cor do flow baseada na fase do mapa usando paleta moderna
    switch (Phase)
    {
        case EAURACRONMapPhase::Awakening:
            return FLinearColor(0.8f, 0.9f, 1.0f, 1.0f); // Azul suave (despertar)

        case EAURACRONMapPhase::Convergence:
            return FLinearColor(1.0f, 1.0f, 0.9f, 1.0f); // Branco dourado (convergência)

        case EAURACRONMapPhase::Intensification:
            return FLinearColor(1.0f, 0.7f, 0.4f, 1.0f); // Laranja intenso (intensificação)

        case EAURACRONMapPhase::Resolution:
            return FLinearColor(0.9f, 0.3f, 1.0f, 1.0f); // Roxo místico (resolução)

        default:
            return FLinearColor::White;
    }
}

void AAURACRONPCGPrismalFlow::GenerateFlowPath()
{
    // Gerar caminho do flow usando APIs modernas do UE 5.6
    if (!FlowSpline || !IsValid(FlowSpline))
    {
        return;
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGPrismalFlow::GenerateFlowPath - Generating flow path"));

    // Limpar pontos existentes
    FlowSpline->ClearSplinePoints();

    // Gerar pontos baseados na configuração atual
    for (int32 i = 0; i < NumControlPoints; ++i)
    {
        float Alpha = static_cast<float>(i) / static_cast<float>(NumControlPoints - 1);

        // Posição base ao longo do eixo X
        float X = Alpha * 10000.0f; // 100 metros de comprimento

        // Aplicar curva serpentina
        float Y = FMath::Sin(Alpha * SerpentineFrequency * PI) * SerpentineAmplitude;

        // Variação de altura baseada na intensidade
        float Z = FMath::Sin(Alpha * 2.0f * PI) * (GlobalFlowIntensity * 200.0f);

        FVector SplinePoint(X, Y, Z);
        FlowSpline->AddSplinePoint(SplinePoint, ESplineCoordinateSpace::Local);
    }

    // Atualizar spline
    FlowSpline->UpdateSpline();

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGPrismalFlow::GenerateFlowPath - Generated %d spline points"), NumControlPoints);
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGPrismalFlow::CreateEnvironmentTransitions()
{
    // Criar transições entre ambientes usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::CreateEnvironmentTransitions - Creating environment transitions with modern UE 5.6 APIs"));

    if (!FlowSpline || !IsValid(FlowSpline))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::CreateEnvironmentTransitions - FlowSpline is invalid"));
        return;
    }

    int32 NumSplinePoints = FlowSpline->GetNumberOfSplinePoints();
    if (NumSplinePoints < 3)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::CreateEnvironmentTransitions - Not enough spline points"));
        return;
    }

    // Criar transições em pontos estratégicos ao longo do spline
    int32 NumTransitions = FMath::Clamp(NumSplinePoints / 4, 3, 8);

    for (int32 i = 0; i < NumTransitions; ++i)
    {
        float Alpha = static_cast<float>(i) / static_cast<float>(NumTransitions - 1);
        float SplineDistance = FlowSpline->GetSplineLength() * Alpha;

        // Obter localização e rotação no spline
        FVector TransitionLocation = FlowSpline->GetLocationAtDistanceAlongSpline(SplineDistance, ESplineCoordinateSpace::World);
        FRotator TransitionRotation = FlowSpline->GetRotationAtDistanceAlongSpline(SplineDistance, ESplineCoordinateSpace::World);

        // Criar componente de transição ambiental
        UStaticMeshComponent* EnvironmentTransition = CreateDefaultSubobject<UStaticMeshComponent>(
            *FString::Printf(TEXT("EnvironmentTransition_%d_%d"), i, FMath::RandRange(1000, 9999))
        );

        if (EnvironmentTransition)
        {
            EnvironmentTransition->SetupAttachment(RootComponent);
            EnvironmentTransition->SetWorldLocation(TransitionLocation);
            EnvironmentTransition->SetWorldRotation(TransitionRotation);

            // Escala baseada na intensidade do flow
            float TransitionScale = 1.5f + (GlobalFlowIntensity * 0.8f);
            EnvironmentTransition->SetRelativeScale3D(FVector(TransitionScale, TransitionScale, TransitionScale * 0.5f));

            // Configurar material de transição usando APIs modernas
            if (UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial")))
            {
                UMaterialInstanceDynamic* TransitionMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
                if (TransitionMaterial)
                {
                    // Configurar parâmetros de transição baseados na posição
                    float TransitionIntensity = 1.0f + (Alpha * 1.5f);
                    FLinearColor TransitionColor = FMath::Lerp(
                        FLinearColor(0.2f, 0.8f, 1.0f, 0.8f), // Azul inicial
                        FLinearColor(1.0f, 0.4f, 0.8f, 0.8f), // Rosa final
                        Alpha
                    );

                    TransitionMaterial->SetScalarParameterValue(FName("TransitionIntensity"), TransitionIntensity);
                    TransitionMaterial->SetScalarParameterValue(FName("FlowSpeed"), BaseFlowSpeed * 1.5f);
                    TransitionMaterial->SetVectorParameterValue(FName("TransitionColor"), TransitionColor);
                    TransitionMaterial->SetScalarParameterValue(FName("EnvironmentBlend"), Alpha);

                    EnvironmentTransition->SetMaterial(0, TransitionMaterial);
                }
            }

            // Configurar colisão para interação ambiental
            EnvironmentTransition->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            EnvironmentTransition->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);

            GeneratedComponents.Add(EnvironmentTransition);
        }
    }

    // Criar efeitos de partículas para transições usando APIs modernas
    for (int32 i = 0; i < NumTransitions; ++i)
    {
        float Alpha = static_cast<float>(i) / static_cast<float>(NumTransitions - 1);
        float SplineDistance = FlowSpline->GetSplineLength() * Alpha;
        FVector ParticleLocation = FlowSpline->GetLocationAtDistanceAlongSpline(SplineDistance, ESplineCoordinateSpace::World);

        // Adicionar offset vertical para efeitos de partículas
        ParticleLocation.Z += 100.0f;

        UNiagaraComponent* TransitionEffect = CreateDefaultSubobject<UNiagaraComponent>(
            *FString::Printf(TEXT("TransitionEffect_%d_%d"), i, FMath::RandRange(1000, 9999))
        );

        if (TransitionEffect)
        {
            TransitionEffect->SetupAttachment(RootComponent);
            TransitionEffect->SetWorldLocation(ParticleLocation);

            // Configurar parâmetros do efeito de transição
            TransitionEffect->SetFloatParameter(FName("TransitionIntensity"), GlobalFlowIntensity * 1.2f);
            TransitionEffect->SetFloatParameter(FName("FlowSpeed"), BaseFlowSpeed);
            TransitionEffect->SetVectorParameter(FName("FlowDirection"), FlowSpline->GetDirectionAtDistanceAlongSpline(SplineDistance, ESplineCoordinateSpace::World));

            // Configurar cor baseada na fase do mapa
            FLinearColor EffectColor = GetFlowColorForPhase(CurrentMapPhase);
            TransitionEffect->SetColorParameter(FName("EffectColor"), EffectColor);

            GeneratedComponents.Add(TransitionEffect);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::CreateEnvironmentTransitions - Created %d environment transitions"), NumTransitions);
}

void AAURACRONPCGPrismalFlow::UpdateTimeBasedParameters()
{
    // Atualizar parâmetros baseados no tempo usando APIs modernas do UE 5.6
    if (!GetWorld())
    {
        return;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();
    AccumulatedTime += GetWorld()->GetDeltaSeconds();

    // Atualizar parâmetros de flow baseados no tempo
    float TimeBasedIntensity = GlobalFlowIntensity * (1.0f + FMath::Sin(AccumulatedTime * 0.5f) * 0.3f);
    float TimeBasedSpeed = BaseFlowSpeed * (1.0f + FMath::Cos(AccumulatedTime * 0.8f) * 0.2f);

    // Atualizar amplitude serpentina baseada no tempo
    float TimeBasedAmplitude = SerpentineAmplitude * (1.0f + FMath::Sin(AccumulatedTime * 0.3f) * 0.15f);

    // Atualizar frequência serpentina baseada no tempo
    float TimeBasedFrequency = SerpentineFrequency * (1.0f + FMath::Cos(AccumulatedTime * 0.4f) * 0.1f);

    // Aplicar parâmetros atualizados aos componentes de efeito
    if (MainFlowEffect && IsValid(MainFlowEffect))
    {
        MainFlowEffect->SetFloatParameter(FName("FlowIntensity"), TimeBasedIntensity);
        MainFlowEffect->SetFloatParameter(FName("FlowSpeed"), TimeBasedSpeed);
        MainFlowEffect->SetFloatParameter(FName("SerpentineAmplitude"), TimeBasedAmplitude);
        MainFlowEffect->SetFloatParameter(FName("SerpentineFrequency"), TimeBasedFrequency);
        MainFlowEffect->SetFloatParameter(FName("TimeOffset"), AccumulatedTime);

        // Atualizar cor baseada no tempo e fase ou equipe controladora
        FLinearColor TimeBasedColor = GetFlowColorForPhase(CurrentMapPhase);
        float ColorIntensity = 1.0f + FMath::Sin(AccumulatedTime * 2.0f) * 0.2f;
        TimeBasedColor *= ColorIntensity;
        MainFlowEffect->SetColorParameter(FName("FlowColor"), TimeBasedColor);
    }

    // Atualizar componentes gerados com parâmetros baseados no tempo
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            // Atualizar materiais dinâmicos
            if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
            {
                DynMaterial->SetScalarParameterValue(FName("TimeOffset"), AccumulatedTime);
                DynMaterial->SetScalarParameterValue(FName("FlowIntensity"), TimeBasedIntensity);
                DynMaterial->SetScalarParameterValue(FName("FlowSpeed"), TimeBasedSpeed);

                // Aplicar efeitos específicos baseados no nome do componente
                if (MeshComp->GetName().Contains(TEXT("EnvironmentTransition")))
                {
                    float TransitionPulse = 1.0f + FMath::Sin(AccumulatedTime * 3.0f) * 0.4f;
                    DynMaterial->SetScalarParameterValue(FName("TransitionIntensity"), TimeBasedIntensity * TransitionPulse);
                }
                else if (MeshComp->GetName().Contains(TEXT("FlowObstacle")))
                {
                    float ObstaclePulse = 1.0f + FMath::Cos(AccumulatedTime * 2.5f) * 0.3f;
                    DynMaterial->SetScalarParameterValue(FName("ObstacleIntensity"), TimeBasedIntensity * ObstaclePulse);
                }
            }

            // Aplicar rotação baseada no tempo para elementos específicos
            if (MeshComp->GetName().Contains(TEXT("EnergyNode")) || MeshComp->GetName().Contains(TEXT("FlowCore")))
            {
                FRotator TimeBasedRotation = FRotator(0.0f, AccumulatedTime * 30.0f, 0.0f);
                MeshComp->SetRelativeRotation(TimeBasedRotation);
            }
        }
        else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
        {
            // Atualizar efeitos de partículas com parâmetros baseados no tempo
            NiagaraComp->SetFloatParameter(FName("TimeOffset"), AccumulatedTime);
            NiagaraComp->SetFloatParameter(FName("FlowIntensity"), TimeBasedIntensity);
            NiagaraComp->SetFloatParameter(FName("FlowSpeed"), TimeBasedSpeed);

            // Atualizar cor dos efeitos
            FLinearColor EffectColor = GetFlowColorForPhase(CurrentMapPhase);
            float EffectIntensity = 1.0f + FMath::Sin(AccumulatedTime * 1.5f) * 0.25f;
            EffectColor *= EffectIntensity;
            NiagaraComp->SetColorParameter(FName("EffectColor"), EffectColor);
        }
    }

    // Atualizar spline baseado no tempo se necessário
    if (FlowSpline && IsValid(FlowSpline))
    {
        // Aplicar deformação temporal sutil ao spline
        int32 NumPoints = FlowSpline->GetNumberOfSplinePoints();
        for (int32 i = 0; i < NumPoints; ++i)
        {
            FVector OriginalLocation = FlowSpline->GetLocationAtSplinePoint(i, ESplineCoordinateSpace::Local);

            // Aplicar ondulação temporal
            float TimeWave = FMath::Sin(AccumulatedTime * 0.8f + i * 0.5f) * 20.0f * GlobalFlowIntensity;
            FVector TimeBasedOffset = FVector(0.0f, 0.0f, TimeWave);

            // Aplicar apenas se a intensidade for significativa
            if (GlobalFlowIntensity > 0.5f)
            {
                FlowSpline->SetLocationAtSplinePoint(i, OriginalLocation + TimeBasedOffset, ESplineCoordinateSpace::Local, false);
            }
        }

        // Atualizar spline após modificações
        if (GlobalFlowIntensity > 0.5f)
        {
            FlowSpline->UpdateSpline();
        }
    }

    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGPrismalFlow::UpdateTimeBasedParameters - Updated time-based parameters (Time: %.2f, Intensity: %.2f)"), AccumulatedTime, TimeBasedIntensity);
}

void AAURACRONPCGPrismalFlow::GenerateFlowObstacles()
{
    // Gerar obstáculos do flow usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::GenerateFlowObstacles - Generating flow obstacles with modern UE 5.6 APIs"));

    if (!FlowSpline || !IsValid(FlowSpline))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::GenerateFlowObstacles - FlowSpline is invalid"));
        return;
    }

    float SplineLength = FlowSpline->GetSplineLength();
    if (SplineLength <= 0.0f)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGPrismalFlow::GenerateFlowObstacles - Spline length is invalid"));
        return;
    }

    // Calcular número de obstáculos baseado no comprimento do spline e intensidade
    int32 NumObstacles = FMath::Clamp(
        static_cast<int32>((SplineLength / 1000.0f) * GlobalFlowIntensity * 3.0f),
        2, 12
    );

    for (int32 i = 0; i < NumObstacles; ++i)
    {
        // Posicionar obstáculos de forma semi-aleatória ao longo do spline
        float Alpha = (static_cast<float>(i) + FMath::RandRange(0.1f, 0.9f)) / static_cast<float>(NumObstacles);
        float SplineDistance = SplineLength * Alpha;

        // Obter localização e direção no spline
        FVector ObstacleLocation = FlowSpline->GetLocationAtDistanceAlongSpline(SplineDistance, ESplineCoordinateSpace::World);
        FVector FlowDirection = FlowSpline->GetDirectionAtDistanceAlongSpline(SplineDistance, ESplineCoordinateSpace::World);
        FRotator ObstacleRotation = FlowDirection.Rotation();

        // Adicionar offset lateral aleatório
        FVector RightVector = FVector::CrossProduct(FlowDirection, FVector::UpVector).GetSafeNormal();
        float LateralOffset = FMath::RandRange(-300.0f, 300.0f);
        ObstacleLocation += RightVector * LateralOffset;

        // Adicionar offset vertical
        ObstacleLocation.Z += FMath::RandRange(50.0f, 200.0f);

        // Criar obstáculo usando APIs modernas
        UStaticMeshComponent* FlowObstacle = CreateDefaultSubobject<UStaticMeshComponent>(
            *FString::Printf(TEXT("FlowObstacle_%d_%d"), i, FMath::RandRange(1000, 9999))
        );

        if (FlowObstacle)
        {
            FlowObstacle->SetupAttachment(RootComponent);
            FlowObstacle->SetWorldLocation(ObstacleLocation);
            FlowObstacle->SetWorldRotation(ObstacleRotation);

            // Escala baseada na intensidade e tipo de obstáculo
            float ObstacleScale = FMath::RandRange(0.8f, 2.2f) * (1.0f + GlobalFlowIntensity * 0.5f);
            FlowObstacle->SetRelativeScale3D(FVector(ObstacleScale, ObstacleScale, ObstacleScale * 1.5f));

            // Configurar material de obstáculo usando APIs modernas
            if (UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial")))
            {
                UMaterialInstanceDynamic* ObstacleMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
                if (ObstacleMaterial)
                {
                    // Configurar parâmetros baseados no tipo de obstáculo
                    float ObstacleType = FMath::RandRange(1.0f, 4.0f);
                    FLinearColor ObstacleColor;

                    if (ObstacleType < 2.0f)
                    {
                        // Obstáculo cristalino
                        ObstacleColor = FLinearColor(0.3f, 0.8f, 1.0f, 0.9f);
                        ObstacleMaterial->SetScalarParameterValue(FName("CrystallineEffect"), 2.0f);
                    }
                    else if (ObstacleType < 3.0f)
                    {
                        // Obstáculo energético
                        ObstacleColor = FLinearColor(1.0f, 0.6f, 0.2f, 0.8f);
                        ObstacleMaterial->SetScalarParameterValue(FName("EnergyPulse"), 1.8f);
                    }
                    else
                    {
                        // Obstáculo místico
                        ObstacleColor = FLinearColor(0.8f, 0.3f, 1.0f, 0.85f);
                        ObstacleMaterial->SetScalarParameterValue(FName("MysticAura"), 2.2f);
                    }

                    ObstacleMaterial->SetScalarParameterValue(FName("ObstacleIntensity"), GlobalFlowIntensity * 1.5f);
                    ObstacleMaterial->SetScalarParameterValue(FName("FlowInteraction"), BaseFlowSpeed * 0.8f);
                    ObstacleMaterial->SetVectorParameterValue(FName("ObstacleColor"), ObstacleColor);

                    FlowObstacle->SetMaterial(0, ObstacleMaterial);
                }
            }

            // Configurar colisão para interação com o flow
            FlowObstacle->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
            FlowObstacle->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);
            FlowObstacle->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Block);

            GeneratedComponents.Add(FlowObstacle);
        }

        // Criar efeito de partículas para o obstáculo
        UNiagaraComponent* ObstacleEffect = CreateDefaultSubobject<UNiagaraComponent>(
            *FString::Printf(TEXT("ObstacleEffect_%d_%d"), i, FMath::RandRange(1000, 9999))
        );

        if (ObstacleEffect)
        {
            ObstacleEffect->SetupAttachment(RootComponent);
            ObstacleEffect->SetWorldLocation(ObstacleLocation + FVector(0.0f, 0.0f, 50.0f));

            // Configurar parâmetros do efeito de obstáculo
            ObstacleEffect->SetFloatParameter(FName("ObstacleIntensity"), GlobalFlowIntensity * 1.3f);
            ObstacleEffect->SetFloatParameter(FName("FlowDisruption"), BaseFlowSpeed * 0.6f);
            ObstacleEffect->SetVectorParameter(FName("FlowDirection"), FlowDirection);

            // Configurar cor baseada na fase do mapa
            FLinearColor EffectColor = GetFlowColorForPhase(CurrentMapPhase);
            EffectColor *= 1.2f; // Intensificar para obstáculos
            ObstacleEffect->SetColorParameter(FName("EffectColor"), EffectColor);

            GeneratedComponents.Add(ObstacleEffect);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::GenerateFlowObstacles - Generated %d flow obstacles"), NumObstacles);
}

void AAURACRONPCGPrismalFlow::ApplyMapPhaseEffects()
{
    // Aplicar efeitos da fase do mapa usando APIs modernas do UE 5.6
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::ApplyMapPhaseEffects - Applying map phase effects for phase %d"), (int32)CurrentMapPhase);

    // Aplicar efeitos baseados na fase atual do mapa
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
        {
            // Fase do despertar: flow suave e crescente
            GlobalFlowIntensity = FMath::Clamp(GlobalFlowIntensity * 0.7f, 0.4f, 1.0f);
            BaseFlowSpeed = FMath::Clamp(BaseFlowSpeed * 0.8f, 0.5f, 2.0f);

            // Aplicar efeitos visuais de despertar
            ApplyPhaseVisualEffects(FLinearColor(1.0f, 0.9f, 0.7f, 1.0f), 0.6f, TEXT("AwakeningGlow"));
            break;
        }

        case EAURACRONMapPhase::Convergence:
        {
            // Fase da convergência: flow equilibrado e focado
            GlobalFlowIntensity = FMath::Clamp(GlobalFlowIntensity * 0.9f, 0.6f, 1.2f);
            BaseFlowSpeed = FMath::Clamp(BaseFlowSpeed * 1.0f, 0.8f, 2.5f);

            // Aplicar efeitos visuais de convergência
            ApplyPhaseVisualEffects(FLinearColor(0.8f, 1.0f, 0.9f, 1.0f), 0.8f, TEXT("ConvergenceEnergy"));
            break;
        }

        case EAURACRONMapPhase::Intensification:
        {
            // Fase da intensificação: flow poderoso e dinâmico
            GlobalFlowIntensity = FMath::Clamp(GlobalFlowIntensity * 1.2f, 0.8f, 1.5f);
            BaseFlowSpeed = FMath::Clamp(BaseFlowSpeed * 1.3f, 1.0f, 3.0f);

            // Aplicar efeitos visuais de intensificação
            ApplyPhaseVisualEffects(FLinearColor(1.0f, 0.6f, 0.3f, 1.0f), 1.0f, TEXT("IntensificationPower"));
            break;
        }

        case EAURACRONMapPhase::Resolution:
        {
            // Fase da resolução: flow épico e transcendente
            GlobalFlowIntensity = FMath::Clamp(GlobalFlowIntensity * 1.5f, 1.0f, 2.0f);
            BaseFlowSpeed = FMath::Clamp(BaseFlowSpeed * 1.8f, 1.5f, 4.0f);

            // Aplicar efeitos visuais de resolução
            ApplyPhaseVisualEffects(FLinearColor(0.7f, 0.4f, 1.0f, 1.0f), 1.5f, TEXT("ResolutionMajesty"));
            break;
        }
    }

    // Atualizar amplitude e frequência serpentina baseadas na fase
    switch (CurrentMapPhase)
    {
        case EAURACRONMapPhase::Awakening:
            SerpentineAmplitude = FMath::Clamp(SerpentineAmplitude * 0.8f, 800.0f, 2000.0f);
            SerpentineFrequency = FMath::Clamp(SerpentineFrequency * 0.9f, 2.0f, 5.0f);
            break;

        case EAURACRONMapPhase::Convergence:
            SerpentineAmplitude = FMath::Clamp(SerpentineAmplitude * 1.0f, 1000.0f, 2500.0f);
            SerpentineFrequency = FMath::Clamp(SerpentineFrequency * 1.1f, 3.0f, 6.0f);
            break;

        case EAURACRONMapPhase::Intensification:
            SerpentineAmplitude = FMath::Clamp(SerpentineAmplitude * 1.3f, 1200.0f, 3000.0f);
            SerpentineFrequency = FMath::Clamp(SerpentineFrequency * 1.4f, 4.0f, 7.0f);
            break;

        case EAURACRONMapPhase::Resolution:
            SerpentineAmplitude = FMath::Clamp(SerpentineAmplitude * 1.6f, 1500.0f, 4000.0f);
            SerpentineFrequency = FMath::Clamp(SerpentineFrequency * 1.8f, 5.0f, 8.0f);
            break;
    }

    // Regenerar caminho do flow com novos parâmetros
    GenerateFlowPath();

    // Aplicar efeitos específicos aos componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
            {
                // Aplicar parâmetros da fase atual
                DynMaterial->SetScalarParameterValue(FName("PhaseIntensity"), GlobalFlowIntensity);
                DynMaterial->SetScalarParameterValue(FName("FlowSpeed"), BaseFlowSpeed);
                DynMaterial->SetScalarParameterValue(FName("SerpentineAmplitude"), SerpentineAmplitude);
                DynMaterial->SetScalarParameterValue(FName("SerpentineFrequency"), SerpentineFrequency);

                // Aplicar cor da fase
                FLinearColor PhaseColor = GetFlowColorForPhase(CurrentMapPhase);
                DynMaterial->SetVectorParameterValue(FName("PhaseColor"), PhaseColor);
            }
        }
        else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
        {
            // Atualizar efeitos de partículas com parâmetros da fase
            NiagaraComp->SetFloatParameter(FName("PhaseIntensity"), GlobalFlowIntensity);
            NiagaraComp->SetFloatParameter(FName("FlowSpeed"), BaseFlowSpeed);

            FLinearColor PhaseColor = GetFlowColorForPhase(CurrentMapPhase);
            NiagaraComp->SetColorParameter(FName("PhaseColor"), PhaseColor);
        }
    }

    // Regenerar conteúdo PCG se necessário
    if (PCGComponent && IsValid(PCGComponent) && GlobalFlowIntensity > 0.0f)
    {
        PCGComponent->GenerateLocal(false); // Regeneração não-bloqueante
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::ApplyMapPhaseEffects - Map phase effects applied successfully"));
}

void AAURACRONPCGPrismalFlow::ApplyPhaseVisualEffects(const FLinearColor& PhaseColor, float PhaseIntensity, const FString& EffectName)
{
    // Aplicar efeitos visuais específicos da fase usando APIs modernas
    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGPrismalFlow::ApplyPhaseVisualEffects - Applying %s effects"), *EffectName);

    // Aplicar ao efeito principal
    if (MainFlowEffect && IsValid(MainFlowEffect))
    {
        MainFlowEffect->SetColorParameter(FName("PhaseColor"), PhaseColor);
        MainFlowEffect->SetFloatParameter(FName("PhaseIntensity"), PhaseIntensity);
        MainFlowEffect->SetFloatParameter(FName(*EffectName), PhaseIntensity * 1.2f);
    }

    // Aplicar aos componentes gerados
    for (UActorComponent* Component : GeneratedComponents)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
            {
                DynMaterial->SetVectorParameterValue(FName("PhaseColor"), PhaseColor);
                DynMaterial->SetScalarParameterValue(FName("PhaseIntensity"), PhaseIntensity);
                DynMaterial->SetScalarParameterValue(FName(*EffectName), PhaseIntensity * 1.1f);
            }
        }
        else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
        {
            NiagaraComp->SetColorParameter(FName("PhaseColor"), PhaseColor);
            NiagaraComp->SetFloatParameter(FName("PhaseIntensity"), PhaseIntensity);
            NiagaraComp->SetFloatParameter(FName(*EffectName), PhaseIntensity * 1.3f);
        }
    }
}

FLinearColor AAURACRONPCGPrismalFlow::GetFlowColorForTeam() const
{
    // Retornar cor baseada na equipe controladora
    switch (ControllingTeam)
    {
        case 1: // Equipe A
            return FLinearColor(0.0f, 0.5f, 1.0f, 1.0f); // Azul

        case 2: // Equipe B
            return FLinearColor(1.0f, 0.2f, 0.2f, 1.0f); // Vermelho

        default: // Neutro ou inválido
            return GetFlowColorForPhase(CurrentMapPhase); // Usar cor da fase atual
    }
}

void AAURACRONPCGPrismalFlow::SetControllingTeam(int32 TeamID)
{
    // Verificar se a equipe mudou
    if (ControllingTeam != TeamID)
    {
        // Atualizar a equipe controladora
        ControllingTeam = TeamID;
        
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGPrismalFlow::SetControllingTeam - Flow now controlled by team %d"), ControllingTeam);
        
        // Atualizar efeitos visuais para refletir a nova equipe controladora
        if (MainFlowEffect && IsValid(MainFlowEffect))
        {
            FLinearColor TeamColor = GetFlowColorForTeam();
            MainFlowEffect->SetColorParameter(FName("FlowColor"), TeamColor);
            MainFlowEffect->SetColorParameter(FName("PhaseColor"), TeamColor);
        }
        
        // Atualizar componentes gerados
        for (UActorComponent* Component : GeneratedComponents)
        {
            if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
            {
                if (UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                {
                    FLinearColor TeamColor = GetFlowColorForTeam();
                    DynMaterial->SetVectorParameterValue(FName("PhaseColor"), TeamColor);
                }
            }
            else if (UNiagaraComponent* NiagaraComp = Cast<UNiagaraComponent>(Component))
            {
                FLinearColor TeamColor = GetFlowColorForTeam();
                NiagaraComp->SetColorParameter(FName("PhaseColor"), TeamColor);
                NiagaraComp->SetColorParameter(FName("EffectColor"), TeamColor);
            }
        }
    }
}
