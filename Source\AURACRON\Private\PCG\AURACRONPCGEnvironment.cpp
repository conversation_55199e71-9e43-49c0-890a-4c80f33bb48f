// AURACRONPCGEnvironment.cpp
// Sistema de GeraÃ§Ã£o Procedural para AURACRON - UE 5.6
// ImplementaÃ§Ã£o da classe para gerenciar os ambientes procedurais

#include "PCG/AURACRONPCGEnvironment.h"
#include "PCG/AURACRONPCGSubsystem.h"
#include "PCG/AURACRONPCGEnvironmentManager.h"
#include "PCG/AURACRONMapMeasurements.h"
#include "PCG/AURACRONPCGMathLibrary.h"
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGSettings.h"
#include "PCGVolume.h"
#include "Engine/World.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "NiagaraFunctionLibrary.h"
#include "PCG/AURACRONPCGPortal.h"
#include "Kismet/GameplayStatics.h"
#include "Net/UnrealNetwork.h"

AAURACRONPCGEnvironment::AAURACRONPCGEnvironment()
{
    PrimaryActorTick.bCanEverTick = true;

    // Criar o componente raiz (USceneComponent)
    USceneComponent* SceneRoot = CreateDefaultSubobject<USceneComponent>(TEXT("SceneRoot"));
    RootComponent = SceneRoot;

    // Criar o componente PCG
    PCGComponent = CreateDefaultSubobject<UPCGComponent>(TEXT("PCGComponent"));

    // Valores padrÃ£o
    EnvironmentType = EAURACRONEnvironmentType::RadiantPlains;
    ActivityScale = 0.0f;

    // Inicializar caracterÃ­sticas especÃ­ficas
    bHasCrystallinePlateaus = true;
    bHasLivingCanyons = true;
    bHasBreathingForests = true;
    bHasTectonicBridges = true;
    
    bHasOrbitalArchipelagos = true;
    bHasAuroraBridges = true;
    bHasCloudFortresses = true;
    bHasStellarGardens = true;
    bHasVoidRifts = true;
    
    bHasSpectralPlains = true;
    bHasRiversOfSouls = true;
    bHasFragmentedStructures = true;
    bHasTemporalDistortionZones = true;
}

void AAURACRONPCGEnvironment::BeginPlay()
{
    Super::BeginPlay();
    
    // Configurar o componente PCG com as configuraÃ§Ãµes apropriadas (UE 5.6 API moderna)
    if (EnvironmentSettings)
    {
        // No UE 5.6, as configuraÃ§Ãµes sÃ£o definidas via GraphInstance
        if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
        {
            UE_LOG(LogTemp, Log, TEXT("AURACRONPCGEnvironment: EnvironmentSettings configurado via GraphInstance"));
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGEnvironment: GraphInstance nÃ£o encontrado - configuraÃ§Ãµes serÃ£o definidas via Blueprint"));
        }
    }
    
    // Iniciar com visibilidade desativada atÃ© que seja explicitamente ativado
    SetEnvironmentVisibility(false);
    
    // Definir escala de atividade inicial
    SetActivityScale(0.0f);
}

void AAURACRONPCGEnvironment::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Atualizar elementos dinâmicos do ambiente
    if (bIsActive)
    {
        UpdateDynamicElements(DeltaTime);
    }
}

void AAURACRONPCGEnvironment::SetEnvironmentType(EAURACRONEnvironmentType NewType)
{
    EnvironmentType = NewType;
    
    // Reconfigurar o ambiente com base no novo tipo
    // Isso pode envolver a alteraÃ§Ã£o das configuraÃ§Ãµes do PCG
    GenerateEnvironment();
}

void AAURACRONPCGEnvironment::GenerateEnvironment()
{
    // Verificar se temos configuraÃ§Ãµes vÃ¡lidas
    if (!EnvironmentSettings)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGEnvironment: Nenhuma configuraÃ§Ã£o PCG definida para o ambiente %s"), 
               *GetNameSafe(this));
        return;
    }

    // Gerar caracterÃ­sticas especÃ­ficas com base no tipo de ambiente
    switch (EnvironmentType)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        if (bHasCrystallinePlateaus) GenerateCrystallinePlateaus();
        if (bHasLivingCanyons) GenerateLivingCanyons();
        if (bHasBreathingForests) GenerateBreathingForests();
        if (bHasTectonicBridges) GenerateTectonicBridges();
        break;
        
    case EAURACRONEnvironmentType::ZephyrFirmament:
        if (bHasOrbitalArchipelagos) GenerateOrbitalArchipelagos();
        if (bHasAuroraBridges) GenerateAuroraBridges();
        if (bHasCloudFortresses) GenerateCloudFortresses();
        if (bHasStellarGardens) GenerateStellarGardens();
        if (bHasVoidRifts) GenerateVoidRifts();
        break;
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        if (bHasSpectralPlains) GenerateSpectralPlains();
        if (bHasRiversOfSouls) GenerateRiversOfSouls();
        if (bHasFragmentedStructures) GenerateFragmentedStructures();
        if (bHasTemporalDistortionZones) GenerateTemporalDistortionZones();
        if (bHasShadowStructures) {
            GenerateShadowNexuses();
            GenerateTowersOfLamentation();
            GenerateSpectralGuardian();
            GeneratePurgatoryAnchor();
        }
        break;
    }

    // Executar a geraÃ§Ã£o PCG
    PCGComponent->Generate();
}

void AAURACRONPCGEnvironment::UpdateForMapPhase(EAURACRONMapPhase MapPhase)
{
    // Ajustar o ambiente com base na fase do mapa
    // Isso pode envolver a alteraÃ§Ã£o de parÃ¢metros de geraÃ§Ã£o ou a ativaÃ§Ã£o/desativaÃ§Ã£o de caracterÃ­sticas
    
    // Exemplo: Aumentar a intensidade de certas caracterÃ­sticas durante a fase de Prismal Flow
    if (MapPhase == EAURACRONMapPhase::PrismalFlow)
    {
        // Aumentar a intensidade de caracterÃ­sticas especÃ­ficas
        // Isso seria implementado atravÃ©s de parÃ¢metros especÃ­ficos no PCG
    }
    
    // Regenerar o ambiente com as novas configuraÃ§Ãµes
    GenerateEnvironment();
}

void AAURACRONPCGEnvironment::SetEnvironmentVisibility(bool bVisible)
{
    // Definir a visibilidade de todos os componentes gerados
    SetActorHiddenInGame(!bVisible);
    
    // Se estiver visÃ­vel, garantir que a geraÃ§Ã£o PCG esteja atualizada
    if (bVisible)
    {
        GenerateEnvironment();
    }
}

void AAURACRONPCGEnvironment::SetActivityScale(float Scale)
{
    // Limitar a escala entre 0.0 e 1.0
    ActivityScale = FMath::Clamp(Scale, 0.0f, 1.0f);
    
    // Aplicar a escala de atividade aos parÃ¢metros de geraÃ§Ã£o
    // Isso pode envolver a alteraÃ§Ã£o de parÃ¢metros especÃ­ficos no PCG
    
    // Regenerar o ambiente se a escala for significativa
    if (ActivityScale > 0.01f)
    {
        GenerateEnvironment();
    }
}

// ========================================
// IMPLEMENTAÃ‡Ã•ES DAS FUNÃ‡Ã•ES DE GERAÃ‡ÃƒO ESPECÃFICAS
// ========================================

void AAURACRONPCGEnvironment::GenerateCrystallinePlateaus()
{
    if (!HasAuthority())
    {
        return;
    }

    // Usar as dimensÃµes definidas no sistema de medidas
    int32 PlateauCount = FMath::RandRange(
        FAURACRONMapDimensions::CRYSTALLINE_PLATEAUS_COUNT_MIN,
        FAURACRONMapDimensions::CRYSTALLINE_PLATEAUS_COUNT_MAX
    );

    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    // Distribuir platÃ´s usando amostragem de Poisson
    TArray<FVector> PlateauPositions = UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
        EnvironmentCenter,
        EnvironmentRadius * 0.8f, // Dentro de 80% do raio do ambiente
        800.0f, // DistÃ¢ncia mÃ­nima entre platÃ´s (8 metros)
        30,
        12345
    );

    // Limitar ao nÃºmero desejado
    int32 ActualCount = FMath::Min(PlateauCount, PlateauPositions.Num());

    for (int32 i = 0; i < ActualCount; ++i)
    {
        FVector PlateauCenter = PlateauPositions[i];
        float PlateauRadius = FMath::RandRange(300.0f, 600.0f); // 3-6 metros
        float PlateauHeight = FMath::RandRange(
            FAURACRONMapDimensions::CRYSTALLINE_PLATEAU_HEIGHT_MIN_CM,
            FAURACRONMapDimensions::CRYSTALLINE_PLATEAU_HEIGHT_MAX_CM
        );

        // Gerar vÃ©rtices do platÃ´
        TArray<FVector> PlateauVertices = UAURACRONPCGMathLibrary::GenerateCrystallinePlateauVertices(
            PlateauCenter,
            PlateauRadius,
            PlateauHeight,
            8, // 8 lados
            0.3f // 30% de irregularidade
        );

        // Criar ator para o platÃ´ (seria substituÃ­do por geraÃ§Ã£o PCG real)
        FActorSpawnParameters SpawnParams;
        SpawnParams.Owner = this;

        AStaticMeshActor* PlateauActor = GetWorld()->SpawnActor<AStaticMeshActor>(
            PlateauCenter, FRotator::ZeroRotator, SpawnParams
        );

        if (PlateauActor)
        {
            GeneratedActors.Add(PlateauActor);
        }
    }
}

void AAURACRONPCGEnvironment::GenerateLivingCanyons()
{
    if (!HasAuthority())
    {
        return;
    }

    int32 CanyonCount = FMath::RandRange(
        FAURACRONMapDimensions::LIVING_CANYONS_COUNT_MIN,
        FAURACRONMapDimensions::LIVING_CANYONS_COUNT_MAX
    );

    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    for (int32 i = 0; i < CanyonCount; ++i)
    {
        // Gerar pontos de inÃ­cio e fim aleatÃ³rios
        float StartAngle = FMath::RandRange(0.0f, 2.0f * PI);
        float EndAngle = StartAngle + FMath::RandRange(PI * 0.5f, PI * 1.5f);

        FVector StartPoint = EnvironmentCenter + FVector(
            EnvironmentRadius * 0.6f * FMath::Cos(StartAngle),
            EnvironmentRadius * 0.6f * FMath::Sin(StartAngle),
            0.0f
        );

        FVector EndPoint = EnvironmentCenter + FVector(
            EnvironmentRadius * 0.6f * FMath::Cos(EndAngle),
            EnvironmentRadius * 0.6f * FMath::Sin(EndAngle),
            0.0f
        );

        float CanyonWidth = FMath::RandRange(400.0f, 800.0f); // 4-8 metros
        float CanyonDepth = FMath::RandRange(
            FAURACRONMapDimensions::LIVING_CANYON_DEPTH_MIN_CM,
            FAURACRONMapDimensions::LIVING_CANYON_DEPTH_MAX_CM
        );

        // Gerar caminho do cÃ¢nion
        TArray<FVector> CanyonPath = UAURACRONPCGMathLibrary::GenerateLivingCanyonPath(
            StartPoint,
            EndPoint,
            CanyonWidth,
            CanyonDepth,
            20 // 20 segmentos
        );

        // Criar representaÃ§Ã£o do cÃ¢nion (seria substituÃ­do por geraÃ§Ã£o PCG real)
        for (const FVector& PathPoint : CanyonPath)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.Owner = this;

            AStaticMeshActor* CanyonSegment = GetWorld()->SpawnActor<AStaticMeshActor>(
                PathPoint, FRotator::ZeroRotator, SpawnParams
            );

            if (CanyonSegment)
            {
                GeneratedActors.Add(CanyonSegment);
            }
        }
    }
}

void AAURACRONPCGEnvironment::GenerateBreathingForests()
{
    if (!HasAuthority())
    {
        return;
    }

    int32 ForestCount = FMath::RandRange(
        FAURACRONMapDimensions::BREATHING_FORESTS_COUNT_MIN,
        FAURACRONMapDimensions::BREATHING_FORESTS_COUNT_MAX
    );

    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    // Distribuir centros das florestas
    TArray<FVector> ForestCenters = UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
        EnvironmentCenter,
        EnvironmentRadius * 0.7f,
        1000.0f, // DistÃ¢ncia mÃ­nima entre florestas (10 metros)
        30,
        54321
    );

    int32 ActualForestCount = FMath::Min(ForestCount, ForestCenters.Num());

    for (int32 i = 0; i < ActualForestCount; ++i)
    {
        FVector ForestCenter = ForestCenters[i];
        float ForestRadius = FMath::RandRange(
            FAURACRONMapDimensions::BREATHING_FOREST_RADIUS_MIN_CM,
            FAURACRONMapDimensions::BREATHING_FOREST_RADIUS_MAX_CM
        );

        int32 TreeCount = FMath::RandRange(20, 50);

        // Gerar posiÃ§Ãµes das Ã¡rvores
        TArray<FVector> TreePositions = UAURACRONPCGMathLibrary::GenerateBreathingForestPositions(
            ForestCenter,
            ForestRadius,
            TreeCount,
            150.0f, // DistÃ¢ncia mÃ­nima entre Ã¡rvores (1.5 metros)
            30.0f,  // Amplitude de respiraÃ§Ã£o (30cm)
            0.0f    // Tempo inicial
        );

        // Armazenar informaÃ§Ãµes da floresta para atualizaÃ§Ã£o dinÃ¢mica
        BreathingForests.Add({ForestCenter, ForestRadius, TreePositions});

        // Criar Ã¡rvores
        for (const FVector& TreePos : TreePositions)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.Owner = this;

            AStaticMeshActor* Tree = GetWorld()->SpawnActor<AStaticMeshActor>(
                TreePos, FRotator::ZeroRotator, SpawnParams
            );

            if (Tree)
            {
                GeneratedActors.Add(Tree);
            }
        }
    }
}

void AAURACRONPCGEnvironment::GenerateTectonicBridges()
{
    if (!HasAuthority())
    {
        return;
    }

    int32 BridgeCount = FMath::RandRange(
        FAURACRONMapDimensions::TECTONIC_BRIDGES_COUNT_MIN,
        FAURACRONMapDimensions::TECTONIC_BRIDGES_COUNT_MAX
    );

    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    for (int32 i = 0; i < BridgeCount; ++i)
    {
        // Gerar pontos de inÃ­cio e fim para a ponte
        float StartAngle = FMath::RandRange(0.0f, 2.0f * PI);
        float EndAngle = StartAngle + FMath::RandRange(PI * 0.3f, PI * 0.8f);

        FVector StartPoint = EnvironmentCenter + FVector(
            EnvironmentRadius * 0.5f * FMath::Cos(StartAngle),
            EnvironmentRadius * 0.5f * FMath::Sin(StartAngle),
            FMath::RandRange(100.0f, 300.0f) // Altura variÃ¡vel
        );

        FVector EndPoint = EnvironmentCenter + FVector(
            EnvironmentRadius * 0.5f * FMath::Cos(EndAngle),
            EnvironmentRadius * 0.5f * FMath::Sin(EndAngle),
            FMath::RandRange(100.0f, 300.0f) // Altura variÃ¡vel
        );

        float BridgeWidth = FMath::RandRange(200.0f, 400.0f); // 2-4 metros
        int32 NumSupports = FMath::RandRange(3, 6);
        float ArchHeight = FMath::RandRange(200.0f, 500.0f); // 2-5 metros

        // Gerar pontos da ponte
        TArray<FVector> BridgePoints = UAURACRONPCGMathLibrary::GenerateTectonicBridgePoints(
            StartPoint,
            EndPoint,
            BridgeWidth,
            NumSupports,
            ArchHeight
        );

        // Criar segmentos da ponte
        for (const FVector& BridgePoint : BridgePoints)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.Owner = this;

            AStaticMeshActor* BridgeSegment = GetWorld()->SpawnActor<AStaticMeshActor>(
                BridgePoint, FRotator::ZeroRotator, SpawnParams
            );

            if (BridgeSegment)
            {
                GeneratedActors.Add(BridgeSegment);
            }
        }
    }
}

// ========================================
// IMPLEMENTAÃ‡Ã•ES PARA ZEPHYR FIRMAMENT
// ========================================

void AAURACRONPCGEnvironment::GenerateOrbitalArchipelagos()
{
    if (!HasAuthority())
    {
        return;
    }

    int32 ArchipelagoCount = FMath::RandRange(
        FAURACRONMapDimensions::ORBITAL_ARCHIPELAGOS_COUNT_MIN,
        FAURACRONMapDimensions::ORBITAL_ARCHIPELAGOS_COUNT_MAX
    );

    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    for (int32 i = 0; i < ArchipelagoCount; ++i)
    {
        // Cada arquipÃ©lago tem um centro orbital
        float OrbitRadius = FMath::RandRange(EnvironmentRadius * 0.3f, EnvironmentRadius * 0.7f);
        float OrbitAngle = (2.0f * PI * i / ArchipelagoCount) + FMath::RandRange(-0.5f, 0.5f);

        FVector ArchipelagoCenter = EnvironmentCenter + FVector(
            OrbitRadius * FMath::Cos(OrbitAngle),
            OrbitRadius * FMath::Sin(OrbitAngle),
            FMath::RandRange(200.0f, 800.0f) // Altura variÃ¡vel
        );

        // Gerar ilhas individuais no arquipÃ©lago
        int32 IslandCount = FMath::RandRange(3, 8);
        float ArchipelagoRadius = FMath::RandRange(300.0f, 600.0f);

        TArray<FVector> IslandPositions = UAURACRONPCGMathLibrary::CreateOrbitalPath(
            ArchipelagoCenter,
            ArchipelagoRadius,
            0.0f, // Altura relativa
            IslandCount,
            FMath::RandRange(0.0f, 2.0f * PI) // Fase aleatÃ³ria
        );

        for (const FVector& IslandPos : IslandPositions)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.Owner = this;

            AStaticMeshActor* Island = GetWorld()->SpawnActor<AStaticMeshActor>(
                IslandPos, FRotator::ZeroRotator, SpawnParams
            );

            if (Island)
            {
                // Configurar a malha da ilha
                UStaticMeshComponent* MeshComponent = Island->GetStaticMeshComponent();
                if (MeshComponent)
                {
                    // Carregar uma malha de ilha flutuante
                    static ConstructorHelpers::FObjectFinder<UStaticMesh> IslandMeshFinder(TEXT("/Game/AURACRON/Meshes/Environment/ZephyrFirmament/SM_FloatingIsland"));
                    UStaticMesh* IslandMesh = IslandMeshFinder.Object;
                    
                    if (IslandMesh)
                    {
                        MeshComponent->SetStaticMesh(IslandMesh);
                        
                        // Escala aleatória para variedade
                        float IslandScale = FMath::RandRange(0.8f, 1.5f);
                        MeshComponent->SetWorldScale3D(FVector(IslandScale, IslandScale, IslandScale));
                        
                        // Rotação aleatória para variedade
                        MeshComponent->SetWorldRotation(FRotator(0.0f, FMath::RandRange(0.0f, 360.0f), 0.0f));
                        
                        // Configurar física
                        MeshComponent->SetMobility(EComponentMobility::Movable);
                        MeshComponent->SetSimulatePhysics(false);
                        MeshComponent->SetEnableGravity(false);
                        
                        // Adicionar efeito de flutuação
                        FFloatingIslandData IslandData;
                        IslandData.IslandActor = Island;
                        IslandData.OriginalHeight = IslandPos.Z;
                        IslandData.FloatHeight = FMath::RandRange(5.0f, 15.0f);
                        IslandData.FloatSpeed = FMath::RandRange(0.5f, 1.5f);
                        IslandData.TimeOffset = FMath::RandRange(0.0f, 2.0f * PI);
                        
                        // Armazenar para animação em Tick
                        FloatingIslandData.Add(IslandData);
                    }
                }
                
                GeneratedActors.Add(Island);
            }
        }
    }
}

void AAURACRONPCGEnvironment::GenerateAuroraBridges()
{
    if (!HasAuthority())
    {
        return;
    }

    int32 BridgeCount = FMath::RandRange(
        FAURACRONMapDimensions::AURORA_BRIDGES_COUNT_MIN,
        FAURACRONMapDimensions::AURORA_BRIDGES_COUNT_MAX
    );

    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    for (int32 i = 0; i < BridgeCount; ++i)
    {
        // Pontes Aurora conectam pontos distantes
        float StartAngle = FMath::RandRange(0.0f, 2.0f * PI);
        float EndAngle = StartAngle + FMath::RandRange(PI * 0.5f, PI * 1.5f);

        FVector StartPoint = EnvironmentCenter + FVector(
            EnvironmentRadius * 0.8f * FMath::Cos(StartAngle),
            EnvironmentRadius * 0.8f * FMath::Sin(StartAngle),
            FMath::RandRange(500.0f, 1000.0f)
        );

        FVector EndPoint = EnvironmentCenter + FVector(
            EnvironmentRadius * 0.8f * FMath::Cos(EndAngle),
            EnvironmentRadius * 0.8f * FMath::Sin(EndAngle),
            FMath::RandRange(500.0f, 1000.0f)
        );

        // Criar curva aurora
        FAURACRONSplineCurve AuroraCurve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
            StartPoint,
            EndPoint,
            15, // 15 pontos de controle
            300.0f, // Amplitude de 3 metros
            1.5f // FrequÃªncia
        );

        // Distribuir pontos de luz ao longo da curva
        TArray<FVector> LightPoints = UAURACRONPCGMathLibrary::DistributePointsAlongCurve(
            AuroraCurve,
            200.0f, // EspaÃ§amento de 2 metros
            true // Alinhar Ã  tangente
        );

        // Criar sistema de partículas para a ponte aurora
        UNiagaraComponent* AuroraBridge = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            AuroraBridgeParticleSystem,
            StartPoint,
            FRotator::ZeroRotator,
            FVector(1.0f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
        
        if (AuroraBridge)
        {
            // Configurar parâmetros do sistema de partículas
            AuroraBridge->SetVectorParameter("StartPoint", StartPoint);
            AuroraBridge->SetVectorParameter("EndPoint", EndPoint);
            AuroraBridge->SetFloatParameter("BridgeWidth", FMath::RandRange(50.0f, 150.0f));
            
            // Cor baseada no ciclo dia/noite (simulado com tempo do mundo)
            float TimeOfDay = FMath::Frac(GetWorld()->GetTimeSeconds() / 300.0f); // Ciclo de 5 minutos
            FLinearColor BridgeColor;
            
            if (TimeOfDay < 0.5f) // Dia
            {
                BridgeColor = FLinearColor(0.2f, 0.8f, 1.0f, 0.8f); // Azul claro
            }
            else // Noite
            {
                BridgeColor = FLinearColor(0.8f, 0.2f, 1.0f, 0.8f); // Roxo
            }
            
            AuroraBridge->SetColorParameter("BridgeColor", BridgeColor);
            
            // Visibilidade baseada no ciclo dia/noite
            bool bIsVisible = (TimeOfDay > 0.4f && TimeOfDay < 0.6f) || // Transição dia/noite
                             (TimeOfDay > 0.9f || TimeOfDay < 0.1f);   // Transição noite/dia
            
            AuroraBridge->SetVisibility(bIsVisible, true);
            
            // Adicionar à lista de atores gerados para limpeza posterior
            GeneratedActors.Add(AuroraBridge);
            
            // Criar colisão para a ponte (invisível, apenas para permitir movimento)
            for (const FVector& LightPoint : LightPoints)
            {
                FActorSpawnParameters SpawnParams;
                SpawnParams.Owner = this;
                
                AStaticMeshActor* BridgeCollision = GetWorld()->SpawnActor<AStaticMeshActor>(
                    LightPoint, FRotator::ZeroRotator, SpawnParams
                );
                
                if (BridgeCollision)
                {
                    // Configurar colisão invisível
                    UStaticMeshComponent* MeshComponent = BridgeCollision->GetStaticMeshComponent();
                    if (MeshComponent)
                    {
                        // Usar uma malha simples para colisão
                        static ConstructorHelpers::FObjectFinder<UStaticMesh> CollisionMeshFinder(TEXT("/Engine/BasicShapes/Cube"));
                        UStaticMesh* CollisionMesh = CollisionMeshFinder.Object;
                        
                        if (CollisionMesh)
                        {
                            MeshComponent->SetStaticMesh(CollisionMesh);
                            MeshComponent->SetWorldScale3D(FVector(0.5f, 0.5f, 0.1f));
                            MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
                            MeshComponent->SetCollisionResponseToAllChannels(ECR_Block);
                            MeshComponent->SetVisibility(false);
                        }
                    }
                    
                    GeneratedActors.Add(BridgeCollision);
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::GenerateVoidRifts()
{
    if (!HasAuthority())
    {
        return;
    }

    int32 RiftCount = FMath::RandRange(
        FAURACRONMapDimensions::VOID_RIFTS_COUNT_MIN,
        FAURACRONMapDimensions::VOID_RIFTS_COUNT_MAX
    );

    FVector EnvironmentCenter = GetActorLocation();
    float EnvironmentRadius = UAURACRONMapMeasurements::GetEnvironmentRadius(static_cast<int32>(EnvironmentType));

    for (int32 i = 0; i < RiftCount; ++i)
    {
        // Posicionar fendas do vazio em locais estratégicos
        float RiftAngle = FMath::RandRange(0.0f, 2.0f * PI);
        float RiftRadius = FMath::RandRange(EnvironmentRadius * 0.2f, EnvironmentRadius * 0.8f);
        
        FVector RiftCenter = EnvironmentCenter + FVector(
            RiftRadius * FMath::Cos(RiftAngle),
            RiftRadius * FMath::Sin(RiftAngle),
            FMath::RandRange(300.0f, 700.0f) // Altura variável
        );
        
        // Determinar tamanho e orientação da fenda
        float RiftSize = FMath::RandRange(200.0f, 500.0f);
        FRotator RiftRotation = FRotator(
            FMath::RandRange(-30.0f, 30.0f),  // Pitch
            FMath::RandRange(0.0f, 360.0f),  // Yaw
            FMath::RandRange(-15.0f, 15.0f)  // Roll
        );
        
        // Criar a fenda principal
        FActorSpawnParameters SpawnParams;
        SpawnParams.Owner = this;
        
        AStaticMeshActor* VoidRift = GetWorld()->SpawnActor<AStaticMeshActor>(
            RiftCenter, RiftRotation, SpawnParams
        );
        
        if (VoidRift)
        {
            // Configurar a malha e materiais da fenda
            UStaticMeshComponent* MeshComp = VoidRift->GetStaticMeshComponent();
            if (MeshComp)
            {
                // Configurar escala baseada no tamanho desejado
                MeshComp->SetWorldScale3D(FVector(RiftSize / 100.0f));
                
                // Adicionar efeitos de partículas e distorção visual
                UNiagaraComponent* RiftEffect = NewObject<UNiagaraComponent>(VoidRift);
                if (RiftEffect)
                {
                    RiftEffect->RegisterComponent();
                    RiftEffect->AttachToComponent(MeshComp, FAttachmentTransformRules::KeepRelativeTransform);
                    // Configurar sistema de partículas para efeito de fenda
                }
            }
            
            GeneratedActors.Add(VoidRift);
            
            // Gerar pequenas fendas satélites ao redor da principal
            int32 SatelliteCount = FMath::RandRange(2, 5);
            for (int32 j = 0; j < SatelliteCount; ++j)
            {
                float SatelliteAngle = FMath::RandRange(0.0f, 2.0f * PI);
                float SatelliteDistance = FMath::RandRange(RiftSize * 0.5f, RiftSize * 1.5f);
                
                FVector SatellitePos = RiftCenter + FVector(
                    SatelliteDistance * FMath::Cos(SatelliteAngle),
                    SatelliteDistance * FMath::Sin(SatelliteAngle),
                    FMath::RandRange(-100.0f, 100.0f) // Variação de altura
                );
                
                FRotator SatelliteRot = RiftRotation + FRotator(
                    FMath::RandRange(-45.0f, 45.0f),
                    FMath::RandRange(-45.0f, 45.0f),
                    FMath::RandRange(-45.0f, 45.0f)
                );
                
                AStaticMeshActor* SatelliteRift = GetWorld()->SpawnActor<AStaticMeshActor>(
                    SatellitePos, SatelliteRot, SpawnParams
                );
                
                if (SatelliteRift)
                {
                    UStaticMeshComponent* SatMeshComp = SatelliteRift->GetStaticMeshComponent();
                    if (SatMeshComp)
                    {
                        // Escala menor para fendas satélites
                        float SatelliteScale = RiftSize * FMath::RandRange(0.2f, 0.4f) / 100.0f;
                        SatMeshComp->SetWorldScale3D(FVector(SatelliteScale));
                    }
                    
                    GeneratedActors.Add(SatelliteRift);
                }
            }
        }
    }
}

// Fim das implementações das funções de geração específicas

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES DE ATUALIZAÇÃO DINÂMICA
// ========================================

void AAURACRONPCGEnvironment::UpdateDynamicElements(float DeltaTime)
{
    // Atualizar elementos dinâmicos com base no tipo de ambiente
    float CurrentTime = GetWorld()->GetTimeSeconds();
    
    // Calcular posição do sol para comportamento dinâmico
    float TimeOfDay = FMath::Fmod(CurrentTime / 3600.0f, 24.0f); // Ciclo de 24 horas
    float SunAngle = (TimeOfDay / 24.0f) * 2.0f * PI;
    float SunElevation = FMath::Sin(SunAngle) * 90.0f; // -90 a +90 graus
    float SunAzimuth = (TimeOfDay / 24.0f) * 360.0f; // 0 a 360 graus
    float SolarIntensity = FMath::Max(0.0f, SunElevation / 90.0f); // 0.0 a 1.0
    float LunarIntensity = FMath::Max(0.0f, -SunElevation / 90.0f); // Inverso do solar
    
    // Aplicar efeitos solares globais via PCG
    SetPCGParameterModern(TEXT("SolarIntensity"), FVector(SolarIntensity, 0.0f, 0.0f), TEXT("SolarSystem"));
    SetPCGParameterModern(TEXT("LunarIntensity"), FVector(LunarIntensity, 0.0f, 0.0f), TEXT("SolarSystem"));
    SetPCGParameterModern(TEXT("SunElevation"), FVector(SunElevation, 0.0f, 0.0f), TEXT("SolarSystem"));
    SetPCGParameterModern(TEXT("SunAzimuth"), FVector(SunAzimuth, 0.0f, 0.0f), TEXT("SolarSystem"));
    SetPCGParameterModern(TEXT("TimeOfDay"), FVector(TimeOfDay, 0.0f, 0.0f), TEXT("SolarSystem"));
    
    // Calcular direção solar para efeitos direcionais
    FVector SunDirection = FVector(
        FMath::Cos(FMath::DegreesToRadians(SunAzimuth)) * FMath::Cos(FMath::DegreesToRadians(SunElevation)),
        FMath::Sin(FMath::DegreesToRadians(SunAzimuth)) * FMath::Cos(FMath::DegreesToRadians(SunElevation)),
        FMath::Sin(FMath::DegreesToRadians(SunElevation))
    );
    SetPCGParameterModern(TEXT("SunDirection"), SunDirection, TEXT("SolarSystem"));
    
    switch (EnvironmentType)
    {
    case EAURACRONEnvironmentType::RadiantPlains:
        // Radiant Plains são mais afetados pela luz solar
        {
            float RadiantMultiplier = 1.0f + (SolarIntensity * 0.5f); // Até 50% mais intenso durante o dia
            SetPCGParameterModern(TEXT("RadiantMultiplier"), FVector(RadiantMultiplier, 0.0f, 0.0f), TEXT("RadiantPlains"));
            
            // Cristalline Plateaus brilham mais durante o dia
            if (bHasCrystallinePlateaus)
            {
                float CrystalGlow = SolarIntensity * 2.0f; // Brilho baseado na intensidade solar
                SetPCGParameterModern(TEXT("CrystalGlow"), FVector(CrystalGlow, 0.0f, 0.0f), TEXT("CrystallinePlateaus"));
            }
            
            if (bHasBreathingForests) UpdateBreathingForests(CurrentTime);
            if (bHasTectonicBridges) UpdateTectonicBridges(CurrentTime);
        }
        break;
        
    case EAURACRONEnvironmentType::ZephyrFirmament:
        // Zephyr Firmament responde aos ventos solares
        {
            float WindIntensity = 0.5f + (SolarIntensity * 0.8f); // Ventos mais fortes durante o dia
            float AuroralActivity = SolarIntensity * LunarIntensity * 4.0f; // Máximo durante crepúsculo
            
            SetPCGParameterModern(TEXT("WindIntensity"), FVector(WindIntensity, 0.0f, 0.0f), TEXT("ZephyrFirmament"));
            SetPCGParameterModern(TEXT("AuroralActivity"), FVector(AuroralActivity, 0.0f, 0.0f), TEXT("ZephyrFirmament"));
            
            if (bHasOrbitalArchipelagos) UpdateOrbitalArchipelagos(CurrentTime);
            if (bHasAuroraBridges) UpdateAuroraBridges(CurrentTime);
            if (bHasCloudFortresses) UpdateCloudFortresses(CurrentTime);
            if (bHasStellarGardens) UpdateStellarGardens(CurrentTime);
        }
        break;
        
    case EAURACRONEnvironmentType::PurgatoryRealm:
        // Purgatory Realm é mais ativo durante a noite
        {
            float ShadowIntensity = LunarIntensity * 1.5f; // Sombras mais intensas à noite
            float SpectralActivity = LunarIntensity * 2.0f; // Atividade espectral noturna
            float TemporalDistortion = 1.0f + (LunarIntensity * 0.3f); // Distorção temporal aumenta à noite
            
            SetPCGParameterModern(TEXT("ShadowIntensity"), FVector(ShadowIntensity, 0.0f, 0.0f), TEXT("PurgatoryRealm"));
            SetPCGParameterModern(TEXT("SpectralActivity"), FVector(SpectralActivity, 0.0f, 0.0f), TEXT("PurgatoryRealm"));
            SetPCGParameterModern(TEXT("TemporalDistortion"), FVector(TemporalDistortion, 0.0f, 0.0f), TEXT("PurgatoryRealm"));
            
            if (bHasTemporalDistortionZones) UpdateTemporalDistortionZones(CurrentTime);
            if (bHasRiversOfSouls) UpdateRiversOfSouls(CurrentTime);
            if (bHasFragmentedStructures) UpdateFragmentedStructures(CurrentTime);
            if (bHasShadowStructures) {
                UpdateShadowNexuses(CurrentTime);
                UpdateTowersOfLamentation(CurrentTime);
                UpdateSpectralGuardian(CurrentTime);
                UpdatePurgatoryAnchor(CurrentTime);
            }
        }
        break;
    }
    
    // Regenerar PCG se houver mudanças significativas na intensidade solar
    static float LastSolarIntensity = -1.0f;
    if (FMath::Abs(SolarIntensity - LastSolarIntensity) > 0.1f)
    {
        if (PCGComponent)
        {
            PCGComponent->Generate();
        }
        LastSolarIntensity = SolarIntensity;
    }
}

void AAURACRONPCGEnvironment::UpdateOrbitalArchipelagos(float Time)
{
    // Atualizar ilhas flutuantes dos arquipélagos orbitais
    for (FFloatingIslandData& IslandData : FloatingIslandData)
    {
        if (IslandData.IslandActor && IslandData.IslandActor->GetStaticMeshComponent())
        {
            // Calcular movimento de flutuação baseado em seno
            float FloatOffset = FMath::Sin((Time + IslandData.TimeOffset) * IslandData.FloatSpeed) * IslandData.FloatHeight;
            
            // Aplicar movimento vertical
            FVector CurrentLocation = IslandData.IslandActor->GetActorLocation();
            FVector NewLocation = FVector(CurrentLocation.X, CurrentLocation.Y, IslandData.OriginalHeight + FloatOffset);
            IslandData.IslandActor->SetActorLocation(NewLocation);
            
            // Aplicar leve rotação para efeito de balanço
            float RotationAmount = FMath::Sin((Time + IslandData.TimeOffset) * IslandData.FloatSpeed * 0.5f) * 0.5f;
            FRotator CurrentRotation = IslandData.IslandActor->GetActorRotation();
            FRotator NewRotation = FRotator(CurrentRotation.Pitch + RotationAmount, CurrentRotation.Yaw, CurrentRotation.Roll + RotationAmount * 0.7f);
            IslandData.IslandActor->SetActorRotation(NewRotation);
        }
    }
}

void AAURACRONPCGEnvironment::UpdateAuroraBridges(float Time)
{
    // Atualizar pontes aurora com efeitos de ondulação e cores dinâmicas
    for (FAuroraBridgeData& BridgeData : AuroraBridgeData)
    {
        if (BridgeData.BridgeActor && IsValid(BridgeData.BridgeActor))
        {
            // Atualizar sistema de partículas se existir
            TArray<UActorComponent*> Components = BridgeData.BridgeActor->GetComponents().Array();
            for (UActorComponent* Component : Components)
            {
                if (UNiagaraComponent* AuroraEffect = Cast<UNiagaraComponent>(Component))
                {
                    // Calcular intensidade baseada no ciclo dia/noite
                    float TimeOfDay = FMath::Frac(Time / 300.0f); // Ciclo de 5 minutos
                    float AuroraIntensity = 0.5f + (ActivityScale * 0.5f);
                    
                    // Intensidade maior durante a noite
                    if (TimeOfDay >= 0.5f) // Noite
                    {
                        AuroraIntensity *= 1.5f;
                    }
                    
                    // Aplicar ondulação baseada em seno
                    float WaveIntensity = FMath::Sin(Time * 0.2f) * 0.3f + 0.7f;
                    AuroraEffect->SetFloatParameter("AuroraIntensity", AuroraIntensity * WaveIntensity);
                    
                    // Variar cores ao longo do tempo
                    float HueShift = FMath::Frac(Time * 0.05f); // Mudança lenta de cor
                    FLinearColor BaseColor = FLinearColor(0.2f, 0.4f, 1.0f); // Azul base
                    FLinearColor ShiftedColor = UAURACRONPCGMathLibrary::ShiftHue(BaseColor, HueShift);
                    AuroraEffect->SetColorParameter("AuroraColor", ShiftedColor);
                    
                    // Ajustar velocidade de fluxo
                    float FlowSpeed = 0.8f + FMath::Sin(Time * 0.1f) * 0.2f;
                    AuroraEffect->SetFloatParameter("FlowSpeed", FlowSpeed);
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::UpdateStellarGardens(float Time)
{
    // Atualizar jardins estelares com rotação e brilho dinâmicos
    for (FStellarGardenData& GardenData : StellarGardenData)
    {
        if (GardenData.GardenActor && IsValid(GardenData.GardenActor))
        {
            // Aplicar rotação lenta
            FRotator CurrentRotation = GardenData.GardenActor->GetActorRotation();
            float RotationSpeed = GardenData.RotationSpeed * ActivityScale;
            FRotator NewRotation = FRotator(CurrentRotation.Pitch, CurrentRotation.Yaw + RotationSpeed * Time * 0.1f, CurrentRotation.Roll);
            GardenData.GardenActor->SetActorRotation(NewRotation);
            
            // Atualizar materiais dinâmicos para efeito de brilho pulsante
            TArray<UActorComponent*> Components = GardenData.GardenActor->GetComponents().Array();
            for (UActorComponent* Component : Components)
            {
                if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                {
                    if (UMaterialInstanceDynamic* DynamicMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                    {
                        // Calcular brilho pulsante
                        float GlowIntensity = 0.8f + FMath::Sin(Time * GardenData.PulseSpeed) * 0.2f;
                        GlowIntensity *= ActivityScale;
                        
                        // Aplicar parâmetros
                        DynamicMaterial->SetScalarParameterValue(TEXT("GlowIntensity"), GlowIntensity);
                        
                        // Variar cor ao longo do tempo
                        float HueShift = FMath::Frac(Time * 0.02f); // Mudança muito lenta de cor
                        FLinearColor BaseColor = FLinearColor(0.8f, 0.9f, 1.0f); // Azul claro base
                        FLinearColor ShiftedColor = UAURACRONPCGMathLibrary::ShiftHue(BaseColor, HueShift);
                        DynamicMaterial->SetVectorParameterValue(TEXT("GlowColor"), ShiftedColor);
                    }
                }
                
                // Atualizar sistemas de partículas
                if (UNiagaraComponent* StarEffect = Cast<UNiagaraComponent>(Component))
                {
                    // Ajustar taxa de spawn baseada na atividade
                    float SpawnRate = 10.0f + (ActivityScale * 20.0f);
                    StarEffect->SetFloatParameter("SpawnRate", SpawnRate);
                    
                    // Ajustar brilho
                    float StarBrightness = 0.7f + FMath::Sin(Time * 0.3f) * 0.3f;
                    StarBrightness *= ActivityScale;
                    StarEffect->SetFloatParameter("StarBrightness", StarBrightness);
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::UpdateBreathingForests(float Time)
{
    // Atualizar florestas respirantes com movimento de "respiração"
    for (FBreathingForestData& ForestData : BreathingForestData)
    {
        if (ForestData.TreeActor && IsValid(ForestData.TreeActor))
        {
            // Calcular ciclo de respiração
            float BreathCycle = FMath::Sin(Time * ForestData.BreathSpeed) * 0.5f + 0.5f; // Normalizado entre 0 e 1
            
            // Aplicar escala para simular respiração
            float BaseScale = ForestData.OriginalScale;
            float BreathScale = BaseScale * (1.0f + (BreathCycle * ForestData.BreathAmount * ActivityScale));
            FVector NewScale = FVector(BreathScale, BreathScale, BreathScale * (1.0f + BreathCycle * 0.1f));
            ForestData.TreeActor->SetActorScale3D(NewScale);
            
            // Atualizar materiais para efeito de "vida"
            TArray<UActorComponent*> Components = ForestData.TreeActor->GetComponents().Array();
            for (UActorComponent* Component : Components)
            {
                if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                {
                    if (UMaterialInstanceDynamic* DynamicMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                    {
                        // Pulsar cor baseado no ciclo de respiração
                        float ColorIntensity = 0.8f + (BreathCycle * 0.2f);
                        DynamicMaterial->SetScalarParameterValue(TEXT("EmissiveIntensity"), ColorIntensity * ActivityScale);
                        
                        // Ajustar cor baseado no ciclo dia/noite
                        float TimeOfDay = FMath::Frac(Time / 300.0f); // Ciclo de 5 minutos
                        FLinearColor EmissiveColor;
                        
                        if (TimeOfDay < 0.5f) // Dia
                        {
                            EmissiveColor = FLinearColor(0.2f, 0.8f, 0.3f, 1.0f); // Verde mais claro
                        }
                        else // Noite
                        {
                            EmissiveColor = FLinearColor(0.1f, 0.5f, 0.2f, 1.0f); // Verde mais escuro
                        }
                        
                        DynamicMaterial->SetVectorParameterValue(TEXT("EmissiveColor"), EmissiveColor);
                    }
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::UpdateRiversOfSouls(float Time)
{
    // Atualizar rios de almas com fluxo dinâmico
    for (FRiverOfSoulsData& RiverData : RiversOfSoulsData)
    {
        if (RiverData.RiverActor && IsValid(RiverData.RiverActor))
        {
            // Atualizar sistema de partículas para fluxo de almas
            TArray<UActorComponent*> Components = RiverData.RiverActor->GetComponents().Array();
            for (UActorComponent* Component : Components)
            {
                if (UNiagaraComponent* SoulEffect = Cast<UNiagaraComponent>(Component))
                {
                    // Ajustar velocidade de fluxo baseado na atividade
                    float FlowSpeed = RiverData.BaseFlowSpeed * (0.8f + ActivityScale * 0.4f);
                    SoulEffect->SetFloatParameter("FlowSpeed", FlowSpeed);
                    
                    // Ajustar densidade de almas
                    float SoulDensity = RiverData.BaseSoulDensity * ActivityScale;
                    SoulEffect->SetFloatParameter("SoulDensity", SoulDensity);
                    
                    // Ajustar brilho baseado no ciclo dia/noite
                    float TimeOfDay = FMath::Frac(Time / 300.0f); // Ciclo de 5 minutos
                    float SoulBrightness;
                    
                    if (TimeOfDay < 0.5f) // Dia
                    {
                        SoulBrightness = 0.7f; // Menos brilhante durante o dia
                    }
                    else // Noite
                    {
                        SoulBrightness = 1.2f; // Mais brilhante durante a noite
                    }
                    
                    SoulEffect->SetFloatParameter("SoulBrightness", SoulBrightness * ActivityScale);
                    
                    // Variar cor ao longo do tempo
                    float HueShift = FMath::Frac(Time * 0.01f); // Mudança muito lenta de cor
                    FLinearColor BaseColor = FLinearColor(0.5f, 0.8f, 1.0f); // Azul espectral base
                    FLinearColor ShiftedColor = UAURACRONPCGMathLibrary::ShiftHue(BaseColor, HueShift);
                    SoulEffect->SetColorParameter("SoulColor", ShiftedColor);
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::UpdateFragmentedStructures(float Time)
{
    // Atualizar estruturas fragmentadas com movimento de flutuação e rotação
    for (FFragmentedStructureData& StructureData : FragmentedStructureData)
    {
        if (StructureData.StructureActor && IsValid(StructureData.StructureActor))
        {
            // Calcular movimento de flutuação baseado em seno
            float FloatOffset = FMath::Sin((Time + StructureData.TimeOffset) * StructureData.FloatSpeed) * StructureData.FloatHeight;
            
            // Aplicar movimento vertical
            FVector CurrentLocation = StructureData.StructureActor->GetActorLocation();
            FVector NewLocation = FVector(CurrentLocation.X, CurrentLocation.Y, StructureData.OriginalHeight + FloatOffset);
            StructureData.StructureActor->SetActorLocation(NewLocation);
            
            // Aplicar rotação lenta
            FRotator CurrentRotation = StructureData.StructureActor->GetActorRotation();
            float RotationAmount = FMath::Sin((Time + StructureData.TimeOffset) * StructureData.FloatSpeed * 0.3f) * 0.8f;
            FRotator NewRotation = FRotator(
                CurrentRotation.Pitch + RotationAmount * StructureData.RotationFactor.X,
                CurrentRotation.Yaw + (Time * StructureData.RotationSpeed * 0.1f),
                CurrentRotation.Roll + RotationAmount * StructureData.RotationFactor.Z
            );
            StructureData.StructureActor->SetActorRotation(NewRotation);
            
            // Atualizar materiais para efeito de fragmentação
            TArray<UActorComponent*> Components = StructureData.StructureActor->GetComponents().Array();
            for (UActorComponent* Component : Components)
            {
                if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                {
                    if (UMaterialInstanceDynamic* DynamicMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                    {
                        // Ajustar dissolução baseada na atividade
                        float DissolveAmount = 0.2f + (1.0f - ActivityScale) * 0.3f;
                        DynamicMaterial->SetScalarParameterValue(TEXT("DissolveAmount"), DissolveAmount);
                        
                        // Ajustar brilho das bordas
                        float EdgeGlow = 0.5f + FMath::Sin(Time * 0.4f) * 0.3f;
                        EdgeGlow *= ActivityScale;
                        DynamicMaterial->SetScalarParameterValue(TEXT("EdgeGlowIntensity"), EdgeGlow);
                        
                        // Ajustar cor baseado no ciclo dia/noite
                        float TimeOfDay = FMath::Frac(Time / 300.0f); // Ciclo de 5 minutos
                        FLinearColor GlowColor;
                        
                        if (TimeOfDay < 0.5f) // Dia
                        {
                            GlowColor = FLinearColor(0.8f, 0.3f, 0.1f, 1.0f); // Laranja-avermelhado
                        }
                        else // Noite
                        {
                            GlowColor = FLinearColor(0.5f, 0.1f, 0.3f, 1.0f); // Roxo-avermelhado
                        }
                        
                        DynamicMaterial->SetVectorParameterValue(TEXT("EdgeGlowColor"), GlowColor);
                    }
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::UpdateTemporalDistortionZones(float Time)
{
    // Atualizar zonas de distorção temporal com efeitos de ondulação e distorção
    for (FTemporalDistortionData& DistortionData : TemporalDistortionData)
    {
        if (DistortionData.DistortionActor && IsValid(DistortionData.DistortionActor))
        {
            // Calcular intensidade de distorção baseada em seno
            float DistortionIntensity = 0.5f + FMath::Sin(Time * DistortionData.PulseSpeed) * 0.3f;
            DistortionIntensity *= ActivityScale;
            
            // Atualizar materiais para efeito de distorção
            TArray<UActorComponent*> Components = DistortionData.DistortionActor->GetComponents().Array();
            for (UActorComponent* Component : Components)
            {
                if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
                {
                    if (UMaterialInstanceDynamic* DynamicMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0)))
                    {
                        // Aplicar intensidade de distorção
                        DynamicMaterial->SetScalarParameterValue(TEXT("DistortionIntensity"), DistortionIntensity);
                        
                        // Ajustar velocidade de distorção
                        float DistortionSpeed = 0.5f + FMath::Sin(Time * 0.2f) * 0.2f;
                        DynamicMaterial->SetScalarParameterValue(TEXT("DistortionSpeed"), DistortionSpeed);
                        
                        // Ajustar cor baseado no tempo
                        float HueShift = FMath::Frac(Time * 0.03f); // Mudança lenta de cor
                        FLinearColor BaseColor = FLinearColor(0.3f, 0.1f, 0.5f); // Roxo base
                        FLinearColor ShiftedColor = UAURACRONPCGMathLibrary::ShiftHue(BaseColor, HueShift);
                        DynamicMaterial->SetVectorParameterValue(TEXT("DistortionColor"), ShiftedColor);
                    }
                }
                
                // Atualizar sistemas de partículas
                if (UNiagaraComponent* DistortionEffect = Cast<UNiagaraComponent>(Component))
                {
                    // Ajustar intensidade do efeito
                    DistortionEffect->SetFloatParameter("EffectIntensity", DistortionIntensity);
                    
                    // Ajustar velocidade de rotação
                    float RotationSpeed = 0.3f + FMath::Sin(Time * 0.15f) * 0.2f;
                    DistortionEffect->SetFloatParameter("RotationSpeed", RotationSpeed);
                    
                    // Ajustar cor do efeito
                    float TimeOfDay = FMath::Frac(Time / 300.0f); // Ciclo de 5 minutos
                    FLinearColor EffectColor;
                    
                    if (TimeOfDay < 0.5f) // Dia
                    {
                        EffectColor = FLinearColor(0.4f, 0.2f, 0.6f, 0.7f); // Roxo mais claro
                    }
                    else // Noite
                    {
                        EffectColor = FLinearColor(0.2f, 0.1f, 0.4f, 0.9f); // Roxo mais escuro e opaco
                    }
                    
                    DistortionEffect->SetColorParameter("EffectColor", EffectColor);
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::UpdateCloudFortresses(float Time)
{
    // Atualizar fortalezas de nuvem com movimento de deriva circular e flutuação vertical
    for (FCloudFortressData& FortressData : CloudFortressData)
    {
        if (FortressData.FortressActor && IsValid(FortressData.FortressActor))
        {
            // Calcular movimento de deriva circular
            float DriftAngle = (Time + FortressData.TimeOffset) * FortressData.DriftSpeed;
            
            // Calcular nova posição com base no movimento circular
            FVector DriftOffset = FVector(
                FortressData.DriftRadius * FMath::Cos(DriftAngle),
                FortressData.DriftRadius * FMath::Sin(DriftAngle),
                0.0f
            );
            
            // Calcular movimento de flutuação vertical
            float VerticalOffset = FortressData.VerticalAmplitude * FMath::Sin((Time + FortressData.TimeOffset) * FortressData.VerticalSpeed);
            
            // Aplicar movimento combinado
            FVector NewLocation = FortressData.OriginalPosition + DriftOffset + FVector(0.0f, 0.0f, VerticalOffset);
            FortressData.FortressActor->SetActorLocation(NewLocation);
            
            // Aplicar leve rotação para efeito de balanço
            float RotationAmount = FMath::Sin((Time + FortressData.TimeOffset) * FortressData.VerticalSpeed * 0.5f) * 2.0f;
            FRotator CurrentRotation = FortressData.FortressActor->GetActorRotation();
            FRotator NewRotation = FRotator(CurrentRotation.Pitch + RotationAmount, CurrentRotation.Yaw, CurrentRotation.Roll + RotationAmount * 0.7f);
            FortressData.FortressActor->SetActorRotation(NewRotation);
            
            // Atualizar efeitos de partículas se existirem
            TArray<UActorComponent*> Components = FortressData.FortressActor->GetComponents().Array();
            for (UActorComponent* Component : Components)
            {
                if (UNiagaraComponent* CloudEffect = Cast<UNiagaraComponent>(Component))
                {
                    // Ajustar intensidade do efeito de nuvem com base na atividade
                    float CloudIntensity = 0.5f + (ActivityScale * 0.5f);
                    CloudEffect->SetFloatParameter("CloudIntensity", CloudIntensity);
                    
                    // Ajustar cor com base no ciclo dia/noite (simulado com tempo do mundo)
                    float TimeOfDay = FMath::Frac(Time / 300.0f); // Ciclo de 5 minutos
                    FLinearColor CloudColor;
                    
                    if (TimeOfDay < 0.5f) // Dia
                    {
                        CloudColor = FLinearColor(1.0f, 1.0f, 1.0f, 0.8f); // Branco
                    }
                    else // Noite
                    {
                        CloudColor = FLinearColor(0.7f, 0.8f, 1.0f, 0.8f); // Azul claro
                    }
                    
                    CloudEffect->SetColorParameter("CloudColor", CloudColor);
                }
            }
        }
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGEnvironment::RegisterWithEnvironmentManager()
{
    // Buscar o Environment Manager no mundo usando as APIs modernas do UE 5.6
    if (UWorld* World = GetWorld())
    {
        // Usar o sistema de subsistemas moderno do UE 5.6
        if (UAURACRONPCGSubsystem* PCGSubsystem = World->GetSubsystem<UAURACRONPCGSubsystem>())
        {
            // Buscar o Environment Manager através do subsistema
            TArray<AActor*> FoundManagers;
            UGameplayStatics::GetAllActorsOfClass(World, AAURACRONPCGEnvironmentManager::StaticClass(), FoundManagers);

            if (FoundManagers.Num() > 0)
            {
                if (AAURACRONPCGEnvironmentManager* Manager = Cast<AAURACRONPCGEnvironmentManager>(FoundManagers[0]))
                {
                    // Registrar esta instância usando API moderna
                    Manager->RegisterEnvironmentInstance(this);

                    // Log usando sistema moderno do UE 5.6
                    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::RegisterWithEnvironmentManager - Environment %s registered successfully"), *GetName());
                }
            }
            else
            {
                // Criar um Environment Manager se não existir (padrão moderno UE 5.6)
                FActorSpawnParameters SpawnParams;
                SpawnParams.Name = FName(TEXT("AURACRONPCGEnvironmentManager"));
                SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;

                if (AAURACRONPCGEnvironmentManager* NewManager = World->SpawnActor<AAURACRONPCGEnvironmentManager>(SpawnParams))
                {
                    NewManager->RegisterEnvironmentInstance(this);
                    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::RegisterWithEnvironmentManager - Created new manager and registered environment %s"), *GetName());
                }
            }
        }
    }
}

void AAURACRONPCGEnvironment::ActivateEnvironment()
{
    // Ativar usando APIs modernas do UE 5.6
    if (PCGComponent && IsValid(PCGComponent))
    {
        // Usar a API moderna de ativação do PCG Component
        PCGComponent->SetComponentTickEnabled(true);

        // Ativar geração procedural usando API moderna
        if (PCGComponent->GetGraph())
        {
            // Executar geração usando sistema moderno do UE 5.6
            PCGComponent->GenerateLocal(true);

            // Marcar como ativo
            bIsActive = true;

            // Aplicar configurações de qualidade baseadas na performance
            ApplyQualitySettings();

            // Log usando sistema moderno
            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::ActivateEnvironment - Environment %s activated with modern UE 5.6 APIs"), *GetName());
        }
    }

    // Ativar componentes relacionados usando APIs modernas
    TArray<UActorComponent*> Components = GetComponents().Array();
    for (UActorComponent* Component : Components)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            MeshComp->SetComponentTickEnabled(true);
            MeshComp->SetVisibility(true);
        }
    }
}

void AAURACRONPCGEnvironment::DeactivateEnvironment()
{
    // Desativar usando APIs modernas do UE 5.6
    if (PCGComponent && IsValid(PCGComponent))
    {
        // Usar a API moderna de desativação do PCG Component
        PCGComponent->SetComponentTickEnabled(false);

        // Limpar geração procedural usando API moderna
        PCGComponent->CleanupLocal(true);

        // Marcar como inativo
        bIsActive = false;

        // Log usando sistema moderno
        UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::DeactivateEnvironment - Environment %s deactivated with modern UE 5.6 APIs"), *GetName());
    }

    // Desativar componentes relacionados usando APIs modernas
    TArray<UActorComponent*> Components = GetComponents().Array();
    for (UActorComponent* Component : Components)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            MeshComp->SetComponentTickEnabled(false);
            MeshComp->SetVisibility(false);
        }
    }
}

void AAURACRONPCGEnvironment::ApplyTransitionEffect(float TransitionAlpha, bool bFadeIn)
{
    // Aplicar efeitos de transição usando APIs modernas do UE 5.6
    if (!IsValid(this))
    {
        return;
    }

    // Calcular alpha efetivo baseado na direção da transição
    float EffectiveAlpha = bFadeIn ? TransitionAlpha : (1.0f - TransitionAlpha);

    // Aplicar transição aos componentes usando APIs modernas
    TArray<UActorComponent*> Components = GetComponents().Array();
    for (UActorComponent* Component : Components)
    {
        if (UStaticMeshComponent* MeshComp = Cast<UStaticMeshComponent>(Component))
        {
            // Usar API moderna de materiais dinâmicos do UE 5.6
            if (UMaterialInterface* Material = MeshComp->GetMaterial(0))
            {
                if (UMaterialInstanceDynamic* DynamicMaterial = MeshComp->CreateAndSetMaterialInstanceDynamic(0))
                {
                    // Aplicar parâmetros de transição usando API moderna
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("TransitionAlpha")), EffectiveAlpha);
                    DynamicMaterial->SetScalarParameterValue(FName(TEXT("Opacity")), EffectiveAlpha);

                    // Aplicar efeitos baseados no tipo de ambiente
                    switch (EnvironmentType)
                    {
                        case EAURACRONEnvironmentType::RadiantPlains:
                            DynamicMaterial->SetVectorParameterValue(FName(TEXT("RadiantGlow")), FLinearColor(1.0f, 0.9f, 0.7f, EffectiveAlpha));
                            break;
                        case EAURACRONEnvironmentType::ZephyrFirmament:
                            DynamicMaterial->SetVectorParameterValue(FName(TEXT("ZephyrFlow")), FLinearColor(0.7f, 0.9f, 1.0f, EffectiveAlpha));
                            if (bHasVoidRifts) {
                                DynamicMaterial->SetVectorParameterValue(FName(TEXT("VoidDistortion")), FLinearColor(0.3f, 0.1f, 0.5f, EffectiveAlpha * 0.8f));
                            }
                            break;
                        case EAURACRONEnvironmentType::PurgatoryRealm:
                            DynamicMaterial->SetVectorParameterValue(FName(TEXT("PurgatoryAura")), FLinearColor(0.9f, 0.3f, 0.3f, EffectiveAlpha));
                            break;
                        default:
                            DynamicMaterial->SetVectorParameterValue(FName(TEXT("DefaultGlow")), FLinearColor(1.0f, 1.0f, 1.0f, EffectiveAlpha));
                            break;
                    }
                }
            }
        }
    }

    // Aplicar transição ao PCG Component usando APIs modernas
    if (PCGComponent && IsValid(PCGComponent))
    {
        // Usar sistema moderno de parâmetros dinâmicos do UE 5.6
        if (PCGComponent->GetGraph())
        {
            // Aplicar parâmetros de transição ao grafo PCG
            SetPCGParameterModern(TEXT("TransitionAlpha"), FVector(EffectiveAlpha, 0.0f, 0.0f), TEXT("TransitionEffect"));
            SetPCGParameterModern(TEXT("FadeDirection"), FVector(bFadeIn ? 1.0f : 0.0f, 0.0f, 0.0f), TEXT("TransitionEffect"));
        }
    }

    // Log usando sistema moderno
    UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironment::ApplyTransitionEffect - Applied transition alpha %.2f (FadeIn: %s) to environment %s"),
           EffectiveAlpha, bFadeIn ? TEXT("true") : TEXT("false"), *GetName());
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES DE GERAÇÃO ESPECÍFICAS - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGEnvironment::GenerateCloudFortresses()
{
    // Verificar autoridade de rede e componente PCG
    if (!HasAuthority() || !PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::GenerateCloudFortresses - Invalid state or no authority"));
        return;
    }

    // Limpar dados anteriores
    CloudFortressData.Empty();

    // Usar sistema moderno de geração procedural do UE 5.6
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    float CloudHeight = 2000.0f; // 20 metros acima do solo

    // Gerar posições para fortalezas usando algoritmo moderno
    TArray<FVector> FortressPositions = UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
        MapCenter + FVector(0.0f, 0.0f, CloudHeight),
        5000.0f, // Raio de 50 metros
        1000.0f, // Distância mínima de 10 metros entre fortalezas
        30, // Máximo de tentativas
        FMath::Rand() // Seed aleatório
    );

    // Determinar características baseadas na fase do mapa
    FLinearColor FortressGlowColor = FLinearColor(1.0f, 0.8f, 0.6f); // Cor padrão
    float PhaseIntensity = 1.0f;

    if (UWorld* World = GetWorld())
    {
        if (UAURACRONPCGSubsystem* PCGSubsystem = World->GetSubsystem<UAURACRONPCGSubsystem>())
        {
            EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();

            switch (CurrentPhase)
            {
                case EAURACRONMapPhase::Awakening:
                    PhaseIntensity = 0.7f;
                    FortressGlowColor = FLinearColor(1.0f, 0.8f, 0.6f); // Dourado suave
                    break;
                case EAURACRONMapPhase::Convergence:
                    PhaseIntensity = 1.0f;
                    FortressGlowColor = FLinearColor(1.0f, 1.0f, 1.0f); // Branco brilhante
                    break;
                case EAURACRONMapPhase::Intensification:
                    PhaseIntensity = 0.8f;
                    FortressGlowColor = FLinearColor(1.0f, 0.6f, 0.4f); // Laranja intenso
                    break;
                case EAURACRONMapPhase::Resolution:
                    PhaseIntensity = 0.5f;
                    FortressGlowColor = FLinearColor(0.4f, 0.6f, 1.0f); // Azul calmo
                    break;
            }
        }
    }

    // Criar fortalezas nas nuvens em cada posição gerada
    for (int32 i = 0; i < FortressPositions.Num(); i++)
    {
        // Calcular posição com variação de altura
        FVector FortressPosition = FortressPositions[i];
        FortressPosition.Z += FMath::RandRange(-200.0f, 200.0f); // Variação de altura

        // Criar ator de malha estática para a fortaleza
        AStaticMeshActor* FortressActor = GetWorld()->SpawnActor<AStaticMeshActor>(AStaticMeshActor::StaticClass(), 
                                                                                 FTransform(FortressPosition));
        if (FortressActor)
        {
            // Configurar nome único
            FortressActor->SetActorLabel(FString::Printf(TEXT("CloudFortress_%d"), i));

            // Configurar malha
            UStaticMeshComponent* MeshComponent = FortressActor->GetStaticMeshComponent();
            if (MeshComponent)
            {
                // Carregar malha de fortaleza
                static ConstructorHelpers::FObjectFinder<UStaticMesh> FortressMeshFinder(TEXT("/Game/AURACRON/Meshes/Environment/ZephyrFirmament/SM_CloudFortress"));
                if (FortressMeshFinder.Succeeded())
                {
                    MeshComponent->SetStaticMesh(FortressMeshFinder.Object);
                }
                else
                {
                    // Fallback para cubo básico se a malha não for encontrada
                    static ConstructorHelpers::FObjectFinder<UStaticMesh> FallbackMeshFinder(TEXT("/Engine/BasicShapes/Cube"));
                    if (FallbackMeshFinder.Succeeded())
                    {
                        MeshComponent->SetStaticMesh(FallbackMeshFinder.Object);
                    }
                }
                
                // Configurar escala aleatória
                float BaseScale = FMath::RandRange(0.8f, 1.2f);
                FortressActor->SetActorScale3D(FVector(BaseScale, BaseScale, BaseScale * FMath::RandRange(0.9f, 1.1f)));
                
                // Configurar rotação aleatória
                FRotator RandomRotation(0.0f, FMath::RandRange(0.0f, 360.0f), 0.0f);
                FortressActor->SetActorRotation(RandomRotation);
                
                // Configurar material dinâmico
                UMaterialInterface* BaseMaterial = MeshComponent->GetMaterial(0);
                if (BaseMaterial)
                {
                    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, FortressActor);
                    if (DynamicMaterial)
                    {
                        // Configurar parâmetros do material
                        DynamicMaterial->SetVectorParameterValue(TEXT("GlowColor"), FortressGlowColor);
                        DynamicMaterial->SetScalarParameterValue(TEXT("GlowIntensity"), PhaseIntensity);
                        DynamicMaterial->SetScalarParameterValue(TEXT("CloudOpacity"), FMath::RandRange(0.7f, 0.9f));
                        
                        // Aplicar material dinâmico
                        MeshComponent->SetMaterial(0, DynamicMaterial);
                    }
                }
                
                // Configurar física
                MeshComponent->SetSimulatePhysics(false);
                MeshComponent->SetEnableGravity(false);
                MeshComponent->SetCollisionProfileName(TEXT("BlockAllDynamic"));
                
                // Adicionar efeito de nuvem (partículas)
                UNiagaraComponent* CloudEffect = NewObject<UNiagaraComponent>(FortressActor);
                if (CloudEffect)
                {
                    CloudEffect->RegisterComponent();
                    CloudEffect->AttachToComponent(MeshComponent, FAttachmentTransformRules::KeepRelativeTransform);
                    
                    // Configurar sistema de partículas
                    static ConstructorHelpers::FObjectFinder<UNiagaraSystem> CloudSystemFinder(TEXT("/Game/AURACRON/FX/Environment/NS_CloudAmbient"));
                    if (CloudSystemFinder.Succeeded())
                    {
                        CloudEffect->SetAsset(CloudSystemFinder.Object);
                        CloudEffect->SetColorParameter(TEXT("CloudColor"), FortressGlowColor);
                        CloudEffect->SetFloatParameter(TEXT("CloudDensity"), FMath::RandRange(0.8f, 1.2f));
                    }
                }
                
                // Criar dados para movimento de deriva
                FCloudFortressData FortressData;
                FortressData.FortressActor = FortressActor;
                FortressData.OriginalPosition = FortressPosition;
                FortressData.DriftRadius = FMath::RandRange(200.0f, 400.0f);
                FortressData.DriftSpeed = FMath::RandRange(0.1f, 0.3f);
                FortressData.TimeOffset = FMath::RandRange(0.0f, 2.0f * PI);
                FortressData.VerticalAmplitude = FMath::RandRange(30.0f, 70.0f);
                FortressData.VerticalSpeed = FMath::RandRange(0.3f, 0.7f);
                
                // Adicionar à lista de fortalezas
                CloudFortressData.Add(FortressData);
                
                // Adicionar à lista de atores gerados
                GeneratedActors.Add(FortressActor);
            }
                if (FortressMeshFinder.Succeeded())
                {
                    MeshComponent->SetStaticMesh(FortressMeshFinder.Object);
                }

                // Configurar escala aleatória para variedade
                float BaseScale = FMath::RandRange(5.0f, 8.0f);
                float HeightScale = FMath::RandRange(3.0f, 5.0f);
                MeshComponent->SetRelativeScale3D(FVector(BaseScale, BaseScale, HeightScale));

                // Configurar rotação aleatória
                FRotator RandomRotation(0.0f, FMath::RandRange(0.0f, 360.0f), 0.0f);
                FortressActor->SetActorRotation(RandomRotation);

                // Criar material dinâmico para efeitos visuais
                UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial"));
                if (BaseMaterial)
                {
                    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, FortressActor);
                    if (DynamicMaterial)
                    {
                        // Aplicar cor baseada na fase do mapa
                        DynamicMaterial->SetVectorParameterValue("Color", FortressGlowColor);
                        DynamicMaterial->SetScalarParameterValue("Metallic", 0.8f);
                        DynamicMaterial->SetScalarParameterValue("Roughness", 0.2f);
                        DynamicMaterial->SetScalarParameterValue("Emissive", PhaseIntensity);
                        
                        // Aplicar material à malha
                        MeshComponent->SetMaterial(0, DynamicMaterial);
                    }
                }

                // Configurar física
                MeshComponent->SetSimulatePhysics(false);
                MeshComponent->SetEnableGravity(false);
                MeshComponent->SetCollisionProfileName(TEXT("BlockAllDynamic"));

                // Adicionar efeito de nuvem usando sistema de partículas Niagara
                UNiagaraComponent* CloudEffect = UNiagaraFunctionLibrary::SpawnSystemAttached(
                    LoadObject<UNiagaraSystem>(nullptr, TEXT("/Engine/BasicEffects/System/NS_Smoke")),
                    MeshComponent,
                    NAME_None,
                    FVector(0.0f, 0.0f, -100.0f), // Offset abaixo da fortaleza
                    FRotator::ZeroRotator,
                    EAttachLocation::KeepRelativeOffset,
                    true
                );

                if (CloudEffect)
                {
                    // Configurar efeito de nuvem
                    CloudEffect->SetVariableVec3("User.CloudColor", FortressGlowColor.ToFVector3f());
                    CloudEffect->SetVariableFloat("User.CloudSize", BaseScale * 3.0f);
                    CloudEffect->SetVariableFloat("User.CloudDensity", 0.5f * PhaseIntensity);
                }

                // Adicionar movimento de deriva
                FortressActor->SetMobility(EComponentMobility::Movable);
                
                // Armazenar dados para movimento de deriva
                FFloatingIslandData FortressData;
                FortressData.IslandActor = FortressActor;
                FortressData.OriginalHeight = FortressPosition.Z;
                FortressData.FloatHeight = FMath::RandRange(20.0f, 50.0f); // Amplitude de flutuação
                FortressData.FloatSpeed = FMath::RandRange(0.1f, 0.3f); // Velocidade de flutuação
                FortressData.TimeOffset = FMath::RandRange(0.0f, 2.0f * PI); // Offset de tempo para movimento não sincronizado
                
                // Adicionar à lista de ilhas flutuantes para animação
                FloatingIslandData.Add(FortressData);
            }

            // Adicionar à lista de atores gerados
            GeneratedActors.Add(FortressActor);
        }
    }

    // Aplicar parâmetros ao PCG para efeitos adicionais
    SetPCGParameterModern(TEXT("FortressPositions"), MapCenter + FVector(0.0f, 0.0f, CloudHeight), TEXT("CloudFortresses"));
    SetPCGParameterModern(TEXT("FortressCount"), FVector(FortressPositions.Num(), 0.0f, 0.0f), TEXT("CloudFortresses"));
    SetPCGParameterModern(TEXT("CloudHeight"), FVector(CloudHeight, 0.0f, 0.0f), TEXT("CloudFortresses"));
    SetPCGParameterModern(TEXT("PhaseIntensity"), FVector(PhaseIntensity, 0.0f, 0.0f), TEXT("CloudFortresses"));
    SetPCGParameterModern(TEXT("FortressGlow"), FortressGlowColor.ToFVector(), TEXT("CloudFortresses"));

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateCloudFortresses - Generated %d cloud fortresses"), FortressPositions.Num());
}

void AAURACRONPCGEnvironment::GenerateStellarGardens()
{
    // Gerar jardins estelares usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::GenerateStellarGardens - PCGComponent is invalid"));
        return;
    }
    
    // Limpar dados anteriores
    StellarGardenData.Empty();
    
    // Usar sistema moderno de geração procedural do UE 5.6
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    float GardenHeight = 1800.0f; // Altura dos jardins estelares
    
    // Gerar posições para jardins estelares usando distribuição de Poisson
    int32 NumGardens = FMath::RandRange(3, 7); // Número de jardins estelares
    
    TArray<FVector> GardenPositions = UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
        MapCenter + FVector(0.0f, 0.0f, GardenHeight),
        4000.0f, // Raio de distribuição
        1200.0f, // Distância mínima entre jardins
        30,      // Máximo de tentativas
        FMath::Rand() // Seed aleatório
    );
    
    // Limitar ao número desejado de jardins
    if (GardenPositions.Num() > NumGardens)
    {
        GardenPositions.SetNum(NumGardens);
    }
    
    // Determinar características baseadas na fase do mapa
    FLinearColor GardenGlowColor = FLinearColor(0.2f, 0.8f, 1.0f); // Cor padrão azul celeste
    float PhaseIntensity = 1.0f;
    
    if (UWorld* World = GetWorld())
    {
        if (UAURACRONPCGSubsystem* PCGSubsystem = World->GetSubsystem<UAURACRONPCGSubsystem>())
        {
            EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
            
            // Ajustar cor e intensidade com base na fase do mapa
            switch (CurrentPhase)
            {
                case EAURACRONMapPhase::EarlyGame:
                    GardenGlowColor = FLinearColor(0.2f, 0.8f, 1.0f); // Azul celeste
                    PhaseIntensity = 0.7f;
                    break;
                    
                case EAURACRONMapPhase::MidGame:
                    GardenGlowColor = FLinearColor(0.5f, 0.9f, 1.0f); // Azul mais brilhante
                    PhaseIntensity = 1.0f;
                    break;
                    
                case EAURACRONMapPhase::LateGame:
                    GardenGlowColor = FLinearColor(0.7f, 1.0f, 1.0f); // Azul intenso
                    PhaseIntensity = 1.3f;
                    break;
                    
                case EAURACRONMapPhase::FinalBattle:
                    GardenGlowColor = FLinearColor(0.9f, 1.0f, 1.0f); // Quase branco
                    PhaseIntensity = 1.5f;
                    break;
                    
                default:
                    break;
            }
        }
    }
    
    // Criar jardins estelares
    for (const FVector& Position : GardenPositions)
    {
        // Criar ator de malha estática para o jardim
        FActorSpawnParameters SpawnParams;
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
        
        AStaticMeshActor* GardenActor = GetWorld()->SpawnActor<AStaticMeshActor>(
            Position,
            FRotator::ZeroRotator,
            SpawnParams
        );
        
        if (GardenActor)
        {
            // Configurar a malha do jardim estelar
            UStaticMeshComponent* MeshComp = GardenActor->GetStaticMeshComponent();
            if (MeshComp)
            {
                // Carregar malha para o jardim estelar
                static ConstructorHelpers::FObjectFinder<UStaticMesh> GardenMeshFinder(TEXT("/Game/AURACRON/Environment/ZephyrFirmament/Meshes/SM_StellarGarden"));
                if (GardenMeshFinder.Succeeded())
                {
                    MeshComp->SetStaticMesh(GardenMeshFinder.Object);
                }
                
                // Configurar material com efeito de brilho
                static ConstructorHelpers::FObjectFinder<UMaterialInterface> GardenMaterialFinder(TEXT("/Game/AURACRON/Environment/ZephyrFirmament/Materials/M_StellarGarden"));
                if (GardenMaterialFinder.Succeeded())
                {
                    MeshComp->SetMaterial(0, GardenMaterialFinder.Object);
                    
                    // Criar instância de material dinâmica
                    UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(GardenMaterialFinder.Object, GardenActor);
                    if (DynamicMaterial)
                    {
                        // Configurar parâmetros do material
                        DynamicMaterial->SetVectorParameterValue(TEXT("GlowColor"), GardenGlowColor);
                        DynamicMaterial->SetScalarParameterValue(TEXT("GlowIntensity"), PhaseIntensity);
                        
                        MeshComp->SetMaterial(0, DynamicMaterial);
                    }
                }
                
                // Configurar física e colisão
                MeshComp->SetMobility(EComponentMobility::Movable);
                MeshComp->SetCollisionProfileName(TEXT("BlockAll"));
                MeshComp->SetGenerateOverlapEvents(true);
                
                // Configurar escala aleatória para variedade
                float Scale = FMath::RandRange(0.8f, 1.2f);
                GardenActor->SetActorScale3D(FVector(Scale, Scale, Scale));
                
                // Adicionar dados para animação
                FStellarGardenData GardenData;
                GardenData.GardenActor = GardenActor;
                GardenData.Position = Position;
                GardenData.RotationSpeed = FMath::RandRange(0.05f, 0.15f);
                GardenData.PulsationAmplitude = FMath::RandRange(0.1f, 0.3f);
                GardenData.PulsationSpeed = FMath::RandRange(0.3f, 0.7f);
                GardenData.TimeOffset = FMath::RandRange(0.0f, 2.0f * PI);
                
                // Adicionar à lista de jardins estelares para animação
                StellarGardenData.Add(GardenData);
            }
            
            // Adicionar à lista de atores gerados
            GeneratedActors.Add(GardenActor);
        }
    }
    
    // Aplicar parâmetros ao PCG para efeitos adicionais
    SetPCGParameterModern(TEXT("GardenPositions"), MapCenter + FVector(0.0f, 0.0f, GardenHeight), TEXT("StellarGardens"));
    SetPCGParameterModern(TEXT("GardenCount"), FVector(GardenPositions.Num(), 0.0f, 0.0f), TEXT("StellarGardens"));
    SetPCGParameterModern(TEXT("GardenHeight"), FVector(GardenHeight, 0.0f, 0.0f), TEXT("StellarGardens"));
    SetPCGParameterModern(TEXT("PhaseIntensity"), FVector(PhaseIntensity, 0.0f, 0.0f), TEXT("StellarGardens"));
    SetPCGParameterModern(TEXT("GardenGlow"), GardenGlowColor.ToFVector(), TEXT("StellarGardens"));
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateStellarGardens - Generated %d stellar gardens using modern UE 5.6 APIs"), GardenPositions.Num());


    // Usar sistema moderno de geração procedural do UE 5.6
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;

    // Gerar padrão hexagonal para jardins usando algoritmo moderno
    TArray<FVector> GardenPositions = UAURACRONPCGMathLibrary::GenerateHexagonalGrid(
        MapCenter,
        3000.0f, // Raio de 30 metros
        800.0f, // Espaçamento de 8 metros
        true, // Adicionar offset aleatório
        0.3f // Quantidade de offset aleatório
    );

    // Aplicar parâmetros ao PCG usando APIs modernas
    SetPCGParameterModern(TEXT("GardenCenter"), MapCenter, TEXT("StellarGardens"));
    SetPCGParameterModern(TEXT("GardenCount"), FVector(GardenPositions.Num(), 0.0f, 0.0f), TEXT("StellarGardens"));
    SetPCGParameterModern(TEXT("GardenRadius"), FVector(3000.0f, 0.0f, 0.0f), TEXT("StellarGardens"));

    // Aplicar efeitos baseados no tempo usando APIs modernas
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float StellarPulse = (FMath::Sin(CurrentTime * 0.5f) + 1.0f) * 0.5f; // Normalizado entre 0 e 1
    float StellarGlow = 0.8f + 0.2f * FMath::Cos(CurrentTime * 0.3f);

    SetPCGParameterModern(TEXT("StellarPulse"), FVector(StellarPulse, 0.0f, 0.0f), TEXT("StellarGardens"));
    SetPCGParameterModern(TEXT("StellarGlow"), FVector(StellarGlow, 0.0f, 0.0f), TEXT("StellarGardens"));

    // Aplicar características baseadas na fase lunar
    float LunarIntensity = UAURACRONPCGMathLibrary::GetLunarPhaseIntensity(CurrentTime / 86400.0f); // Converter para dias
    SetPCGParameterModern(TEXT("LunarIntensity"), FVector(LunarIntensity, 0.0f, 0.0f), TEXT("StellarGardens"));

    // Executar geração usando API moderna do UE 5.6
    if (PCGComponent->GetGraph())
    {
        PCGComponent->GenerateLocal(true);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateStellarGardens - Generated %d stellar gardens using modern UE 5.6 APIs"), GardenPositions.Num());
}

void AAURACRONPCGEnvironment::GenerateSpectralPlains()
{
    // Gerar planícies espectrais usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::GenerateSpectralPlains - PCGComponent is invalid"));
        return;
    }

    // Usar sistema moderno de geração procedural do UE 5.6
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;

    // Configurar padrão de ruído para planícies espectrais
    FAURACRONNoisePattern SpectralNoise;
    SpectralNoise.Frequency = 0.01f; // Baixa frequência para planícies suaves
    SpectralNoise.Amplitude = 200.0f; // Amplitude de 2 metros
    SpectralNoise.Octaves = 4;
    SpectralNoise.Persistence = 0.6f;
    SpectralNoise.Lacunarity = 2.0f;
    SpectralNoise.Seed = FMath::Rand();

    // Gerar mapa de altura usando algoritmo moderno
    TArray<float> HeightMap = UAURACRONPCGMathLibrary::GenerateHeightMap(
        100, // Largura
        100, // Altura
        SpectralNoise,
        -100.0f, // Altura mínima
        300.0f // Altura máxima
    );

    // Aplicar parâmetros ao PCG usando APIs modernas
    SetPCGParameterModern(TEXT("PlainsCenter"), MapCenter, TEXT("SpectralPlains"));
    SetPCGParameterModern(TEXT("NoiseFrequency"), FVector(SpectralNoise.Frequency, 0.0f, 0.0f), TEXT("SpectralPlains"));
    SetPCGParameterModern(TEXT("NoiseAmplitude"), FVector(SpectralNoise.Amplitude, 0.0f, 0.0f), TEXT("SpectralPlains"));

    // Aplicar efeitos espectrais baseados no tempo
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float SpectralShift = FMath::Sin(CurrentTime * 0.2f) * 0.5f + 0.5f; // Normalizado entre 0 e 1
    float SpectralIntensity = 0.7f + 0.3f * FMath::Cos(CurrentTime * 0.4f);

    SetPCGParameterModern(TEXT("SpectralShift"), FVector(SpectralShift, 0.0f, 0.0f), TEXT("SpectralPlains"));
    SetPCGParameterModern(TEXT("SpectralIntensity"), FVector(SpectralIntensity, 0.0f, 0.0f), TEXT("SpectralPlains"));

    // Aplicar cores espectrais baseadas na fase do mapa
    if (UWorld* World = GetWorld())
    {
        if (UAURACRONPCGSubsystem* PCGSubsystem = World->GetSubsystem<UAURACRONPCGSubsystem>())
        {
            EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
            FLinearColor SpectralColor;

            switch (CurrentPhase)
            {
                case EAURACRONMapPhase::Awakening:
                    SpectralColor = FLinearColor(0.8f, 0.6f, 1.0f, 0.7f); // Roxo suave
                    break;
                case EAURACRONMapPhase::Convergence:
                    SpectralColor = FLinearColor(0.6f, 0.8f, 1.0f, 0.8f); // Azul claro
                    break;
                case EAURACRONMapPhase::Intensification:
                    SpectralColor = FLinearColor(1.0f, 0.6f, 0.8f, 0.7f); // Rosa
                    break;
                case EAURACRONMapPhase::Resolution:
                    SpectralColor = FLinearColor(0.4f, 0.4f, 1.0f, 0.9f); // Azul escuro
                    break;
            }

            SetPCGParameterModern(TEXT("SpectralColor"), FVector(SpectralColor.R, SpectralColor.G, SpectralColor.B), TEXT("SpectralPlains"));
        }
    }

    // Executar geração usando API moderna do UE 5.6
    if (PCGComponent->GetGraph())
    {
        PCGComponent->GenerateLocal(true);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateSpectralPlains - Generated spectral plains with %d height points using modern UE 5.6 APIs"), HeightMap.Num());
}

void AAURACRONPCGEnvironment::GenerateRiversOfSouls()
{
    // Gerar rios de almas usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::GenerateRiversOfSouls - PCGComponent is invalid"));
        return;
    }

    // Usar sistema moderno de geração procedural do UE 5.6
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    FVector RiverStart = MapCenter + FVector(-4000.0f, -2000.0f, 0.0f);
    FVector RiverEnd = MapCenter + FVector(4000.0f, 2000.0f, 0.0f);

    // Criar curva serpentina para o rio usando algoritmo moderno
    FAURACRONSplineCurve RiverCurve = UAURACRONPCGMathLibrary::CreateSerpentineCurve(
        RiverStart,
        RiverEnd,
        15, // Número de pontos de controle
        800.0f, // Amplitude das curvas (8 metros)
        1.5f // Frequência das curvas
    );

    // Distribuir pontos ao longo da curva
    TArray<FVector> RiverPoints = UAURACRONPCGMathLibrary::DistributePointsAlongCurve(
        RiverCurve,
        200.0f, // Espaçamento de 2 metros
        true // Alinhar com tangente
    );

    // Aplicar parâmetros ao PCG usando APIs modernas
    SetPCGParameterModern(TEXT("RiverStart"), RiverStart, TEXT("RiversOfSouls"));
    SetPCGParameterModern(TEXT("RiverEnd"), RiverEnd, TEXT("RiversOfSouls"));
    SetPCGParameterModern(TEXT("RiverPointCount"), FVector(RiverPoints.Num(), 0.0f, 0.0f), TEXT("RiversOfSouls"));

    // Aplicar efeitos de almas baseados no tempo
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float SoulFlow = FMath::Sin(CurrentTime * 0.8f) * 0.3f + 0.7f; // Fluxo entre 0.4 e 1.0
    float SoulDensity = 0.6f + 0.4f * FMath::Cos(CurrentTime * 0.6f);
    float SoulGlow = (FMath::Sin(CurrentTime * 1.2f) + 1.0f) * 0.5f; // Normalizado entre 0 e 1

    SetPCGParameterModern(TEXT("SoulFlow"), FVector(SoulFlow, 0.0f, 0.0f), TEXT("RiversOfSouls"));
    SetPCGParameterModern(TEXT("SoulDensity"), FVector(SoulDensity, 0.0f, 0.0f), TEXT("RiversOfSouls"));
    SetPCGParameterModern(TEXT("SoulGlow"), FVector(SoulGlow, 0.0f, 0.0f), TEXT("RiversOfSouls"));

    // Aplicar cor das almas baseada na intensidade solar
    float SolarIntensity = UAURACRONPCGMathLibrary::GetSolarIntensity(CurrentTime / 86400.0f);
    FLinearColor SoulColor = FLinearColor::LerpUsingHSV(
        FLinearColor(0.2f, 0.8f, 1.0f, 0.8f), // Azul claro durante o dia
        FLinearColor(0.8f, 0.2f, 1.0f, 0.9f), // Roxo durante a noite
        1.0f - SolarIntensity
    );

    SetPCGParameterModern(TEXT("SoulColor"), FVector(SoulColor.R, SoulColor.G, SoulColor.B), TEXT("RiversOfSouls"));

    // Executar geração usando API moderna do UE 5.6
    if (PCGComponent->GetGraph())
    {
        PCGComponent->GenerateLocal(true);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateRiversOfSouls - Generated river of souls with %d points using modern UE 5.6 APIs"), RiverPoints.Num());
}

void AAURACRONPCGEnvironment::GenerateFragmentedStructures()
{
    // Gerar estruturas fragmentadas usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::GenerateFragmentedStructures - PCGComponent is invalid"));
        return;
    }

    // Usar sistema moderno de geração procedural do UE 5.6
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;

    // Gerar posições fragmentadas usando amostragem de Poisson
    TArray<FVector> FragmentPositions = UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
        MapCenter,
        6000.0f, // Raio de 60 metros
        500.0f, // Distância mínima de 5 metros entre fragmentos
        40, // Máximo de tentativas
        FMath::Rand() // Seed aleatório
    );

    // Aplicar variação de altura para fragmentos flutuantes
    for (FVector& Position : FragmentPositions)
    {
        float HeightVariation = FMath::RandRange(100.0f, 1500.0f); // Entre 1 e 15 metros de altura
        Position.Z += HeightVariation;
    }

    // Aplicar parâmetros ao PCG usando APIs modernas
    SetPCGParameterModern(TEXT("FragmentCenter"), MapCenter, TEXT("FragmentedStructures"));
    SetPCGParameterModern(TEXT("FragmentCount"), FVector(FragmentPositions.Num(), 0.0f, 0.0f), TEXT("FragmentedStructures"));
    SetPCGParameterModern(TEXT("FragmentRadius"), FVector(6000.0f, 0.0f, 0.0f), TEXT("FragmentedStructures"));

    // Aplicar efeitos de fragmentação baseados no tempo
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float FragmentRotation = CurrentTime * 10.0f; // Rotação lenta
    float FragmentFloat = FMath::Sin(CurrentTime * 0.5f) * 50.0f; // Flutuação de 0.5 metros
    float FragmentGlow = 0.5f + 0.5f * FMath::Sin(CurrentTime * 0.7f);

    SetPCGParameterModern(TEXT("FragmentRotation"), FVector(FragmentRotation, 0.0f, 0.0f), TEXT("FragmentedStructures"));
    SetPCGParameterModern(TEXT("FragmentFloat"), FVector(FragmentFloat, 0.0f, 0.0f), TEXT("FragmentedStructures"));
    SetPCGParameterModern(TEXT("FragmentGlow"), FVector(FragmentGlow, 0.0f, 0.0f), TEXT("FragmentedStructures"));

    // Aplicar características baseadas na atividade do mapa
    float ActivityLevel = ActivityScale;
    float FragmentStability = 1.0f - (ActivityLevel * 0.3f); // Menos estabilidade com mais atividade
    float FragmentEnergy = ActivityLevel * 2.0f; // Mais energia com mais atividade

    SetPCGParameterModern(TEXT("FragmentStability"), FVector(FragmentStability, 0.0f, 0.0f), TEXT("FragmentedStructures"));
    SetPCGParameterModern(TEXT("FragmentEnergy"), FVector(FragmentEnergy, 0.0f, 0.0f), TEXT("FragmentedStructures"));

    // Executar geração usando API moderna do UE 5.6
    if (PCGComponent->GetGraph())
    {
        PCGComponent->GenerateLocal(true);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateFragmentedStructures - Generated %d fragmented structures using modern UE 5.6 APIs"), FragmentPositions.Num());
}

void AAURACRONPCGEnvironment::GenerateVoidRifts()
{
    // Gerar fendas do vazio usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::GenerateVoidRifts - PCGComponent is invalid"));
        return;
    }
    
    // Usar sistema moderno de geração procedural do UE 5.6
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;
    
    // Gerar posições para fendas do vazio usando distribuição aleatória controlada
    int32 NumVoidRifts = FMath::RandRange(FAURACRONMapDimensions::VOID_RIFTS_COUNT_MIN, 
                                         FAURACRONMapDimensions::VOID_RIFTS_COUNT_MAX);
    
    TArray<FVector> VoidRiftPositions = UAURACRONPCGMathLibrary::GeneratePoissonDiscSampling(
        MapCenter,
        5000.0f, // Raio de 50 metros
        800.0f,  // Distância mínima de 8 metros entre fendas
        30,      // Máximo de tentativas
        FMath::Rand() // Seed aleatório
    );
    
    // Limitar ao número desejado de fendas
    if (VoidRiftPositions.Num() > NumVoidRifts)
    {
        VoidRiftPositions.SetNum(NumVoidRifts);
    }
    
    // Aplicar parâmetros ao PCG usando APIs modernas
    SetPCGParameterModern(TEXT("VoidRiftCenter"), MapCenter, TEXT("VoidRifts"));
    SetPCGParameterModern(TEXT("VoidRiftCount"), FVector(VoidRiftPositions.Num(), 0.0f, 0.0f), TEXT("VoidRifts"));
    SetPCGParameterModern(TEXT("VoidRiftRadius"), FVector(5000.0f, 0.0f, 0.0f), TEXT("VoidRifts"));
    
    // Aplicar efeitos de distorção do vazio baseados no tempo
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float VoidPulse = FMath::Sin(CurrentTime * 0.4f) * 0.5f + 0.5f; // Normalizado entre 0 e 1
    float VoidIntensity = 0.7f + 0.3f * FMath::Cos(CurrentTime * 0.3f);
    float VoidDistortion = 1.0f + 0.8f * FMath::Sin(CurrentTime * 0.2f);
    
    SetPCGParameterModern(TEXT("VoidPulse"), FVector(VoidPulse, 0.0f, 0.0f), TEXT("VoidRifts"));
    SetPCGParameterModern(TEXT("VoidIntensity"), FVector(VoidIntensity, 0.0f, 0.0f), TEXT("VoidRifts"));
    SetPCGParameterModern(TEXT("VoidDistortion"), FVector(VoidDistortion, 0.0f, 0.0f), TEXT("VoidRifts"));
    
    // Aplicar cor do vazio baseada na atividade do ambiente
    float ActivityLevel = ActivityScale;
    FLinearColor VoidColor = FLinearColor::LerpUsingHSV(
        FLinearColor(0.1f, 0.0f, 0.3f, 0.8f), // Roxo escuro com baixa atividade
        FLinearColor(0.6f, 0.0f, 1.0f, 0.9f), // Roxo brilhante com alta atividade
        ActivityLevel
    );
    
    SetPCGParameterModern(TEXT("VoidColor"), FVector(VoidColor.R, VoidColor.G, VoidColor.B), TEXT("VoidRifts"));
    
    // Executar geração usando API moderna do UE 5.6
    if (PCGComponent->GetGraph())
    {
        PCGComponent->GenerateLocal(true);
    }
    
    // Criar sistema de partículas e portais de teletransporte para cada fenda
    for (const FVector& Position : VoidRiftPositions)
    {
        if (VoidRiftParticleSystem)
        {
            // Determinar tamanho aleatório para cada fenda
            float RiftRadius = FMath::RandRange(FAURACRONMapDimensions::VOID_RIFT_RADIUS_MIN_CM, 
                                              FAURACRONMapDimensions::VOID_RIFT_RADIUS_MAX_CM) / 100.0f; // Converter para metros
            
            UNiagaraComponent* VoidParticles = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                VoidRiftParticleSystem,
                Position,
                FRotator::ZeroRotator,
                FVector(RiftRadius, RiftRadius, RiftRadius), // Aplicar escala baseada no raio
                true,
                true,
                ENCPoolMethod::AutoRelease
            );
            
            if (VoidParticles)
            {
                // Configurar parâmetros do sistema de partículas
                VoidParticles->SetFloatParameter(TEXT("VoidIntensity"), VoidIntensity);
                VoidParticles->SetColorParameter(TEXT("VoidColor"), VoidColor);
                VoidParticles->SetFloatParameter(TEXT("VoidPulse"), VoidPulse);
                VoidParticles->SetFloatParameter(TEXT("VoidRadius"), RiftRadius);
                
                // Adicionar à lista de atores gerados para limpeza posterior
                GeneratedActors.Add(VoidParticles);
                
                // Criar portal de teletransporte para esta fenda
                CreateVoidRiftPortal(Position, RiftRadius, VoidColor);
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateVoidRifts - Generated %d void rifts using modern UE 5.6 APIs"), VoidRiftPositions.Num());
}

void AAURACRONPCGEnvironment::CreateVoidRiftPortal(const FVector& Position, float Radius, const FLinearColor& Color)
{
    // Verificar se o mundo é válido
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::CreateVoidRiftPortal - World is invalid"));
        return;
    }
    
    // Criar portal usando a classe AAURACRONPCGPortal
    FActorSpawnParameters SpawnParams;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    
    AAURACRONPCGPortal* Portal = World->SpawnActor<AAURACRONPCGPortal>(
        AAURACRONPCGPortal::StaticClass(), 
        Position, 
        FRotator::ZeroRotator, 
        SpawnParams
    );
    
    if (!Portal)
    {
        UE_LOG(LogTemp, Error, TEXT("AAURACRONPCGEnvironment::CreateVoidRiftPortal - Failed to spawn portal"));
        return;
    }
    
    // Obter um destino aleatório para o teletransporte
    // Idealmente, deveria ser outra fenda do vazio em uma plataforma distante
    // Para esta implementação, vamos usar uma posição aleatória elevada
    FVector RandomOffset = FVector(
        FMath::RandRange(-5000.0f, 5000.0f),
        FMath::RandRange(-5000.0f, 5000.0f),
        FMath::RandRange(1000.0f, 3000.0f) // Altura elevada para simular plataformas celestiais
    );
    
    FVector DestinationLocation = FAURACRONMapDimensions::MAP_CENTER + RandomOffset;
    FRotator DestinationRotation = FRotator::ZeroRotator;
    
    // Configurar o portal
    FAURACRONPortalSettings PortalSettings;
    PortalSettings.CurrentEnvironment = EAURACRONEnvironmentType::ZephyrFirmament;
    PortalSettings.DestinationLocation = DestinationLocation;
    PortalSettings.DestinationRotation = DestinationRotation;
    PortalSettings.PortalScale = Radius * 0.5f; // Escala proporcional ao raio da fenda
    PortalSettings.PortalColor = Color;
    PortalSettings.EffectIntensity = 1.5f; // Intensidade aumentada para fendas do vazio
    PortalSettings.ActivationRadius = Radius * 100.0f; // Raio de ativação em centímetros
    PortalSettings.bIsActive = true;
    PortalSettings.bIsVisible = true;
    
    // Inicializar o portal
    Portal->InitializePortal(PortalSettings);
    
    // Adicionar à lista de atores gerados para limpeza posterior
    GeneratedActors.Add(Portal);
    
    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::CreateVoidRiftPortal - Created void rift portal at %s with destination %s"),
           *Position.ToString(), *DestinationLocation.ToString());
}

void AAURACRONPCGEnvironment::GenerateTemporalDistortionZones()
{
    // Gerar zonas de distorção temporal usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::GenerateTemporalDistortionZones - PCGComponent is invalid"));
        return;
    }

    // Usar sistema moderno de geração procedural do UE 5.6
    FVector MapCenter = FAURACRONMapDimensions::MAP_CENTER;

    // Gerar posições para zonas de distorção usando padrão hexagonal
    TArray<FVector> DistortionZones = UAURACRONPCGMathLibrary::GenerateHexagonalGrid(
        MapCenter,
        4000.0f, // Raio de 40 metros
        1200.0f, // Espaçamento de 12 metros
        true, // Adicionar offset aleatório
        0.4f // Quantidade de offset aleatório
    );

    // Aplicar parâmetros ao PCG usando APIs modernas
    SetPCGParameterModern(TEXT("DistortionCenter"), MapCenter, TEXT("TemporalDistortionZones"));
    SetPCGParameterModern(TEXT("DistortionCount"), FVector(DistortionZones.Num(), 0.0f, 0.0f), TEXT("TemporalDistortionZones"));
    SetPCGParameterModern(TEXT("DistortionRadius"), FVector(4000.0f, 0.0f, 0.0f), TEXT("TemporalDistortionZones"));

    // Aplicar efeitos temporais baseados no tempo real
    float CurrentTime = GetWorld()->GetTimeSeconds();
    float TemporalWave = FMath::Sin(CurrentTime * 0.3f) * 0.5f + 0.5f; // Normalizado entre 0 e 1
    float TemporalIntensity = 0.8f + 0.2f * FMath::Cos(CurrentTime * 0.4f);
    float TemporalFrequency = 1.0f + 0.5f * FMath::Sin(CurrentTime * 0.2f);

    SetPCGParameterModern(TEXT("TemporalWave"), FVector(TemporalWave, 0.0f, 0.0f), TEXT("TemporalDistortionZones"));
    SetPCGParameterModern(TEXT("TemporalIntensity"), FVector(TemporalIntensity, 0.0f, 0.0f), TEXT("TemporalDistortionZones"));
    SetPCGParameterModern(TEXT("TemporalFrequency"), FVector(TemporalFrequency, 0.0f, 0.0f), TEXT("TemporalDistortionZones"));

    // Aplicar efeitos baseados na posição temporal usando APIs modernas
    float TimeOfDay = FMath::Fmod(CurrentTime / 86400.0f, 1.0f); // Normalizar para ciclo de 24h
    FVector TimeBasedPosition = UAURACRONPCGMathLibrary::GetTimeBasedPosition(
        MapCenter,
        TimeOfDay,
        300.0f, // Amplitude de 3 metros
        2.0f // Frequência
    );

    SetPCGParameterModern(TEXT("TimeBasedPosition"), TimeBasedPosition, TEXT("TemporalDistortionZones"));

    // Aplicar cor temporal baseada na fase lunar
    float LunarIntensity = UAURACRONPCGMathLibrary::GetLunarPhaseIntensity(TimeOfDay);
    FLinearColor TemporalColor = FLinearColor::LerpUsingHSV(
        FLinearColor(0.6f, 0.2f, 1.0f, 0.7f), // Roxo durante lua nova
        FLinearColor(1.0f, 0.8f, 0.2f, 0.8f), // Dourado durante lua cheia
        LunarIntensity
    );

    SetPCGParameterModern(TEXT("TemporalColor"), FVector(TemporalColor.R, TemporalColor.G, TemporalColor.B), TEXT("TemporalDistortionZones"));

    // Executar geração usando API moderna do UE 5.6
    if (PCGComponent->GetGraph())
    {
        PCGComponent->GenerateLocal(true);
    }

    UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::GenerateTemporalDistortionZones - Generated %d temporal distortion zones using modern UE 5.6 APIs"), DistortionZones.Num());
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES AUXILIARES - UE 5.6 APIS MODERNAS
// ========================================

void AAURACRONPCGEnvironment::ApplyQualitySettings()
{
    // Aplicar configurações de qualidade baseadas na performance usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        return;
    }

    // Obter configurações de qualidade do sistema moderno
    if (UWorld* World = GetWorld())
    {
        if (UAURACRONPCGSubsystem* PCGSubsystem = World->GetSubsystem<UAURACRONPCGSubsystem>())
        {
            // Obter nível de qualidade atual
            int32 QualityLevel = PCGSubsystem->GetCurrentQualityLevel();

            // Aplicar configurações baseadas na qualidade
            switch (QualityLevel)
            {
                case 0: // Baixa qualidade
                {
                    SetPCGParameterModern(TEXT("QualityLevel"), FVector(0.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("DetailLevel"), FVector(0.5f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("ParticleCount"), FVector(50.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    break;
                }

                case 1: // Qualidade média
                {
                    SetPCGParameterModern(TEXT("QualityLevel"), FVector(1.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("DetailLevel"), FVector(0.75f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("ParticleCount"), FVector(100.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    break;
                }

                case 2: // Alta qualidade
                {
                    SetPCGParameterModern(TEXT("QualityLevel"), FVector(2.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("DetailLevel"), FVector(1.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("ParticleCount"), FVector(200.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    break;
                }

                default: // Qualidade épica
                {
                    SetPCGParameterModern(TEXT("QualityLevel"), FVector(3.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("DetailLevel"), FVector(1.5f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    SetPCGParameterModern(TEXT("ParticleCount"), FVector(400.0f, 0.0f, 0.0f), TEXT("QualitySettings"));
                    break;
                }
            }

            // Aplicar configurações de LOD baseadas na distância
            float LODDistance = QualityLevel * 1000.0f + 2000.0f; // Entre 2km e 5km
            SetPCGParameterModern(TEXT("LODDistance"), FVector(LODDistance, 0.0f, 0.0f), TEXT("QualitySettings"));

            // Aplicar configurações de culling
            bool bEnableCulling = QualityLevel < 3; // Desabilitar culling apenas na qualidade épica
            SetPCGParameterModern(TEXT("EnableCulling"), FVector(bEnableCulling ? 1.0f : 0.0f, 0.0f, 0.0f), TEXT("QualitySettings"));

            UE_LOG(LogTemp, Log, TEXT("AAURACRONPCGEnvironment::ApplyQualitySettings - Applied quality level %d settings using modern UE 5.6 APIs"), QualityLevel);
        }
    }
}

void AAURACRONPCGEnvironment::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);
	
	// Replicar propriedades básicas do ambiente
	DOREPLIFETIME(AAURACRONPCGEnvironment, EnvironmentType);
	DOREPLIFETIME(AAURACRONPCGEnvironment, ActivityScale);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bIsActive);
	
	// Replicar propriedades específicas do Radiant Plains
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasCrystallinePlateaus);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasOrbitalArchipelagos);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasSpectralPlains);
	
	// Replicar propriedades específicas do Zephyr Firmament
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasAerialIslands);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasFloatingCrystals);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasWindCurrents);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasVoidRifts);
	
	// Replicar propriedades específicas do Purgatory Realm
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasAbyssalChasms);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasEtherealFog);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasTemporalDistortions);
	DOREPLIFETIME(AAURACRONPCGEnvironment, bHasShadowStructures);
}

FAURACRONEnvironmentConfig AAURACRONPCGEnvironment::GetEnvironmentConfiguration() const
{
    // Obter configuração do ambiente usando dados modernos do UE 5.6
    FAURACRONEnvironmentConfig Config;

    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
            Config.PrimaryColor = FLinearColor(1.0f, 0.9f, 0.7f, 1.0f); // Dourado radiante
            Config.LightIntensity = 1.4f;
            Config.MaterialRoughness = 0.25f;
            Config.ActivityScale = 1.3f;
            break;

        case EAURACRONEnvironmentType::ZephyrFirmament:
            Config.PrimaryColor = FLinearColor(0.7f, 0.9f, 1.0f, 1.0f); // Azul aéreo
            Config.LightIntensity = 1.2f;
            Config.MaterialRoughness = 0.15f;
            Config.ActivityScale = 1.1f;
            break;

        case EAURACRONEnvironmentType::PurgatoryRealm:
            Config.PrimaryColor = FLinearColor(0.9f, 0.3f, 0.3f, 1.0f); // Vermelho sombrio
            Config.LightIntensity = 0.7f;
            Config.MaterialRoughness = 0.8f;
            Config.ActivityScale = 1.8f;
            break;

        default:
            Config.PrimaryColor = FLinearColor::White;
            Config.LightIntensity = 1.0f;
            Config.MaterialRoughness = 0.5f;
            Config.ActivityScale = 1.0f;
            break;
    }

    // Aplicar modificações baseadas na escala de atividade atual
    Config.ActivityScale *= ActivityScale;
    Config.LightIntensity *= (1.0f + (ActivityScale - 1.0f) * 0.5f);

    return Config;
}

void AAURACRONPCGEnvironment::SetPCGParameterModern(const FString& ParameterName, const FVector& Value, const FString& Category)
{
    // Definir parâmetro PCG usando APIs modernas do UE 5.6
    if (!PCGComponent || !IsValid(PCGComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetPCGParameterModern - PCGComponent is invalid"));
        return;
    }

    // Usar sistema moderno de parâmetros do UE 5.6
    if (UPCGGraph* PCGGraph = PCGComponent->GetGraph())
    {
        // Usar API moderna para definir parâmetros via GraphInstance
        if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
        {
            // Criar um mapa de parâmetros se não existir
            static TMap<FString, FVector> ParameterCache;

            // Armazenar o parâmetro no cache
            FString FullParameterName = FString::Printf(TEXT("%s_%s"), *Category, *ParameterName);
            ParameterCache.Add(FullParameterName, Value);

            // Aplicar parâmetro usando sistema moderno do UE 5.6
            // Usar ParameterOverrides do GraphInstance
            FPCGOverrideInstancedPropertyBag& Overrides = GraphInstance->GetParameterOverrides();
            
            // Método 1: Parâmetros de transformação
            if (ParameterName.Contains(TEXT("Position")) || ParameterName.Contains(TEXT("Location")))
            {
                FTransform ParameterTransform = FTransform(FRotator::ZeroRotator, FVector(Value.X, Value.Y, Value.Z), FVector::OneVector);
                // Aplicar transformação usando PropertyBag
                Overrides.UpdatePropertyByName(FName(*FullParameterName), ParameterTransform);
            }

            // Método 2: Parâmetros escalares
            else if (ParameterName.Contains(TEXT("Scale")) || ParameterName.Contains(TEXT("Intensity")) || ParameterName.Contains(TEXT("Alpha")))
            {
                float ScalarValue = Value.X; // Usar apenas o componente X para valores escalares
                // Aplicar valor escalar usando PropertyBag
                Overrides.UpdatePropertyByName(FName(*FullParameterName), ScalarValue);
            }

            // Método 3: Parâmetros vetoriais
            else if (ParameterName.Contains(TEXT("Color")) || ParameterName.Contains(TEXT("Direction")) || ParameterName.Contains(TEXT("Offset")))
            {
                FVector VectorValue = Value;
                // Aplicar valor vetorial usando PropertyBag
                Overrides.UpdatePropertyByName(FName(*FullParameterName), VectorValue);
            }

            // Método 4: Parâmetros booleanos
            else if (ParameterName.Contains(TEXT("Enable")) || ParameterName.Contains(TEXT("Active")) || ParameterName.Contains(TEXT("Visible")))
            {
                bool BoolValue = Value.X > 0.5f; // Converter para booleano
                // Aplicar valor booleano usando PropertyBag
                Overrides.UpdatePropertyByName(FName(*FullParameterName), BoolValue);
            }

            // Forçar regeneração do PCG se necessário
            if (bIsActive && PCGComponent->GetGraph())
            {
                // Usar API moderna para regeneração incremental
                PCGComponent->GenerateLocal(false); // Regeneração não-bloqueante
            }

            UE_LOG(LogTemp, VeryVerbose, TEXT("AAURACRONPCGEnvironment::SetPCGParameterModern - Set parameter %s = (%.2f, %.2f, %.2f) in category %s"),
                   *ParameterName, Value.X, Value.Y, Value.Z, *Category);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetPCGParameterModern - GraphInstance not found"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("AAURACRONPCGEnvironment::SetPCGParameterModern - PCG Graph not found"));
    }
}

// Implementação da Âncora do Purgatório
void AAURACRONPCGEnvironment::GeneratePurgatoryAnchor()
{
    // Limpar dados existentes
    PurgatoryAnchorData = FPurgatoryAnchorData();
    
    if (!HasAuthority())
    {
        return;
    }
    
    // Verificar se temos um componente PCG válido
    if (!PCGComponent)
    {   
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGEnvironment: Componente PCG não encontrado para gerar Âncora do Purgatório"));
        return;
    }
    
    // Verificar se temos um mundo válido
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("AURACRONPCGEnvironment: Mundo não encontrado para gerar Âncora do Purgatório"));
        return;
    }
    
    // Verificar se estamos no ambiente correto
    if (EnvironmentType != EAURACRONEnvironmentType::PurgatoryRealm)
    {
        return;
    }
    
    // Calcular posição da Âncora do Purgatório
    // A Âncora deve estar em uma posição estratégica no Reino do Purgatório
    FVector AnchorPosition = FVector::ZeroVector;
    
    // Obter posição do objetivo do Purgatório do sistema de medições do mapa
    UAURACRONMapMeasurements* MapMeasurements = UAURACRONMapMeasurements::GetInstance(World);
    if (MapMeasurements)
    {
        AnchorPosition = MapMeasurements->GetPurgatoryAnchorPosition();
    }
    else
    {
        // Fallback: posicionar entre os Nexos de Sombra e as Torres de Lamentação
        if (ShadowNexusData.Num() > 0 && TowerOfLamentationData.Num() > 0)
        {
            FVector NexusCenter = FVector::ZeroVector;
            for (const FShadowNexusData& Nexus : ShadowNexusData)
            {
                NexusCenter += Nexus.Position;
            }
            NexusCenter /= ShadowNexusData.Num();
            
            FVector TowerCenter = FVector::ZeroVector;
            for (const FTowerOfLamentationData& Tower : TowerOfLamentationData)
            {
                TowerCenter += Tower.Position;
            }
            TowerCenter /= TowerOfLamentationData.Num();
            
            // Posicionar entre os dois centros com uma variação aleatória
            float BlendFactor = 0.5f + FMath::FRandRange(-0.15f, 0.15f);
            AnchorPosition = FMath::Lerp(NexusCenter, TowerCenter, BlendFactor);
            
            // Adicionar variação aleatória na posição
            AnchorPosition += FVector(
                FMath::FRandRange(-500.f, 500.f),
                FMath::FRandRange(-500.f, 500.f),
                0.f
            );
        }
        else
        {
            // Fallback para o centro do mapa
            AnchorPosition = FVector(0.f, 0.f, 0.f);
        }
    }
    
    // Ajustar altura com base na topografia
    float TerrainHeight = UAURACRONPCGMathLibrary::GetTerrainHeightAtLocation(World, AnchorPosition);
    AnchorPosition.Z = TerrainHeight + 150.f; // Flutuar acima do terreno
    
    // Configurar parâmetros da Âncora
    PurgatoryAnchorData.Position = AnchorPosition;
    PurgatoryAnchorData.Radius = 300.f + FMath::FRandRange(-50.f, 50.f);
    PurgatoryAnchorData.RotationSpeed = 15.f + FMath::FRandRange(-5.f, 5.f);
    PurgatoryAnchorData.PulsationAmplitude = 0.3f + FMath::FRandRange(-0.1f, 0.1f);
    PurgatoryAnchorData.PulsationSpeed = 1.2f + FMath::FRandRange(-0.2f, 0.2f);
    PurgatoryAnchorData.bIsActive = false; // Inicialmente inativo
    
    // Configurar parâmetros do PCG Graph
    if (UPCGGraphInstance* GraphInstance = PCGComponent->GetGraphInstance())
    {
        // Definir parâmetros para o PCG Graph
        GraphInstance->SetVectorParameter(TEXT("PurgatoryAnchorPosition"), PurgatoryAnchorData.Position);
        GraphInstance->SetFloatParameter(TEXT("PurgatoryAnchorRadius"), PurgatoryAnchorData.Radius);
    }
    
    // Criar ator da Âncora do Purgatório
    FActorSpawnParameters SpawnParams;
    SpawnParams.Owner = this;
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    
    AStaticMeshActor* AnchorActor = World->SpawnActor<AStaticMeshActor>(AnchorPosition, FRotator::ZeroRotator, SpawnParams);
    if (AnchorActor)
    {
        // Configurar o ator
        AnchorActor->SetMobility(EComponentMobility::Movable);
        AnchorActor->SetActorLabel(TEXT("PurgatoryAnchor"));
        
        // Configurar o mesh
        UStaticMeshComponent* MeshComp = AnchorActor->GetStaticMeshComponent();
        if (MeshComp)
        {
            // Definir o mesh e material para Purgatory Anchor
            // Usar mesh procedural baseado no tipo de anchor
            UStaticMesh* AnchorMesh = GetAnchorMeshForEnvironment(EAURACRONEnvironmentType::PurgatoryRealm);
            if (AnchorMesh)
            {
                MeshComp->SetStaticMesh(AnchorMesh);
                
                // Aplicar material específico do Purgatory Anchor
                UMaterialInterface* PurgatoryMaterial = GetAnchorMaterialForEnvironment(EAURACRONEnvironmentType::PurgatoryRealm);
                if (PurgatoryMaterial)
                {
                    MeshComp->SetMaterial(0, PurgatoryMaterial);
                }
            }
            else
            {
                // Fallback para mesh básico se não encontrar o específico
                static ConstructorHelpers::FObjectFinder<UStaticMesh> CubeMeshFinder(TEXT("/Engine/BasicShapes/Cube.Cube"));
                if (CubeMeshFinder.Succeeded())
                {
                    MeshComp->SetStaticMesh(CubeMeshFinder.Object);
                    MeshComp->SetWorldScale3D(FVector(2.0f, 2.0f, 3.0f)); // Formato mais apropriado para anchor
                }
            }
            
            // Criar material dinâmico
            static ConstructorHelpers::FObjectFinder<UMaterialInterface> AnchorMaterialFinder(TEXT("/Game/AURACRON/Materials/M_PurgatoryAnchor"));
            if (AnchorMaterialFinder.Succeeded())
            {
                UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(AnchorMaterialFinder.Object, this);
                if (DynamicMaterial)
                {
                    MeshComp->SetMaterial(0, DynamicMaterial);
                }
            }
            
            // Configurar escala
            float Scale = PurgatoryAnchorData.Radius / 50.f; // Ajustar com base no raio
            AnchorActor->SetActorScale3D(FVector(Scale, Scale, Scale));
            
            // Configurar colisão
            MeshComp->SetCollisionProfileName(TEXT("OverlapAll"));
            MeshComp->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
            
            // Inicialmente invisível
            MeshComp->SetVisibility(false);
        }
        
        // Armazenar referência ao ator
        PurgatoryAnchorData.AnchorActor = AnchorActor;
        GeneratedActors.Add(AnchorActor);
    }
    
    // Criar sistema de partículas
    if (PurgatoryAnchorParticleSystem)
    {
        UNiagaraComponent* NiagaraComp = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            World,
            PurgatoryAnchorParticleSystem,
            AnchorPosition,
            FRotator::ZeroRotator,
            FVector(1.f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
        
        if (NiagaraComp)
        {
            // Configurar parâmetros do sistema de partículas
            NiagaraComp->SetFloatParameter(TEXT("Radius"), PurgatoryAnchorData.Radius);
            NiagaraComp->SetFloatParameter(TEXT("PulsationSpeed"), PurgatoryAnchorData.PulsationSpeed);
            NiagaraComp->SetFloatParameter(TEXT("PulsationAmplitude"), PurgatoryAnchorData.PulsationAmplitude);
            
            // Inicialmente desativado
            NiagaraComp->SetVisibility(false);
            NiagaraComp->SetActive(false);
            
            // Armazenar referência ao componente
            PurgatoryAnchorData.AuraComponent = NiagaraComp;
        }
    }
}

void AAURACRONPCGEnvironment::UpdatePurgatoryAnchor(float CurrentTime)
{
    // Verificar se estamos no ambiente correto
    if (EnvironmentType != EAURACRONEnvironmentType::PurgatoryRealm)
    {
        return;
    }
    
    // Obter a fase atual do mapa
    UAURACRONPCGSubsystem* PCGSubsystem = GetWorld()->GetSubsystem<UAURACRONPCGSubsystem>();
    if (!PCGSubsystem)
    {
        return;
    }
    
    EAURACRONMapPhase CurrentPhase = PCGSubsystem->GetCurrentMapPhase();
    
    // Atualizar estado ativo com base na fase do mapa
    // A Âncora do Purgatório deve estar ativa apenas durante certas fases
    bool bShouldBeActive = (CurrentPhase == EAURACRONMapPhase::PrismalFlow || 
                           CurrentPhase == EAURACRONMapPhase::EldritchStorm);
    
    // Atualizar estado ativo
    if (PurgatoryAnchorData.bIsActive != bShouldBeActive)
    {
        PurgatoryAnchorData.bIsActive = bShouldBeActive;
        
        // Atualizar visibilidade e colisão
        if (PurgatoryAnchorData.AnchorActor && IsValid(PurgatoryAnchorData.AnchorActor))
        {
            UStaticMeshComponent* MeshComp = PurgatoryAnchorData.AnchorActor->GetStaticMeshComponent();
            if (MeshComp)
            {
                MeshComp->SetVisibility(bShouldBeActive);
                MeshComp->SetCollisionEnabled(bShouldBeActive ? ECollisionEnabled::QueryOnly : ECollisionEnabled::NoCollision);
            }
        }
        
        // Atualizar sistema de partículas
        if (PurgatoryAnchorData.AuraComponent && IsValid(PurgatoryAnchorData.AuraComponent))
        {
            PurgatoryAnchorData.AuraComponent->SetVisibility(bShouldBeActive);
            PurgatoryAnchorData.AuraComponent->SetActive(bShouldBeActive);
        }
    }
    
    // Se ativo, atualizar efeitos visuais
    if (PurgatoryAnchorData.bIsActive)
    {
        // Atualizar rotação
        if (PurgatoryAnchorData.AnchorActor && IsValid(PurgatoryAnchorData.AnchorActor))
        {
            FRotator CurrentRotation = PurgatoryAnchorData.AnchorActor->GetActorRotation();
            FRotator NewRotation = CurrentRotation + FRotator(0.f, PurgatoryAnchorData.RotationSpeed * GetWorld()->GetDeltaSeconds(), 0.f);
            PurgatoryAnchorData.AnchorActor->SetActorRotation(NewRotation);
            
            // Aplicar oscilação vertical
            FVector CurrentLocation = PurgatoryAnchorData.AnchorActor->GetActorLocation();
            float VerticalOffset = FMath::Sin(CurrentTime * PurgatoryAnchorData.PulsationSpeed) * 30.f;
            FVector NewLocation = FVector(CurrentLocation.X, CurrentLocation.Y, PurgatoryAnchorData.Position.Z + VerticalOffset);
            PurgatoryAnchorData.AnchorActor->SetActorLocation(NewLocation);
            
            // Atualizar sistema de partículas para acompanhar o ator
            if (PurgatoryAnchorData.AuraComponent && IsValid(PurgatoryAnchorData.AuraComponent))
            {
                PurgatoryAnchorData.AuraComponent->SetWorldLocation(NewLocation);
                
                // Atualizar parâmetros do sistema de partículas
                float PulseFactor = 1.0f + FMath::Sin(CurrentTime * PurgatoryAnchorData.PulsationSpeed) * PurgatoryAnchorData.PulsationAmplitude;
                PurgatoryAnchorData.AuraComponent->SetFloatParameter(TEXT("PulseFactor"), PulseFactor);
                
                // Variar cor ao longo do tempo
                float HueShift = FMath::Frac(CurrentTime * 0.05f);
                FLinearColor BaseColor = FLinearColor(0.8f, 0.2f, 0.6f); // Roxo-avermelhado base
                FLinearColor ShiftedColor = UAURACRONPCGMathLibrary::ShiftHue(BaseColor, HueShift);
                PurgatoryAnchorData.AuraComponent->SetColorParameter(TEXT("AuraColor"), ShiftedColor);
            }
            
            // Atualizar material dinâmico
            UStaticMeshComponent* MeshComp = PurgatoryAnchorData.AnchorActor->GetStaticMeshComponent();
            if (MeshComp)
            {
                UMaterialInstanceDynamic* DynamicMaterial = Cast<UMaterialInstanceDynamic>(MeshComp->GetMaterial(0));
                if (DynamicMaterial)
                {
                    // Pulsar brilho
                    float GlowIntensity = 2.0f + FMath::Sin(CurrentTime * PurgatoryAnchorData.PulsationSpeed) * 1.5f;
                    DynamicMaterial->SetScalarParameterValue(TEXT("GlowIntensity"), GlowIntensity);
                    
                    // Variar cor ao longo do tempo
                    float HueShift = FMath::Frac(CurrentTime * 0.03f);
                    FLinearColor BaseColor = FLinearColor(0.8f, 0.2f, 0.6f); // Roxo-avermelhado base
                    FLinearColor ShiftedColor = UAURACRONPCGMathLibrary::ShiftHue(BaseColor, HueShift);
                    DynamicMaterial->SetVectorParameterValue(TEXT("GlowColor"), ShiftedColor);
                }
            }
        }
    }
}

UStaticMesh* AAURACRONPCGEnvironment::GetAnchorMeshForEnvironment(EAURACRONEnvironmentType EnvironmentType) const
{
    // Retornar mesh específico baseado no tipo de ambiente
    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
        {
            static ConstructorHelpers::FObjectFinder<UStaticMesh> RadiantMeshFinder(TEXT("/Engine/BasicShapes/Cylinder.Cylinder"));
            return RadiantMeshFinder.Succeeded() ? RadiantMeshFinder.Object : nullptr;
        }
        case EAURACRONEnvironmentType::ZephyrFirmament:
        {
            static ConstructorHelpers::FObjectFinder<UStaticMesh> ZephyrMeshFinder(TEXT("/Engine/BasicShapes/Cone.Cone"));
            return ZephyrMeshFinder.Succeeded() ? ZephyrMeshFinder.Object : nullptr;
        }
        case EAURACRONEnvironmentType::PurgatoryRealm:
        {
            static ConstructorHelpers::FObjectFinder<UStaticMesh> PurgatoryMeshFinder(TEXT("/Engine/BasicShapes/Cube.Cube"));
            return PurgatoryMeshFinder.Succeeded() ? PurgatoryMeshFinder.Object : nullptr;
        }
        default:
            return nullptr;
    }
}

UMaterialInterface* AAURACRONPCGEnvironment::GetAnchorMaterialForEnvironment(EAURACRONEnvironmentType EnvironmentType) const
{
    // Retornar material específico baseado no tipo de ambiente
    switch (EnvironmentType)
    {
        case EAURACRONEnvironmentType::RadiantPlains:
        {
            static ConstructorHelpers::FObjectFinder<UMaterialInterface> RadiantMaterialFinder(TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
            return RadiantMaterialFinder.Succeeded() ? RadiantMaterialFinder.Object : nullptr;
        }
        case EAURACRONEnvironmentType::ZephyrFirmament:
        {
            static ConstructorHelpers::FObjectFinder<UMaterialInterface> ZephyrMaterialFinder(TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
            return ZephyrMaterialFinder.Succeeded() ? ZephyrMaterialFinder.Object : nullptr;
        }
        case EAURACRONEnvironmentType::PurgatoryRealm:
        {
            static ConstructorHelpers::FObjectFinder<UMaterialInterface> PurgatoryMaterialFinder(TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
            return PurgatoryMaterialFinder.Succeeded() ? PurgatoryMaterialFinder.Object : nullptr;
        }
        default:
            return nullptr;
    }
}
}
