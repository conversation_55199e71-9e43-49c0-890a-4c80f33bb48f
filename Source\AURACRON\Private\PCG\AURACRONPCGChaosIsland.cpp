// AURACRONPCGChaosIsland.cpp
// Implementação da classe AChaosIsland para o sistema Prismal Flow

#include "PCG/AURACRONPCGChaosIsland.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/SphereComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "GameFramework/Character.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/StaticMeshActor.h"
#include "Net/UnrealNetwork.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemInterface.h"
#include "GameplayEffect.h"

AChaosIsland::AChaosIsland()
{
    // Configuração padrão
    PrimaryActorTick.bCanEverTick = true;
    
    // Configurar componentes específicos da Chaos Island
    
    // Espiral central do caos
    ChaosSpire = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("ChaosSpire"));
    ChaosSpire->SetupAttachment(RootComponent);
    ChaosSpire->SetRelativeLocation(FVector(0.0f, 0.0f, 250.0f));
    ChaosSpire->SetRelativeScale3D(FVector(1.0f, 1.0f, 5.0f));
    ChaosSpire->SetCollisionProfileName(TEXT("BlockAll"));
    
    // Vórtices de energia
    for (int32 i = 0; i < 3; ++i)
    {
        FString ComponentName = FString::Printf(TEXT("EnergyVortex_%d"), i);
        UNiagaraComponent* EnergyVortex = CreateDefaultSubobject<UNiagaraComponent>(*ComponentName);
        EnergyVortex->SetupAttachment(RootComponent);
        
        // Posicionar em torno da espiral central
        float Angle = (float)i / 3.0f * 2.0f * PI;
        float Distance = 150.0f;
        FVector Position;
        Position.X = Distance * FMath::Cos(Angle);
        Position.Y = Distance * FMath::Sin(Angle);
        Position.Z = 100.0f + 50.0f * i; // Diferentes alturas
        
        EnergyVortex->SetRelativeLocation(Position);
        EnergyVortexes.Add(EnergyVortex);
    }
    
    // Runas antigas
    for (int32 i = 0; i < 5; ++i)
    {
        FString ComponentName = FString::Printf(TEXT("AncientRune_%d"), i);
        UStaticMeshComponent* AncientRune = CreateDefaultSubobject<UStaticMeshComponent>(*ComponentName);
        AncientRune->SetupAttachment(RootComponent);
        
        // Posicionar em um padrão circular
        float Angle = (float)i / 5.0f * 2.0f * PI;
        float Distance = 250.0f;
        FVector Position;
        Position.X = Distance * FMath::Cos(Angle);
        Position.Y = Distance * FMath::Sin(Angle);
        Position.Z = 50.0f;
        
        AncientRune->SetRelativeLocation(Position);
        AncientRune->SetRelativeScale3D(FVector(0.5f, 0.5f, 2.0f));
        AncientRune->SetRelativeRotation(FRotator(0.0f, Angle * 180.0f / PI, 0.0f));
        AncientRune->SetCollisionProfileName(TEXT("BlockAll"));
        
        AncientRunes.Add(AncientRune);
    }
    
    // Definir o tipo de ilha como Chaos
    IslandType = EPrismalFlowIslandType::Chaos;
}

void AChaosIsland::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    // Implementar efeitos caóticos
    if (bIsActive)
    {
        // Rotação da espiral central
        if (ChaosSpire)
        {
            FRotator CurrentRotation = ChaosSpire->GetRelativeRotation();
            CurrentRotation.Yaw += DeltaTime * 20.0f; // 20 graus por segundo
            CurrentRotation.Roll += DeltaTime * 5.0f; // Leve inclinação
            ChaosSpire->SetRelativeRotation(CurrentRotation);
        }
        
        // Pulsar vórtices de energia
        float Time = GetGameTimeSinceCreation();
        for (int32 i = 0; i < EnergyVortexes.Num(); ++i)
        {
            if (EnergyVortexes[i])
            {
                // Cada vórtice pulsa em uma frequência ligeiramente diferente
                float PulseValue = 0.5f + 0.5f * FMath::Sin(Time * (1.5f + 0.5f * i));
                EnergyVortexes[i]->SetFloatParameter(FName("Intensity"), PulseValue * 3.0f);
                
                // Movimento caótico dos vórtices
                float OffsetX = 20.0f * FMath::Sin(Time * (0.7f + 0.3f * i));
                float OffsetY = 20.0f * FMath::Cos(Time * (0.5f + 0.4f * i));
                float OffsetZ = 10.0f * FMath::Sin(Time * (0.3f + 0.2f * i));
                
                FVector BaseLocation = EnergyVortexes[i]->GetRelativeLocation();
                FVector NewLocation = BaseLocation + FVector(OffsetX, OffsetY, OffsetZ);
                EnergyVortexes[i]->SetRelativeLocation(NewLocation);
            }
        }
        
        // Brilho das runas antigas
        for (int32 i = 0; i < AncientRunes.Num(); ++i)
        {
            if (AncientRunes[i])
            {
                // Obter material dinâmico
                UMaterialInstanceDynamic* DynMaterial = Cast<UMaterialInstanceDynamic>(AncientRunes[i]->GetMaterial(0));
                if (!DynMaterial)
                {
                    DynMaterial = AncientRunes[i]->CreateAndSetMaterialInstanceDynamic(0);
                }
                
                if (DynMaterial)
                {
                    // Cada runa brilha em um padrão diferente
                    float GlowValue = 0.5f + 0.5f * FMath::Sin(Time * (0.2f + 0.1f * i));
                    DynMaterial->SetScalarParameterValue(FName("Glow"), GlowValue);
                }
            }
        }
    }
}

void AChaosIsland::ApplyIslandEffect(AActor* OverlappingActor)
{
    // Verificar se a ilha está ativa
    if (!bIsActive || !OverlappingActor)
    {
        return;
    }
    
    // Verificar se o ator é um personagem jogável
    ACharacter* Character = Cast<ACharacter>(OverlappingActor);
    if (!Character)
    {
        return;
    }
    
    // Aplicar efeito visual de feedback
    for (UNiagaraComponent* Vortex : EnergyVortexes)
    {
        if (Vortex)
        {
            Vortex->SetFloatParameter(FName("EffectIntensity"), 5.0f); // Intensificar efeito
        }
    }
    
    // Retornar à intensidade normal após um curto período
    FTimerHandle TimerHandle;
    GetWorldTimerManager().SetTimer(TimerHandle, [this]()
    {
        for (UNiagaraComponent* Vortex : EnergyVortexes)
        {
            if (Vortex)
            {
                Vortex->SetFloatParameter(FName("EffectIntensity"), 1.0f);
            }
        }
    }, 0.5f, false);
    
    // Aplicar efeito caótico ao jogador
    ApplyChaosEffect(OverlappingActor);
    
    // Agendar remoção do efeito após um tempo
    FTimerHandle RemoveEffectTimerHandle;
    GetWorldTimerManager().SetTimer(RemoveEffectTimerHandle, FTimerDelegate::CreateUObject(this, &AChaosIsland::RemoveChaosEffects, OverlappingActor), 10.0f, false);
}

void AChaosIsland::ApplyChaosEffect(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        return;
    }
    
    // Aplicar efeito caótico
    FGameplayEffectContextHandle EffectContext = AbilityComponent->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // Criar GameplayEffect específico para efeito caótico
    UGameplayEffect* ChaosEffect = NewObject<UGameplayEffect>(this, FName("GE_ChaosIslandEffect"));
    ChaosEffect->DurationPolicy = EGameplayEffectDurationType::HasDuration;
    ChaosEffect->DurationMagnitude = FScalableFloat(15.0f); // 15 segundos
    
    // Modificador para velocidade de movimento aleatória (50% - 150%)
    FGameplayModifierInfo SpeedModifier;
    SpeedModifier.ModifierMagnitude = FScalableFloat(FMath::RandRange(0.5f, 1.5f));
    SpeedModifier.ModifierOp = EGameplayModOp::Multiplicative;
    SpeedModifier.Attribute = FGameplayAttribute(); // Atributo de velocidade
    ChaosEffect->Modifiers.Add(SpeedModifier);
    
    // Modificador para dano crítico aleatório (75% - 200%)
    FGameplayModifierInfo CritModifier;
    CritModifier.ModifierMagnitude = FScalableFloat(FMath::RandRange(0.75f, 2.0f));
    CritModifier.ModifierOp = EGameplayModOp::Multiplicative;
    CritModifier.Attribute = FGameplayAttribute(); // Atributo de dano crítico
    ChaosEffect->Modifiers.Add(CritModifier);
    
    // Modificador para resistência caótica (0% - 50%)
    FGameplayModifierInfo ResistanceModifier;
    ResistanceModifier.ModifierMagnitude = FScalableFloat(FMath::RandRange(0.0f, 0.5f));
    ResistanceModifier.ModifierOp = EGameplayModOp::Additive;
    ResistanceModifier.Attribute = FGameplayAttribute(); // Atributo de resistência
    ChaosEffect->Modifiers.Add(ResistanceModifier);
    
    // Aplicar o efeito caótico
    FGameplayEffectSpecHandle SpecHandle = AbilityComponent->MakeOutgoingSpec(ChaosEffect->GetClass(), 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        FActiveGameplayEffectHandle ActiveEffect = AbilityComponent->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        
        if (ActiveEffect.IsValid())
        {
            UE_LOG(LogTemp, Log, TEXT("Chaos Island: Efeito caótico aplicado em %s (Velocidade: %.0f%%, Crítico: %.0f%%, Resistência: +%.0f%%)"), 
                *TargetActor->GetName(), SpeedModifier.ModifierMagnitude.Value * 100, CritModifier.ModifierMagnitude.Value * 100, ResistanceModifier.ModifierMagnitude.Value * 100);
        }
    }
    
    // Criar feedback visual adicional
    UNiagaraSystem* ChaosVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_ChaosIslandEffect"));
    if (ChaosVFX)
    {
        UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            GetWorld(),
            ChaosVFX,
            TargetActor->GetActorLocation(),
            FRotator::ZeroRotator,
            FVector(1.0f),
            true,
            true,
            ENCPoolMethod::AutoRelease
        );
    }
}

void AChaosIsland::RemoveChaosEffects(AActor* TargetActor)
{
    // Verificar se o ator é válido
    if (!TargetActor)
    {
        return;
    }
    
    // Verificar se o ator implementa a interface do sistema de habilidades
    IAbilitySystemInterface* AbilityInterface = Cast<IAbilitySystemInterface>(TargetActor);
    if (!AbilityInterface)
    {
        return;
    }
    
    // Obter o componente do sistema de habilidades
    UAbilitySystemComponent* AbilityComponent = AbilityInterface->GetAbilitySystemComponent();
    if (!AbilityComponent)
    {
        return;
    }
    
    // Remover efeitos caóticos por tag
    FGameplayTagContainer ChaosEffectTags;
    ChaosEffectTags.AddTag(FGameplayTag::RequestGameplayTag(FName("Effect.ChaosIsland")));
    
    // Remover todos os efeitos com a tag de caos
    int32 RemovedEffects = AbilityComponent->RemoveActiveEffectsWithTags(ChaosEffectTags);
    
    if (RemovedEffects > 0)
    {
        UE_LOG(LogTemp, Log, TEXT("Chaos Island: %d efeito(s) caótico(s) removido(s) de %s"), RemovedEffects, *TargetActor->GetName());
        
        // Criar feedback visual de remoção
        UNiagaraSystem* RemovalVFX = LoadObject<UNiagaraSystem>(nullptr, TEXT("/Game/VFX/Islands/NS_ChaosIslandRemoval"));
        if (RemovalVFX)
        {
            UNiagaraFunctionLibrary::SpawnSystemAtLocation(
                GetWorld(),
                RemovalVFX,
                TargetActor->GetActorLocation(),
                FRotator::ZeroRotator,
                FVector(0.8f),
                true,
                true,
                ENCPoolMethod::AutoRelease
            );
        }
    }
    else
    {
        UE_LOG(LogTemp, Verbose, TEXT("Chaos Island: Nenhum efeito caótico encontrado para remover de %s"), *TargetActor->GetName());
    }
}